import type { IUser } from './user'
import type { IInstrumentSelectItem } from './instrument'

// #region Enums

// 交易请求类型枚举
export type TradeRequestType = 'PointPrice' | 'BasisWash'

// 交易请求状态枚举 (V4 - 简化状态机)
export type TradeRequestStatus =
  | 'Executing'
  | 'Completed'
  | 'Rejected'
  | 'Cancelled'
  | 'Expired'

// 执行模式枚举
export type ExecutionMode = 'AUTOMATIC' | 'MANUAL' | 'SIMULATED'

// #endregion

// #region Base Interfaces

// 前向声明以避免循环依赖
interface IExecutionDetailBase {
  ID: number
  tradeRequestID: number
  contractID: number
  executedQuantity: number
  executedPrice: number
  executionType: string
  status: string
  remarks?: string
}

// 交易请求基础接口 (对齐 Go: model/dianjia/trade_request.go)
export interface ITradeRequest {
  // 基础字段
  ID: number
  CreatedAt: string
  UpdatedAt: string
  DeletedAt?: string

  // 用户信息
  pricerID: number
  pricer?: IUser
  setterID: number
  setter?: IUser

  // 合约信息
  instrumentRefID: number
  instrument?: IInstrumentSelectItem

  // 交易请求信息
  requestType: TradeRequestType
  requestedQuantity: number
  requestedPrice: number // 点价时为价格，洗基差时为0
  executedQuantity: number
  executedPrice: number
  status: TradeRequestStatus

  // V4 新增字段
  executionMode: ExecutionMode
  rejectionReason?: string
  expiresAt: string
  notes?: string

  // 关联表
  executionDetails?: IExecutionDetailBase[]
}

// #endregion

// #region API Request & Response Interfaces

// 创建交易请求 (POST /dianjia/traderequests)
export interface ICreateTradeRequestRequest {
  setterID: number
  instrumentRefID: number
  requestType: TradeRequestType
  requestedQuantity: number
  requestedPrice?: number // 点价时必填
  executionMode: ExecutionMode
  expiresAt: string // ISO 8601 格式
}

// 点价方交易请求列表查询 (GET /dianjia/traderequests/as-pricer)
export interface ITradeRequestForPricerRequest {
  page?: number
  pageSize?: number
  instrumentRefID?: number
  requestType?: TradeRequestType
  status?: TradeRequestStatus
  startDate?: string
  endDate?: string
}

// 被点价方交易请求列表查询 (GET /dianjia/traderequests/as-setter)
export interface ITradeRequestForSetterRequest {
  page?: number
  pageSize?: number
  instrumentRefID?: number
  requestType?: TradeRequestType
  status?: TradeRequestStatus
  startDate?: string
  endDate?: string
}

// 人工反馈请求结构 (POST /dianjia/traderequests/:id/feedback)
export interface IManualFeedbackRequest {
  quantity: number
  price: number
  notes?: string
}

// 拒绝交易请求 (POST /dianjia/traderequests/:id/reject)
export interface IRejectTradeRequestRequest {
  reason: string
}

// 通用分页响应结构
export interface IPageResult<T> {
  list: T[]
  total: number
  page: number
  pageSize: number
}

// #endregion

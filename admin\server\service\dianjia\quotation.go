package dianjia

import (
	"errors"
	"strings"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/dianjia"
	"gorm.io/gorm"
)

type QuotationService struct{}

// CreateOrUpdateQuotation 创建或更新报价
func (quotationService *QuotationService) CreateOrUpdateQuotation(req dianjia.CreateQuotationRequest, userID uint) (*dianjia.QuotationResponse, error) {
	// 验证商品是否存在
	var commodity dianjia.Commodity
	err := global.GVA_DB.First(&commodity, req.CommodityID).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("指定的商品不存在")
		}
		return nil, err
	}

	// 如果是基差报价，验证期货合约是否存在
	if req.PriceType == dianjia.QuotationPriceTypeBasis {
		if req.InstrumentRefID == nil {
			return nil, errors.New("基差报价必须指定关联的期货合约")
		}
		var instrument dianjia.Instrument
		err = global.GVA_DB.First(&instrument, *req.InstrumentRefID).Error
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, errors.New("指定的期货合约不存在")
			}
			return nil, err
		}
	}

	// 验证过期时间必须是未来时间
	if req.ExpiresAt.Before(time.Now()) {
		return nil, errors.New("过期时间必须是未来时间")
	}

	// 创建报价
	quotation := dianjia.Quotation{
		UserID:           userID,
		Title:            req.Title,
		CommodityID:      req.CommodityID,
		DeliveryLocation: req.DeliveryLocation,
		Brand:            req.Brand,
		Specifications:   req.Specifications,
		Description:      req.Description,
		PriceType:        req.PriceType,
		Price:            req.Price,
		InstrumentRefID:  req.InstrumentRefID,
		ExpiresAt:        req.ExpiresAt,
		Status:           req.Status,
	}

	// 如果状态为空，默认设为Draft
	if quotation.Status == "" {
		quotation.Status = dianjia.QuotationStatusDraft
	}

	err = global.GVA_DB.Create(&quotation).Error
	if err != nil {
		return nil, err
	}

	// 返回完整的报价信息
	return quotationService.GetQuotationDetail(quotation.ID)
}

// UpdateQuotation 更新报价
func (quotationService *QuotationService) UpdateQuotation(req dianjia.UpdateQuotationRequest, userID uint) (*dianjia.QuotationResponse, error) {
	var quotation dianjia.Quotation
	err := global.GVA_DB.Where("id = ? AND user_id = ?", req.ID, userID).First(&quotation).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("报价不存在或无权限")
		}
		return nil, err
	}

	// 检查报价状态：只有Draft状态的报价可以更新
	if quotation.Status != dianjia.QuotationStatusDraft {
		return nil, errors.New("只有草稿状态的报价可以更新")
	}

	// 验证商品是否存在
	var commodity dianjia.Commodity
	err = global.GVA_DB.First(&commodity, req.CommodityID).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("指定的商品不存在")
		}
		return nil, err
	}

	// 如果是基差报价，验证期货合约是否存在
	if req.PriceType == dianjia.QuotationPriceTypeBasis {
		if req.InstrumentRefID == nil {
			return nil, errors.New("基差报价必须指定关联的期货合约")
		}
		var instrument dianjia.Instrument
		err = global.GVA_DB.First(&instrument, *req.InstrumentRefID).Error
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, errors.New("指定的期货合约不存在")
			}
			return nil, err
		}
	}

	// 验证过期时间必须是未来时间
	if req.ExpiresAt.Before(time.Now()) {
		return nil, errors.New("过期时间必须是未来时间")
	}

	// 更新报价信息
	updates := map[string]interface{}{
		"title":             req.Title,
		"commodity_id":      req.CommodityID,
		"delivery_location": req.DeliveryLocation,
		"brand":             req.Brand,
		"specifications":    req.Specifications,
		"description":       req.Description,
		"price_type":        req.PriceType,
		"price":             req.Price,
		"instrument_ref_id": req.InstrumentRefID,
		"expires_at":        req.ExpiresAt,
	}

	err = global.GVA_DB.Model(&quotation).Updates(updates).Error
	if err != nil {
		return nil, err
	}

	// 返回更新后的完整报价信息
	return quotationService.GetQuotationDetail(quotation.ID)
}

// PublishQuotation 发布报价（将Draft状态改为Active，并设置过期时间）
func (quotationService *QuotationService) PublishQuotation(quotationID uint, userID uint, expiresAt time.Time) error {
	var quotation dianjia.Quotation
	err := global.GVA_DB.Where("id = ? AND user_id = ?", quotationID, userID).First(&quotation).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("报价不存在或无权限")
		}
		return err
	}

	// 检查报价状态：只有Draft状态的报价可以发布
	if quotation.Status != dianjia.QuotationStatusDraft {
		return errors.New("只有草稿状态的报价可以发布")
	}

	// 验证新的过期时间必须是未来时间
	if expiresAt.Before(time.Now()) {
		return errors.New("过期时间必须是未来时间")
	}

	// 更新状态为Active并设置新的过期时间
	quotation.Status = dianjia.QuotationStatusActive
	quotation.ExpiresAt = expiresAt
	return global.GVA_DB.Save(&quotation).Error
}

// ToggleQuotationStatus 切换报价状态（Active <-> Draft）
func (quotationService *QuotationService) ToggleQuotationStatus(quotationID uint, userID uint) error {
	var quotation dianjia.Quotation
	err := global.GVA_DB.Where("id = ? AND user_id = ?", quotationID, userID).First(&quotation).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("报价不存在或无权限")
		}
		return err
	}

	// 根据当前状态切换
	if quotation.Status == dianjia.QuotationStatusActive {
		// Active -> Draft
		quotation.Status = dianjia.QuotationStatusDraft
	} else if quotation.Status == dianjia.QuotationStatusDraft {
		// Draft -> Active (需要检查过期时间)
		if quotation.ExpiresAt.Before(time.Now()) {
			return errors.New("报价已过期，无法激活。请重新设置过期时间")
		}
		quotation.Status = dianjia.QuotationStatusActive
	} else {
		return errors.New("无效的报价状态")
	}

	return global.GVA_DB.Save(&quotation).Error
}

// DeleteQuotation 删除报价（只能删除Draft状态）
func (quotationService *QuotationService) DeleteQuotation(quotationID uint, userID uint) error {
	var quotation dianjia.Quotation
	err := global.GVA_DB.Where("id = ? AND user_id = ?", quotationID, userID).First(&quotation).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("报价不存在或无权限")
		}
		return err
	}

	// 检查报价状态：只有Draft状态的报价可以删除
	if quotation.Status != dianjia.QuotationStatusDraft {
		return errors.New("只有草稿状态的报价可以删除")
	}

	return global.GVA_DB.Delete(&quotation).Error
}

// GetMyQuotationList 获取我的报价列表
func (quotationService *QuotationService) GetMyQuotationList(req dianjia.MyQuotationListRequest, userID uint) (*dianjia.QuotationListResponse, error) {
	var quotations []dianjia.Quotation
	var total int64

	query := global.GVA_DB.Model(&dianjia.Quotation{}).Where("user_id = ?", userID)

	// 根据filter过滤
	if req.Filter == "valid" {
		// 有效报价：Active状态
		query = query.Where("status = ?", dianjia.QuotationStatusActive)
	} else if req.Filter == "invalid" {
		// 无效报价：Draft状态
		query = query.Where("status = ?", dianjia.QuotationStatusDraft)
	}

	// 具体状态过滤
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// 计算总数
	query.Count(&total)

	// 分页
	if req.Page > 0 && req.PageSize > 0 {
		offset := (req.Page - 1) * req.PageSize
		query = query.Offset(offset).Limit(req.PageSize)
	}

	// 预加载关联数据
	err := query.Preload("User").Preload("Commodity").Preload("InstrumentRef").
		Order("created_at DESC").Find(&quotations).Error
	if err != nil {
		return nil, err
	}

	// 转换为响应格式
	responses := make([]dianjia.QuotationResponse, 0)
	for _, quotation := range quotations {
		response := quotationService.buildQuotationResponse(quotation)
		responses = append(responses, response)
	}

	return &dianjia.QuotationListResponse{
		List:     responses,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}

// GetPublicQuotationList 获取公开报价列表（只返回Active状态）
func (quotationService *QuotationService) GetPublicQuotationList(req dianjia.QuotationListRequest) (*dianjia.QuotationListResponse, error) {
	var quotations []dianjia.Quotation
	var total int64

	// 只查询Active状态的报价
	query := global.GVA_DB.Model(&dianjia.Quotation{}).Where("status = ?", dianjia.QuotationStatusActive)

	// 商品种类筛选
	if req.CommodityID != nil {
		query = query.Where("commodity_id = ?", *req.CommodityID)
	}

	// 价格类型筛选
	if req.PriceType != "" {
		query = query.Where("price_type = ?", req.PriceType)
	}

	// 关键词搜索（标题、品牌）
	if req.Keyword != "" {
		keyword := "%" + strings.TrimSpace(req.Keyword) + "%"
		query = query.Where("title LIKE ? OR brand LIKE ?", keyword, keyword)
	}

	// 计算总数
	query.Count(&total)

	// 分页
	if req.Page > 0 && req.PageSize > 0 {
		offset := (req.Page - 1) * req.PageSize
		query = query.Offset(offset).Limit(req.PageSize)
	}

	// 预加载关联数据
	err := query.Preload("User").Preload("Commodity").Preload("InstrumentRef").
		Order("created_at DESC").Find(&quotations).Error
	if err != nil {
		return nil, err
	}

	// 转换为响应格式
	responses := make([]dianjia.QuotationResponse, 0)
	for _, quotation := range quotations {
		response := quotationService.buildQuotationResponse(quotation)
		responses = append(responses, response)
	}

	return &dianjia.QuotationListResponse{
		List:     responses,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}

// GetQuotationDetail 获取报价详情
func (quotationService *QuotationService) GetQuotationDetail(quotationID uint) (*dianjia.QuotationResponse, error) {
	var quotation dianjia.Quotation

	err := global.GVA_DB.Where("id = ?", quotationID).
		Preload("User").
		Preload("Commodity").
		Preload("InstrumentRef").
		First(&quotation).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("报价不存在")
		}
		return nil, err
	}

	response := quotationService.buildQuotationResponse(quotation)
	return &response, nil
}

// GetQuotationDetailWithPermission 获取报价详情（带权限验证，仅供报价发布者查看非Active状态）
func (quotationService *QuotationService) GetQuotationDetailWithPermission(quotationID uint, userID uint) (*dianjia.QuotationResponse, error) {
	var quotation dianjia.Quotation

	err := global.GVA_DB.Where("id = ?", quotationID).
		Preload("User").
		Preload("Commodity").
		Preload("InstrumentRef").
		First(&quotation).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("报价不存在")
		}
		return nil, err
	}

	// 如果报价是Active状态，任何人都可以查看
	if quotation.Status == dianjia.QuotationStatusActive {
		response := quotationService.buildQuotationResponse(quotation)
		return &response, nil
	}

	// 非Active状态只有发布者可以查看
	if quotation.UserID != userID {
		return nil, errors.New("无权限查看该报价详情")
	}

	response := quotationService.buildQuotationResponse(quotation)
	return &response, nil
}

// ExpireQuotations 定时任务：将过期的激活报价状态更新为Draft
func (quotationService *QuotationService) ExpireQuotations() (int, error) {
	result := global.GVA_DB.Model(&dianjia.Quotation{}).
		Where("status = ? AND expires_at < ?", dianjia.QuotationStatusActive, time.Now()).
		Update("status", dianjia.QuotationStatusDraft)

	if result.Error != nil {
		return 0, result.Error
	}

	return int(result.RowsAffected), nil
}

// buildQuotationResponse 构建报价响应对象
func (quotationService *QuotationService) buildQuotationResponse(quotation dianjia.Quotation) dianjia.QuotationResponse {
	now := time.Now()
	isExpired := quotation.ExpiresAt.Before(now)
	remainingHours := 0
	if !isExpired {
		remainingHours = int(quotation.ExpiresAt.Sub(now).Hours())
	}



	return dianjia.QuotationResponse{
		ID:               quotation.ID,
		UserID:           quotation.UserID,
		User:             quotation.User,
		Title:            quotation.Title,
		CommodityID:      quotation.CommodityID,
		Commodity:        quotation.Commodity,
		DeliveryLocation: quotation.DeliveryLocation,
		Brand:            quotation.Brand,
		Specifications:   quotation.Specifications,
		Description:      quotation.Description,
		PriceType:        quotation.PriceType,
		Price:            quotation.Price,
		InstrumentRefID:  quotation.InstrumentRefID,
		InstrumentRef:    quotation.InstrumentRef,
		ExpiresAt:        quotation.ExpiresAt,
		Status:           quotation.Status,
		CreatedAt:        quotation.CreatedAt,
		UpdatedAt:        quotation.UpdatedAt,
		IsExpired:        isExpired,
		RemainingHours:   remainingHours,
	}
}

"""
认证模块事件常量定义

定义了认证相关的所有事件类型，用于事件驱动的状态通知和组件间通信。
这些事件通过vnpy的EventEngine进行传递。
"""

# 用户登录相关事件
EVENT_USER_LOGIN_START = "user.login.start"
"""用户开始登录事件
数据: LoginCredentials
"""

EVENT_USER_LOGIN_SUCCESS = "user.login.success"
"""用户登录成功事件
数据: TokenData
"""

EVENT_USER_LOGIN_FAILED = "user.login.failed"
"""用户登录失败事件
数据: LoginResult
"""

EVENT_USER_LOGOUT = "user.logout"
"""用户登出事件
数据: None
"""

# Token相关事件
EVENT_TOKEN_REFRESHED = "auth.token.refreshed"
"""Token刷新成功事件
数据: TokenData
"""

EVENT_TOKEN_EXPIRED = "auth.token.expired"
"""Token过期事件
数据: TokenData
"""

EVENT_TOKEN_REFRESH_FAILED = "auth.token.refresh.failed"
"""Token刷新失败事件
数据: str (错误消息)
"""

# 认证状态相关事件
EVENT_AUTH_STATUS_CHANGED = "auth.status.changed"
"""认证状态变化事件
数据: dict {"status": str, "user_info": dict}
"""


# 短信验证码相关事件
EVENT_SMS_CODE_SENT = "sms.code.sent"
"""短信验证码发送成功事件
数据: str (手机号)
"""

EVENT_SMS_CODE_SEND_FAILED = "sms.code.send.failed"
"""短信验证码发送失败事件
数据: dict {"phone": str, "error": str}
"""

EVENT_SMS_CODE_COUNTDOWN_START = "sms.code.countdown.start"
"""短信验证码倒计时开始事件
数据: int (倒计时秒数)
"""

EVENT_SMS_CODE_COUNTDOWN_UPDATE = "sms.code.countdown.update"
"""短信验证码倒计时更新事件
数据: int (剩余秒数)
"""

EVENT_SMS_CODE_COUNTDOWN_END = "sms.code.countdown.end"
"""短信验证码倒计时结束事件
数据: None
"""

# 网络连接相关事件
EVENT_NETWORK_CONNECTED = "network.connected"
"""网络连接成功事件
数据: None
"""

EVENT_NETWORK_DISCONNECTED = "network.disconnected"
"""网络连接断开事件
数据: str (断开原因)
"""

EVENT_NETWORK_ERROR = "network.error"
"""网络错误事件
数据: str (错误消息)
"""

# 配置相关事件
EVENT_CONFIG_LOADED = "config.loaded"
"""配置加载完成事件
数据: dict (配置数据)
"""

EVENT_CONFIG_CHANGED = "config.changed"
"""配置变更事件
数据: dict {"key": str, "old_value": Any, "new_value": Any}
"""

# 错误处理相关事件
EVENT_ERROR_OCCURRED = "error.occurred"
"""错误发生事件
数据: dict {"error_type": str, "error_message": str, "error_code": str}
"""

EVENT_WARNING_OCCURRED = "warning.occurred"
"""警告发生事件
数据: dict {"warning_type": str, "warning_message": str}
"""

# 事件数据结构定义
class EventData:
    """事件数据基类"""
    
    def __init__(self, event_type: str, data=None, timestamp=None):
        self.event_type = event_type
        self.data = data
        self.timestamp = timestamp or self._get_current_timestamp()
    
    def _get_current_timestamp(self):
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now()
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            "event_type": self.event_type,
            "data": self.data,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None
        }


class LoginEventData(EventData):
    """登录事件数据"""
    
    def __init__(self, event_type: str, login_result=None, credentials=None):
        super().__init__(event_type)
        self.login_result = login_result
        self.credentials = credentials


class TokenEventData(EventData):
    """Token事件数据"""
    
    def __init__(self, event_type: str, token_data=None, error_message=None):
        super().__init__(event_type)
        self.token_data = token_data
        self.error_message = error_message


class SMSEventData(EventData):
    """短信事件数据"""
    
    def __init__(self, event_type: str, phone=None, countdown_seconds=None, error_message=None):
        super().__init__(event_type)
        self.phone = phone
        self.countdown_seconds = countdown_seconds
        self.error_message = error_message


# 事件优先级定义
class EventPriority:
    """事件优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


# 事件类型分组
LOGIN_EVENTS = [
    EVENT_USER_LOGIN_START,
    EVENT_USER_LOGIN_SUCCESS,
    EVENT_USER_LOGIN_FAILED,
    EVENT_USER_LOGOUT
]

TOKEN_EVENTS = [
    EVENT_TOKEN_REFRESHED,
    EVENT_TOKEN_EXPIRED,
    EVENT_TOKEN_REFRESH_FAILED
]

SMS_EVENTS = [
    EVENT_SMS_CODE_SENT,
    EVENT_SMS_CODE_SEND_FAILED,
    EVENT_SMS_CODE_COUNTDOWN_START,
    EVENT_SMS_CODE_COUNTDOWN_UPDATE,
    EVENT_SMS_CODE_COUNTDOWN_END
]

NETWORK_EVENTS = [
    EVENT_NETWORK_CONNECTED,
    EVENT_NETWORK_DISCONNECTED,
    EVENT_NETWORK_ERROR
]

ALL_AUTH_EVENTS = LOGIN_EVENTS + TOKEN_EVENTS + SMS_EVENTS + NETWORK_EVENTS

"""
Order客户端认证模块

该模块提供完整的用户认证功能，包括：
- 用户登录（密码/手机验证码）
- Token管理（加密存储、自动刷新）
- 认证服务（HTTP API调用）
- WebSocket认证集成

主要组件：
- LoginManager: 登录流程管理
- TokenManager: Token存储管理  
- AuthService: HTTP认证服务
- HttpClient: HTTP客户端封装
"""

from .models import (
    LoginCredentials,
    TokenData,
    LoginResult,
    AuthResponse,
    LoginErrorCode
)

from .events import (
    EVENT_USER_LOGIN_START,
    EVENT_USER_LOGIN_SUCCESS,
    EVENT_USER_LOGIN_FAILED,
    EVENT_USER_LOGOUT,
    EVENT_TOKEN_REFRESHED,
    EVENT_TOKEN_EXPIRED,
    EVENT_AUTH_STATUS_CHANGED,
    EVENT_SMS_CODE_SENT,
    EVENT_SMS_CODE_SEND_FAILED
)

from .exceptions import (
    AuthException,
    LoginFailedException,
    TokenExpiredException,
    NetworkException,
    ValidationException
)

from .managers.login_manager import LoginManager
from .managers.token_manager import TokenManager
from .services.auth_service import AuthService
from .services.http_client import HttpClient

__all__ = [
    # 数据模型
    'LoginCredentials',
    'TokenData', 
    'LoginResult',
    'AuthResponse',
    'LoginErrorCode',
    
    # 事件常量
    'EVENT_USER_LOGIN_START',
    'EVENT_USER_LOGIN_SUCCESS',
    'EVENT_USER_LOGIN_FAILED',
    'EVENT_USER_LOGOUT',
    'EVENT_TOKEN_REFRESHED',
    'EVENT_TOKEN_EXPIRED',
    'EVENT_AUTH_STATUS_CHANGED',
    'EVENT_SMS_CODE_SENT',
    'EVENT_SMS_CODE_SEND_FAILED',
    
    # 异常类
    'AuthException',
    'LoginFailedException',
    'TokenExpiredException',
    'NetworkException',
    'ValidationException',
    
    # 核心组件
    'LoginManager',
    'TokenManager',
    'AuthService',
    'HttpClient'
]

# 版本信息
__version__ = '1.0.0'
__author__ = 'Order Development Team'

<template>
  <view class="setter-contract-page">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">合同管理</text>
      <view class="header-actions">
        <!-- 视图切换按钮 -->
        <view class="view-switch">
          <wd-button
            :type="currentView === 'summary' ? 'primary' : 'info'"
            size="small"
            @click="switchView('summary')"
          >
            汇总视图
          </wd-button>
          <wd-button
            :type="currentView === 'detail' ? 'primary' : 'info'"
            size="small"
            @click="switchView('detail')"
          >
            明细视图
          </wd-button>
        </view>
        <!-- 新建合同按钮 -->
        <wd-button type="success" size="small" round @click="goToCreate">
          <text class="create-icon">+</text>
          新建合同
        </wd-button>
      </view>
    </view>

    <!-- 汇总视图 -->
    <ContractSummary
      v-if="currentView === 'summary'"
      :contracts="summaryContractList"
      user-role="setter"
      :enable-click="false"
    />

    <!-- 筛选条件 - Tabs格式（仅明细视图显示） -->
    <view v-if="currentView === 'detail'" class="filter-tabs">
      <wd-tabs v-model="activeTab" @change="handleTabChange">
        <wd-tab
          v-for="tab in statusTabs"
          :key="tab.value"
          :title="tab.label"
          :name="tab.value"
        >
        </wd-tab>
      </wd-tabs>
    </view>

    <!-- 明细视图 - 合同列表 -->
    <scroll-view
      v-if="currentView === 'detail'"
      class="scroll-container"
      scroll-y
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="loadMore"
    >
      <view class="contract-list">
        <ContractCard
          v-for="contract in contractList"
          :key="contract.ID"
          :contract="contract"
          user-role="setter"
          @click="goToDetail(contract.ID)"
        >
          <template #actions="{ contract }">
            <!-- 操作按钮 -->
            <view class="contract-actions" @click.stop>
          <!-- 激活按钮 - 仅在 Unexecuted 状态显示 -->
          <wd-button
            v-if="contract.status === 'Unexecuted'"
            type="success"
            size="small"
            @click="activateContract(contract.ID)"
          >
            激活
          </wd-button>

          <!-- 挂起按钮 - 仅在 Executing 状态且无冻结数量时显示 -->
          <wd-button
            v-if="contract.status === 'Executing' && contract.frozenQuantity === 0"
            type="warning"
            size="small"
            @click="deactivateContract(contract.ID)"
          >
            挂起
          </wd-button>

          <!-- 取消按钮 - 仅在 Unexecuted 状态显示 -->
          <wd-button
            v-if="contract.status === 'Unexecuted'"
            type="error"
            size="small"
            @click="cancelContract(contract)"
          >
            取消
          </wd-button>

          <!-- 编辑按钮 - 仅在 Unexecuted 状态显示 -->
          <wd-button
            v-if="contract.status === 'Unexecuted'"
            type="primary"
            size="small"
            @click="editContract(contract.ID)"
          >
            编辑
          </wd-button>

          <!-- 删除按钮 - 仅在 Unexecuted 状态显示 -->
          <wd-button
            v-if="contract.status === 'Unexecuted'"
            type="error"
            size="small"
            @click="deleteContract(contract.ID)"
          >
            删除
          </wd-button>



          <!-- 取消记录按钮 -->
          <wd-button
            v-if="hasCancelRecords(contract)"
            type="info"
            size="small"
            @click="viewCancelRecords(contract)"
          >
            取消记录
          </wd-button>
            </view>
          </template>
        </ContractCard>
      </view>

      <!-- 空状态 -->
      <view v-if="!contractList.length && !loading" class="empty-state">
        <text>暂无合同数据</text>
        <wd-button type="primary" @click="goToCreate">创建第一个合同</wd-button>
      </view>

      <!-- 加载更多状态 -->
      <view v-if="isLoading && contractList.length > 0" class="loading-more">
        <wd-loading size="small" />
        <text>加载中...</text>
      </view>

      <!-- 没有更多数据 -->
      <view v-if="!hasMore && contractList.length > 0" class="no-more">
        <text>没有更多数据了</text>
      </view>
    </scroll-view>

    <!-- 初始加载状态 -->
    <view v-if="loading && contractList.length === 0" class="loading-state">
      <wd-loading />
      <text>加载中...</text>
    </view>
  </view>

  <!-- 取消合同弹窗 -->
  <CancelContractDialog
    v-model="showCancelDialog"
    :contract-data="currentContract"
    @confirm="handleCancelConfirm"
  />
</template>

<route lang="jsonc" type="page">
{
  "style": {
    "navigationBarTitleText": "合同管理"
  }
}
</route>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  getContractsAsSetter, 
  deleteContract as deleteContractAPI, 
  activateContract as activateContractAPI,
  deactivateContract as deactivateContractAPI,
  cancelContract as cancelContractAPI
} from '@/api/contract'
import type { IContract, IContractAsSetterRequest, ICancelContractRequest } from '@/types/contract'
import CancelContractDialog from '@/components/CancelContractDialog.vue'
import ContractCard from '@/components/ContractCard.vue'
import ContractSummary from '@/components/ContractSummary.vue'

const router = useRouter()

// 响应式数据
const contractList = ref<IContract[]>([])
const loading = ref(false)
const isLoading = ref(false)
const isRefreshing = ref(false)

// 视图切换相关
type ViewType = 'summary' | 'detail'
const currentView = ref<ViewType>('detail')

// 汇总视图数据
const summaryContractList = ref<IContract[]>([])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const hasMore = ref(true)

// 请求控制
let currentRequest: Promise<any> | null = null

// Tabs筛选相关
const activeTab = ref('all')
const statusTabs = ref([
  { label: '全部', value: 'all' },
  { label: '未执行', value: 'Unexecuted' },
  { label: '执行中', value: 'Executing' },
  { label: '已完成', value: 'Completed' },
  { label: '已取消', value: 'Cancelled' },
])

// 取消弹窗相关
const showCancelDialog = ref(false)
const currentContract = ref<IContract | null>(null)

// 加载合同列表
async function loadContractList(refresh = false) {
  // 如果是刷新操作，立即清理数据和状态
  if (refresh) {
    currentPage.value = 1
    contractList.value = []
    hasMore.value = true
    isRefreshing.value = true

    // 取消之前的请求
    if (currentRequest) {
      currentRequest = null
    }
  } else {
    // 如果正在加载或没有更多数据，直接返回
    if (isLoading.value || !hasMore.value) return
    isLoading.value = true
  }

  try {
    const params: IContractAsSetterRequest = {
      page: currentPage.value,
      pageSize: pageSize.value,
      status: activeTab.value === 'all' ? undefined : activeTab.value,
    }

    // 创建新的请求
    const requestPromise = getContractsAsSetter(params)
    currentRequest = requestPromise

    const response = await requestPromise

    // 检查这个请求是否还是当前请求（防止过期请求覆盖新数据）
    if (currentRequest !== requestPromise) {
      return // 请求已过期，忽略结果
    }

    if (response.code === 0) {
      // 兼容处理：检查返回的数据格式
      let list: IContract[]
      let totalCount: number

      if (Array.isArray(response.data)) {
        // 旧格式：直接返回数组
        list = response.data
        totalCount = response.data.length
        hasMore.value = false // 旧格式没有分页，设置为没有更多数据
      } else {
        // 新格式：分页格式
        list = response.data.list || []
        totalCount = response.data.total || 0
        hasMore.value = contractList.value.length + list.length < totalCount
      }

      if (refresh) {
        contractList.value = list
      } else {
        contractList.value.push(...list)
      }

      total.value = totalCount
    } else {
      uni.showToast({
        title: response.msg || '获取合同列表失败',
        icon: 'error',
      })
    }
  } catch (error) {
    console.error('获取合同列表失败:', error)
    uni.showToast({
      title: '网络错误',
      icon: 'error',
    })
  } finally {
    loading.value = false
    isLoading.value = false
    isRefreshing.value = false
  }
}

// 加载更多
async function loadMore() {
  if (!hasMore.value || isLoading.value) return

  currentPage.value++
  await loadContractList()
}

// 下拉刷新
async function onRefresh() {
  await loadContractList(true)
}

// Tab切换
async function handleTabChange() {
  // 立即清理数据，避免显示旧数据
  contractList.value = []
  hasMore.value = true
  currentPage.value = 1

  // 取消之前的请求
  if (currentRequest) {
    currentRequest = null
  }

  // 加载新数据
  await loadContractList(true)
}

// 创建新合同
function goToCreate() {
  router.push('/pages/contract/form')
}

// 查看合同详情
function goToDetail(contractId: number) {
  router.push(`/pages/contract/detail?id=${contractId}&role=setter`)
}

// 编辑合同
function editContract(contractId: number) {
  router.push(`/pages/contract/form?id=${contractId}`)
}

// 激活合同
async function activateContract(contractId: number) {
  try {
    const response = await activateContractAPI(contractId)
    if (response.code === 0) {
      uni.showToast({
        title: '激活成功',
        icon: 'success',
      })
      loadContractList()
    } else {
      uni.showToast({
        title: response.msg || '激活失败',
        icon: 'error',
      })
    }
  } catch (error) {
    console.error('激活合同失败:', error)
    uni.showToast({
      title: '网络错误',
      icon: 'error',
    })
  }
}

// 挂起合同
async function deactivateContract(contractId: number) {
  try {
    const response = await deactivateContractAPI(contractId)
    if (response.code === 0) {
      uni.showToast({
        title: '挂起成功',
        icon: 'success',
      })
      loadContractList()
    } else {
      uni.showToast({
        title: response.msg || '挂起失败',
        icon: 'error',
      })
    }
  } catch (error) {
    console.error('挂起合同失败:', error)
    uni.showToast({
      title: '网络错误',
      icon: 'error',
    })
  }
}

// 删除合同
function deleteContract(contractId: number) {
  uni.showModal({
    title: '确认删除',
    content: '删除后无法恢复，确认删除？',
    success: async (res) => {
      if (res.confirm) {
        try {
          const response = await deleteContractAPI(contractId)
          if (response.code === 0) {
            uni.showToast({
              title: '删除成功',
              icon: 'success',
            })
            loadContractList()
          } else {
            uni.showToast({
              title: response.msg || '删除失败',
              icon: 'error',
            })
          }
        } catch (error) {
          console.error('删除合同失败:', error)
          uni.showToast({
            title: '网络错误',
            icon: 'error',
          })
        }
      }
    },
  })
}

// 这些函数已移至ContractCard组件中

// 权限判断方法 - 更新为 V3 规范
function hasCancelRecords(contract: IContract): boolean {
  return contract.status === 'Cancelled' || contract.remainingQuantity < contract.totalQuantity
}

// 取消合同
function cancelContract(contract: IContract) {
  currentContract.value = contract
  showCancelDialog.value = true
}

// 处理取消确认
async function handleCancelConfirm(data: { cancelQuantity: number; reason: string }) {
  if (!currentContract.value) return

  try {
    const cancelData: ICancelContractRequest = {
      cancelQuantity: data.cancelQuantity,
      reason: data.reason
    }

    const response = await cancelContractAPI(currentContract.value.ID, cancelData)

    if (response.code === 0) {
      uni.showToast({
        title: '取消成功',
        icon: 'success',
      })
      showCancelDialog.value = false
      loadContractList()
    } else {
      uni.showToast({
        title: response.msg || '取消失败',
        icon: 'error',
      })
    }
  } catch (error) {
    console.error('取消合同失败:', error)
    uni.showToast({
      title: '网络错误',
      icon: 'error',
    })
  }
}

// 查看取消记录
function viewCancelRecords(contract: IContract) {
  router.push(`/pages/contract/cancel-records?contractId=${contract.ID}`)
}

// 加载汇总视图数据
async function loadSummaryData() {
  try {
    // 获取所有执行中的合同进行汇总
    const response = await getContractsAsSetter({
      status: 'Executing',
      pageSize: 1000 // 获取足够多的数据进行汇总
    })

    if (response.code === 0) {
      summaryContractList.value = response.data.list || []
    }
  } catch (error) {
    console.error('获取汇总数据失败:', error)
  }
}

// 切换视图
function switchView(view: ViewType) {
  currentView.value = view

  // 根据视图类型加载对应数据
  if (view === 'detail') {
    loadContractList(true)
  } else if (view === 'summary') {
    loadSummaryData()
  }
}

// 生命周期
onMounted(() => {
  loading.value = true
  loadContractList(true)
})
</script>

<style lang="scss" scoped>
.setter-contract-page {
  padding: 20rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;

  .page-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 20rpx;

    .view-switch {
      display: flex;
      gap: 10rpx;
    }

    // 新建合同按钮样式
    :deep(.wd-button) {
      &[type="success"] {
        background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
        border: none;
        box-shadow: 0 2rpx 8rpx rgba(103, 194, 58, 0.3);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1rpx);
          box-shadow: 0 4rpx 12rpx rgba(103, 194, 58, 0.4);
        }

        &:active {
          transform: translateY(0);
          box-shadow: 0 2rpx 8rpx rgba(103, 194, 58, 0.3);
        }

        .create-icon {
          font-size: 28rpx;
          font-weight: bold;
          margin-right: 8rpx;
          display: inline-block;
          line-height: 1;
        }
      }
    }
  }
}

.filter-tabs {
  margin-bottom: 30rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.scroll-container {
  height: calc(100vh - 200rpx);

  .loading-more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30rpx;
    gap: 16rpx;
    color: #999;
    font-size: 28rpx;
  }

  .no-more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30rpx;
    color: #999;
    font-size: 28rpx;
  }
}

// 合同卡片样式已迁移到ContractCard组件中

.empty-state, .loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx;
  color: #999;
  font-size: 28rpx;
  gap: 30rpx;
}
</style>
<route lang="jsonc" type="page">
{
  "style": {
    "navigationBarTitleText": "期货合约选择器演示"
  }
}
</route>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import InstrumentSelector from '@/components/InstrumentSelector.vue'
import type { IInstrumentSelectItem } from '@/types'
import { ExchangeMap } from '@/types/instrument'

defineOptions({
  name: 'InstrumentSelectorDemo'
})

// 选中的合约
const selectedInstrument = ref<IInstrumentSelectItem | null>(null)
const selectedInstrument2 = ref<IInstrumentSelectItem | null>(null)
const selectedInstrument3 = ref<IInstrumentSelectItem | null>(null)
const selectedInstrument4 = ref<IInstrumentSelectItem | null>(null)

// 显示当前选择的详细信息
const selectionSummary = computed(() => {
  if (!selectedInstrument.value) return null
  return {
    ...selectedInstrument.value,
    exchangeName: ExchangeMap[selectedInstrument.value.exchange_id] || selectedInstrument.value.exchange_id,
    displayName: `${ExchangeMap[selectedInstrument.value.exchange_id] || selectedInstrument.value.exchange_id}.${selectedInstrument.value.instrument_id}`
  }
})

// 处理选择变化
const handleInstrumentChange = (value: IInstrumentSelectItem | null, type: string = '基础') => {
  console.log(`${type}选择器变化:`, value)
  uni.showToast({
    title: value ? `选择了 ${value.instrument_id}` : '已清空选择',
    icon: value ? 'success' : 'none',
    duration: 1500
  })
}

// 重置所有选择
const resetAllSelections = () => {
  selectedInstrument.value = null
  selectedInstrument2.value = null
  selectedInstrument3.value = null
  selectedInstrument4.value = null
  uni.showToast({
    title: '已重置所有选择',
    icon: 'success'
  })
}

// 模拟表单提交
const handleSubmit = () => {
  if (!selectedInstrument.value) {
    uni.showToast({
      title: '请先选择期货合约',
      icon: 'error'
    })
    return
  }
  
  uni.showModal({
    title: '提交确认',
    content: `确认提交合约：${selectedInstrument.value.instrument_id}？`,
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: '提交成功！',
          icon: 'success'
        })
      }
    }
  })
}
</script>

<template>
  <view class="demo-page">
    <view class="demo-header">
      <text class="page-title">期货合约选择器</text>
      <text class="page-desc">演示期货合约选择器组件的各种使用方式</text>
    </view>

    <view class="demo-content">
      <!-- 基础用法 -->
      <view class="demo-section">
        <view class="section-header">
          <text class="section-title">基础用法</text>
          <text class="section-desc">三级联动选择：交易所 → 商品 → 合约</text>
        </view>
        <view class="form-item">
          <InstrumentSelector 
            v-model="selectedInstrument"
            label="选择期货合约:"
            placeholder="请选择期货合约"
            @change="(value) => handleInstrumentChange(value, '基础')"
          />
        </view>
        
        <view v-if="selectionSummary" class="selected-info">
          <text class="info-title">✅ 已选择合约:</text>
          <view class="info-content">
            <view class="info-row">
              <text class="info-label">显示名称:</text>
              <text class="info-value highlight">{{ selectionSummary.displayName }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">合约代码:</text>
              <text class="info-value">{{ selectionSummary.instrument_id }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">合约名称:</text>
              <text class="info-value">{{ selectionSummary.instrument_name }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">商品名称:</text>
              <text class="info-value">{{ selectionSummary.product_name }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">交易所:</text>
              <text class="info-value">{{ selectionSummary.exchangeName }} ({{ selectionSummary.exchange_id }})</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 自定义占位符 -->
      <view class="demo-section">
        <view class="section-header">
          <text class="section-title">自定义占位符</text>
          <text class="section-desc">自定义提示文字</text>
        </view>
        <view class="form-item">
          <InstrumentSelector 
            v-model="selectedInstrument2"
            label="选择主力合约:"
            placeholder="选择一个主力合约进行交易"
            @change="(value) => handleInstrumentChange(value, '主力合约')"
          />
        </view>
      </view>

      <!-- 禁用状态 -->
      <view class="demo-section">
        <view class="section-header">
          <text class="section-title">禁用状态</text>
          <text class="section-desc">禁用选择器的交互</text>
        </view>
        <view class="form-item">
          <InstrumentSelector 
            v-model="selectedInstrument3"
            label="禁用状态:"
            placeholder="此选择器已被禁用"
            disabled
          />
        </view>
      </view>

      <!-- 不可清空 -->
      <view class="demo-section">
        <view class="section-header">
          <text class="section-title">不可清空</text>
          <text class="section-desc">隐藏清空按钮，选择后不可清空</text>
        </view>
        <view class="form-item">
          <InstrumentSelector 
            v-model="selectedInstrument4"
            label="选择后不可清空:"
            placeholder="选择后无法清空"
            :clearable="false"
            @change="(value) => handleInstrumentChange(value, '不可清空')"
          />
        </view>
      </view>

      <!-- 清空功能演示 -->
      <view class="demo-section">
        <view class="section-header">
          <text class="section-title">清空功能演示</text>
          <text class="section-desc">演示清空按钮的使用，可清空状态下会显示清空按钮</text>
        </view>
        <view class="form-item">
          <InstrumentSelector 
            v-model="selectedInstrument"
            label="可清空的选择器:"
            placeholder="选择合约后会显示清空按钮"
            clearable
            @change="(value) => handleInstrumentChange(value, '可清空')"
          />
        </view>
        <view v-if="selectedInstrument" class="clear-demo-info">
          <text class="clear-info-text">✨ 已选择合约，现在可以点击清空按钮清除选择</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="demo-section">
        <view class="section-header">
          <text class="section-title">操作演示</text>
          <text class="section-desc">演示常用操作功能</text>
        </view>
        <view class="button-group">
          <button 
            class="demo-button primary"
            @click="handleSubmit"
          >
            提交选择
          </button>
          <button 
            class="demo-button secondary"
            @click="resetAllSelections"
          >
            重置所有选择
          </button>
        </view>
      </view>

      <!-- 功能特性 -->
      <view class="demo-section">
        <view class="section-header">
          <text class="section-title">🚀 组件特性</text>
        </view>
        <view class="feature-list">
          <view class="feature-item">
            <text class="feature-icon">🔄</text>
            <text class="feature-text">三级联动选择：交易所 → 商品 → 合约</text>
          </view>
          <view class="feature-item">
            <text class="feature-icon">⚡</text>
            <text class="feature-text">异步数据加载，支持大量合约数据</text>
          </view>
          <view class="feature-item">
            <text class="feature-icon">📱</text>
            <text class="feature-text">完美适配移动端触摸操作</text>
          </view>
          <view class="feature-item">
            <text class="feature-icon">🎨</text>
            <text class="feature-text">自定义样式，支持禁用和清空控制</text>
          </view>
          <view class="feature-item">
            <text class="feature-icon">🔍</text>
            <text class="feature-text">智能筛选，快速定位目标合约</text>
          </view>
          <view class="feature-item">
            <text class="feature-icon">💾</text>
            <text class="feature-text">数据缓存，避免重复请求</text>
          </view>
        </view>
      </view>

      <!-- 使用说明 -->
      <view class="demo-section">
        <view class="section-header">
          <text class="section-title">📖 使用方法</text>
        </view>
        <view class="usage-code">
          <text class="code-title">基础用法:</text>
          <view class="code-block">
            <text class="code-line">&lt;InstrumentSelector</text>
            <text class="code-line">  v-model="selectedInstrument"</text>
            <text class="code-line">  label="选择期货合约:"</text>
            <text class="code-line">  placeholder="请选择期货合约"</text>
            <text class="code-line">  @change="handleChange"</text>
            <text class="code-line">/&gt;</text>
          </view>
          
          <text class="code-title">Props配置:</text>
          <view class="props-list">
            <view class="prop-item">
              <text class="prop-name">v-model</text>
              <text class="prop-desc">绑定选中的合约对象</text>
            </view>
            <view class="prop-item">
              <text class="prop-name">placeholder</text>
              <text class="prop-desc">占位符文字</text>
            </view>
            <view class="prop-item">
              <text class="prop-name">disabled</text>
              <text class="prop-desc">是否禁用</text>
            </view>
            <view class="prop-item">
              <text class="prop-name">label</text>
              <text class="prop-desc">选择器标签文字</text>
            </view>
            <view class="prop-item">
              <text class="prop-name">clearable</text>
              <text class="prop-desc">是否可清空</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.demo-page {
  min-height: 100vh;
  background: #f8f9fa;
}

.demo-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 80rpx 32rpx 60rpx;
  color: white;
  text-align: center;
}

.page-title {
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  display: block;
}

.page-desc {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
}

.demo-content {
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  gap: 48rpx;
}

.demo-section {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #303133;
  display: block;
  margin-bottom: 8rpx;
}

.section-desc {
  font-size: 26rpx;
  color: #606266;
  display: block;
}

.form-item {
  margin-bottom: 32rpx;
}

.form-label {
  font-size: 28rpx;
  color: #606266;
  display: block;
  margin-bottom: 16rpx;
}

.selected-info {
  margin-top: 24rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12rpx;
  border: 1rpx solid #0ea5e9;
}

.info-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #0369a1;
  display: block;
  margin-bottom: 16rpx;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.info-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8rpx 0;
}

.info-label {
  font-size: 26rpx;
  color: #64748b;
  font-weight: 500;
  min-width: 160rpx;
}

.info-value {
  font-size: 26rpx;
  color: #1e293b;
  flex: 1;
  text-align: right;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  
  &.highlight {
    color: #0369a1;
    font-weight: 600;
    font-size: 28rpx;
  }
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.feature-icon {
  font-size: 32rpx;
  width: 48rpx;
  text-align: center;
}

.feature-text {
  font-size: 28rpx;
  color: #606266;
  flex: 1;
}

// 清空演示样式
.clear-demo-info {
  margin-top: 24rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border-radius: 12rpx;
  border: 1rpx solid #22c55e;
}

.clear-info-text {
  font-size: 26rpx;
  color: #15803d;
  font-weight: 500;
  display: block;
  line-height: 1.5;
}

// 按钮组样式
.button-group {
  display: flex;
  gap: 24rpx;
  margin-top: 24rpx;
}

.demo-button {
  flex: 1;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
  
  &.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    
    &:active {
      transform: scale(0.98);
      opacity: 0.9;
    }
  }
  
  &.secondary {
    background: #f8f9fa;
    color: #606266;
    border: 1rpx solid #e4e7ed;
    
    &:active {
      background: #e9ecef;
      transform: scale(0.98);
    }
  }
}

// 使用说明样式
.usage-code {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  border: 1rpx solid #e4e7ed;
}

.code-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #303133;
  display: block;
  margin-bottom: 16rpx;
}

.code-block {
  background: #2d3748;
  border-radius: 8rpx;
  padding: 16rpx;
  margin-bottom: 24rpx;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.code-line {
  display: block;
  color: #e2e8f0;
  font-size: 24rpx;
  line-height: 1.6;
  margin-bottom: 4rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.props-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.prop-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  padding: 12rpx;
  background: white;
  border-radius: 8rpx;
  border: 1rpx solid #e4e7ed;
}

.prop-name {
  font-size: 24rpx;
  color: #667eea;
  font-weight: 600;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  min-width: 140rpx;
  background: #f0f4ff;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

.prop-desc {
  font-size: 24rpx;
  color: #606266;
  flex: 1;
  line-height: 1.5;
}
</style>
# 实时行情订阅功能实施指南

## 概述

本文档描述了实时行情订阅与推送功能的实施情况，该功能允许App端用户订阅指定合约的实时行情数据。

## 已实施的功能

### 前端部分 (app/src/store/)

#### 1. 新增文件: `market.ts`
- **功能**: 专门管理行情数据的状态和业务逻辑
- **主要状态**:
  - `marketData`: Record<string, MarketData> - 存储各合约的行情数据（使用响应式对象）
  - `subscribedSymbols`: string[] - 存储已订阅的合约代码（使用响应式数组）
- **主要方法**:
  - `subscribe(symbol, exchange)` - 订阅指定合约行情
  - `unsubscribe(symbol)` - 取消订阅指定合约
  - `getMarketData(symbol)` - 获取指定合约行情数据
  - `getAllMarketData()` - 获取所有行情数据
  - `isSubscribed(symbol)` - 检查是否已订阅
  - `_handleMarketUpdate(data)` - 处理行情更新（内部方法）

#### 2. 修改文件: `socket.ts`
- **新增功能**: 处理 `market_update` 事件
- **修改内容**:
  - 实现基于事件处理器注册的消息处理机制
  - 添加 `registerHandler` 和 `unregisterHandler` 方法
  - 在 `handleMessage` 中调用已注册的事件处理器
  - 导出 `sendMessage` 方法供 market store 使用
  - 消除了对业务 store 的循环依赖

#### 3. 修改文件: `index.ts`
- **新增**: 导出 market store

### 后端部分 (admin/server/ws_hub/)

#### 修改文件: `app_hub.go`
- **新增结构**:
  - `AppClient.marketSubscriptions` - 客户端行情订阅关系
  - `AppHub.marketSubscriptions` - Hub级别的订阅关系管理
- **新增方法**:
  - `handleMarketSubscribe()` - 处理行情订阅请求
  - `handleMarketUnsubscribe()` - 处理取消订阅请求
  - `sendCurrentMarketData()` - 发送当前行情数据（从Redis获取JSON格式数据）
  - `cleanupClientMarketSubscriptions()` - 清理客户端订阅关系
  - `startRedisListener()` - 启动Redis监听器，监听tick_update频道
  - `handleTickUpdate(symbol)` - 处理Redis行情更新，直接接收symbol参数
- **新增事件处理**:
  - `subscribe_market` - 订阅行情事件
  - `unsubscribe_market` - 取消订阅事件

## 通信协议

### 客户端 -> 服务端

#### 订阅行情
```json
{
  "event": "subscribe_market",
  "payload": {
    "symbol": "IF2508",
    "exchange": "CFFEX"
  },
  "timestamp": 1678886400000
}
```

#### 取消订阅
```json
{
  "event": "unsubscribe_market",
  "payload": {
    "symbol": "IF2508"
  },
  "timestamp": 1678886400000
}
```

### 服务端 -> 客户端

#### 行情更新
```json
{
  "event": "market_update",
  "payload": {
    "symbol": "IF2508",
    "exchange": "CFFEX",
    "last_price": 68000.50,
    "last_volume": 12345.67,
    "turnover": 839512345.67,
    "open_interest": 9876,
    "timestamp": 1678886400000,
    "ask_price_1": 68000.60,
    "ask_volume_1": 10.5,
    "bid_price_1": 68000.40,
    "bid_volume_1": 8.8
  },
  "timestamp": 1678886400000
}
```

## 使用示例

### 在Vue组件中使用

```vue
<script setup lang="ts">
import { useSocketStore, useMarketStore } from '@/store'

const socketStore = useSocketStore()
const marketStore = useMarketStore()

// 订阅行情
function subscribeMarket() {
  marketStore.subscribe('IF2508', 'CFFEX')
}

// 取消订阅
function unsubscribeMarket() {
  marketStore.unsubscribe('IF2508')
}

// 获取行情数据
const marketData = marketStore.getMarketData('IF2508')

// 检查订阅状态
const isSubscribed = marketStore.isSubscribed('IF2508')
</script>
```

### 演示页面

已创建演示页面 `app/src/pages/market-demo.vue`，展示了完整的使用流程：
- WebSocket连接状态显示
- 行情订阅/取消订阅操作
- 实时行情数据展示
- 订阅列表管理

## 数据流程

1. **用户订阅**: 用户在App中操作，触发 `marketStore.subscribe()`
2. **发送请求**: market store 调用 `socketStore.sendMessage()` 发送订阅请求
3. **后端处理**: ws_hub 收到请求，记录订阅关系，立即推送当前行情
4. **Redis监听**: ws_hub 监听 `tick_update` 频道
5. **行情更新**: 收到Redis更新通知，获取最新数据并推送给订阅客户端
6. **前端更新**: socket store 收到消息，调用 market store 更新状态

## Redis数据结构

### 行情数据存储
- **键格式**: `tick:{symbol}` (如 `tick:IF2508`)
- **数据类型**: String (JSON格式)
- **字段**: symbol, exchange, last_price, last_volume, turnover, open_interest, timestamp, ask_price_1, ask_volume_1, bid_price_1, bid_volume_1 等

### 更新通知
- **频道**: `tick_update`
- **消息格式**: 合约代码 (如 `IF2508`)
- **处理方式**: 后端接收到频道消息后，直接使用消息内容作为symbol，从Redis获取对应的JSON数据

## 错误处理

- **连接断开**: 自动清理客户端的所有订阅关系
- **重复订阅**: 前端检查避免重复订阅
- **无效数据**: 后端验证订阅参数，前端验证行情数据格式
- **Redis错误**: 记录日志，不影响其他功能

## 性能优化

- **批量推送**: 使用goroutine异步处理行情推送
- **超时保护**: 推送消息时设置超时，避免阻塞
- **内存管理**: 客户端断开时及时清理订阅关系
- **动态导入**: 前端使用动态导入避免循环依赖

## 测试建议

1. **单元测试**: 测试 market store 的各个方法
2. **集成测试**: 测试前后端WebSocket通信
3. **压力测试**: 测试大量客户端同时订阅的性能
4. **故障测试**: 测试网络断开、Redis故障等异常情况

## 部署注意事项

1. 确保Redis服务正常运行
2. 确保 `@hq` 模块正确写入Redis数据
3. 确保WebSocket服务正常启动
4. 监控Redis `tick_update` 频道的消息流量
5. 监控WebSocket连接数和内存使用情况

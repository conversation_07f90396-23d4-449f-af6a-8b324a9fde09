# 使用官方 Python 镜像作为基础镜像
FROM python:3.11

# 设置工作目录
WORKDIR /app

# 复制 requirements.txt 文件到工作目录
COPY requirements.txt /app/

# 配置清华apt源（先于apt操作），适配Debian 12+
RUN sed -i 's|http://deb.debian.org|https://mirrors.tuna.tsinghua.edu.cn|g' /etc/apt/sources.list.d/debian.sources

# 安装必要的软件包并清理缓存
RUN apt-get update && \
    apt-get install -y --no-install-recommends locales && \
    rm -rf /var/lib/apt/lists/*

# 配置中文区域设置
RUN sed -i '/zh_CN.UTF-8/s/^# //g' /etc/locale.gen && \
   sed -i '/zh_CN.GB18030/s/^# //g' /etc/locale.gen && \
   locale-gen

# 设置默认语言环境
ENV LANG=zh_CN.GB18030

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt --index-url https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple

# 复制项目代码（不包括venv）
COPY . /app
RUN if [ -d "/app/venv" ]; then rm -rf /app/venv; fi

# 运行主程序
CMD ["python", "main.py", "docker_config.ini"]
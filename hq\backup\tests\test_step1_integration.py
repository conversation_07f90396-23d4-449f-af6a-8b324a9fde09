#!/usr/bin/env python
"""
步骤1集成测试脚本
测试ServerManager的完整功能，验证无数据库依赖的服务器选择功能
"""
import asyncio
from core.server_manager import ServerManager
from utils.network_utils import ping_ip_port, batch_ping_addresses


async def test_step1_integration():
    """测试步骤1的完整集成功能"""
    print("=== 步骤1：Broker配置本地化与智能优选 - 集成测试 ===\n")
    
    # 1. 测试ServerManager初始化
    print("1. 测试ServerManager初始化...")
    try:
        manager = ServerManager()
        print("[OK] ServerManager初始化成功")
        print(f"[OK] 成功加载 {len(manager.get_all_brokers())} 个期货公司\n")
    except Exception as e:
        print(f"[FAIL] ServerManager初始化失败: {e}")
        return False
    
    # 2. 测试网络工具功能
    print("2. 测试网络工具功能...")
    test_addresses = ['***************:41313', '***************:41313']
    try:
        speed_results = await batch_ping_addresses(test_addresses)
        if speed_results:
            print("[OK] 网络测速功能正常")
            for addr, speed in speed_results.items():
                print(f"  {addr}: {speed:.2f}ms")
        else:
            print("[WARN] 测试地址无法连接，但功能正常")
        print()
    except Exception as e:
        print(f"[FAIL] 网络工具测试失败: {e}")
        return False
    
    # 3. 测试期货公司服务器选择
    print("3. 测试期货公司服务器选择...")
    try:
        # 选择前5个期货公司进行测试，避免测试时间过长
        all_brokers = manager.get_all_brokers()
        test_broker_ids = [broker.broker_id for broker in all_brokers[:5]]
        print(f"测试期货公司: {test_broker_ids}")
        
        best_info = await manager.select_best_broker(broker_ids=test_broker_ids)
        if best_info:
            print("[OK] 成功选择最佳期货公司服务器")
            print(f"  期货公司: {best_info['broker_name']} ({best_info['broker_id']})")
            print(f"  服务器: {best_info['server_name']}")
            print(f"  平均延迟: {best_info['avg_speed']:.2f}ms")
            print(f"  可用地址: {len(best_info['addresses'])} 个")
        else:
            print("[WARN] 未找到可用的期货公司服务器（可能是网络问题）")
        print()
    except Exception as e:
        print(f"[FAIL] 期货公司服务器选择失败: {e}")
        return False
    
    # 4. 测试行情地址获取
    print("4. 测试行情地址获取...")
    try:
        if best_info:
            addresses = manager.get_market_data_addresses()
            print(f"[OK] 成功获取 {len(addresses)} 个行情地址")
            if addresses:
                print(f"  示例地址: {addresses[0]}")
        else:
            # 手动指定一个期货公司进行测试
            addresses = manager.get_market_data_addresses(broker_id="4080")
            if addresses:
                print(f"[OK] 成功获取指定期货公司的 {len(addresses)} 个行情地址")
                print(f"  示例地址: {addresses[0]}")
            else:
                print("[WARN] 未获取到行情地址")
        print()
    except Exception as e:
        print(f"[FAIL] 行情地址获取失败: {e}")
        return False
    
    # 5. 测试健康检查功能
    print("5. 测试健康检查功能...")
    try:
        if best_info:
            is_healthy = manager.health_check()
            status = "正常" if is_healthy else "异常"
            print(f"[OK] 健康检查完成，状态: {status}")
        else:
            print("[WARN] 跳过健康检查（没有选定的服务器）")
        print()
    except Exception as e:
        print(f"[FAIL] 健康检查失败: {e}")
        return False
    
    print("=== 步骤1集成测试完成 ===")
    print("[OK] 所有核心功能测试通过")
    print("[OK] ServerManager可以替代原有的数据库依赖功能")
    print("[OK] 系统已成功实现broker配置本地化与智能优选")
    
    return True


async def test_old_function_deprecation():
    """测试旧函数的弃用提示"""
    print("\n=== 测试旧函数弃用 ===")
    try:
        from engine.address import get_md_address
        print("[FAIL] 错误：旧函数仍然可以导入")
        return False
    except ImportError:
        print("[OK] 旧函数已正确移除")
        return True
    except Exception as e:
        print(f"[OK] 旧函数已弃用: {e}")
        return True


if __name__ == "__main__":
    # 运行集成测试
    success = asyncio.run(test_step1_integration())
    
    # 测试旧函数弃用
    deprecation_success = asyncio.run(test_old_function_deprecation())
    
    if success and deprecation_success:
        print("\n[SUCCESS] 步骤1重构成功！可以继续进行步骤2的开发。")
        exit(0)
    else:
        print("\n[ERROR] 步骤1重构存在问题，请检查并修复。")
        exit(1)
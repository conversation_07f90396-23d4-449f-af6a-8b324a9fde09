import xml.etree.ElementTree as ET
import os
from typing import Dict, List, Optional, Union, Any


class Server:
    """服务器信息类"""
    def __init__(self, name: str, trading_items: List[str], market_data_items: List[str], 
                 se: str = None, is_new: str = None, auth_code2: str = None, auth_code3: str = None):
        self.name = name
        self.se = se
        self.is_new = is_new
        self.auth_code2 = auth_code2
        self.auth_code3 = auth_code3
        self.trading_items = trading_items
        self.market_data_items = market_data_items

    def __str__(self) -> str:
        return f"Server(name={self.name}, trading_items={len(self.trading_items)}, market_data_items={len(self.market_data_items)})"

    def __repr__(self) -> str:
        return self.__str__()


class Broker:
    """券商信息类"""
    def __init__(self, broker_id: str, broker_name: str, broker_ename: str, 
                 float_profit_algorithm: str = None, margin_algorithm: str = None, 
                 is_discount_czce: str = None, is_old_risk_alg: str = None):
        self.broker_id = broker_id
        self.broker_name = broker_name
        self.broker_ename = broker_ename
        self.float_profit_algorithm = float_profit_algorithm
        self.margin_algorithm = margin_algorithm
        self.is_discount_czce = is_discount_czce
        self.is_old_risk_alg = is_old_risk_alg
        self.servers: Dict[str, Server] = {}

    def add_server(self, server: Server):
        self.servers[server.name] = server

    def __str__(self) -> str:
        return f"Broker(id={self.broker_id}, name={self.broker_name}, ename={self.broker_ename}, servers={len(self.servers)})"

    def __repr__(self) -> str:
        return self.__str__()


class BrokerParser:
    """XML broker文件解析器"""
    def __init__(self, xml_file: str):
        """
        初始化解析器
        
        Args:
            xml_file: broker.xml文件的路径
        """
        self.xml_file = xml_file
        self.brokers: Dict[str, Broker] = {}
        self.parse_xml()

    def parse_xml(self):
        """解析XML文件"""
        if not os.path.exists(self.xml_file):
            raise FileNotFoundError(f"文件不存在: {self.xml_file}")
        
        try:
            # broker.xml 明确使用 gb2312 编码
            with open(self.xml_file, 'r', encoding='gb2312') as f:
                xml_content = f.read()
            
            root = ET.fromstring(xml_content)
            self._parse_root(root)
            print(f"成功使用 gb2312 编码解析XML文件: {self.xml_file}")
        except FileNotFoundError:
            raise
        except Exception as e:
            raise Exception(f"解析XML文件失败: {str(e)}")
    
    def _parse_root(self, root: ET.Element):
        """解析XML根元素"""
        # 遍历所有broker元素
        for broker_elem in root.findall('broker'):
            broker_id = broker_elem.get('BrokerID', '')
            broker_name = broker_elem.get('BrokerName', '')
            broker_ename = broker_elem.get('BrokerEName', '')
            
            # 获取可选属性
            float_profit_algorithm = self._get_element_text(broker_elem, 'FloatProfitAlgorithm')
            margin_algorithm = self._get_element_text(broker_elem, 'MarginAlgorithm')
            is_discount_czce = self._get_element_text(broker_elem, 'IsDiscountCZCE')
            is_old_risk_alg = self._get_element_text(broker_elem, 'IsOldRiskAlg')
            
            # 创建Broker对象
            broker = Broker(broker_id, broker_name, broker_ename, 
                           float_profit_algorithm, margin_algorithm,
                           is_discount_czce, is_old_risk_alg)
            
            # 解析Servers
            servers_elem = broker_elem.find('Servers')
            if servers_elem is not None:
                for server_elem in servers_elem.findall('Server'):
                    name = self._get_element_text(server_elem, 'Name')  # 修复：XML中是Name而不是n
                    se = self._get_element_text(server_elem, 'Se')
                    is_new = self._get_element_text(server_elem, 'New')
                    auth_code2 = self._get_element_text(server_elem, 'AuthCode2')
                    auth_code3 = self._get_element_text(server_elem, 'AuthCode3')
                    
                    # 解析Trading和MarketData
                    trading_items = []
                    market_data_items = []
                    
                    trading_elem = server_elem.find('Trading')
                    if trading_elem is not None:
                        trading_items = [item.text for item in trading_elem.findall('item') if item.text]
                    
                    market_data_elem = server_elem.find('MarketData')
                    if market_data_elem is None:
                        market_data_elem = server_elem.find('MarKetData') # 兼容不规范的标签
                    if market_data_elem is not None:
                        market_data_items = [item.text for item in market_data_elem.findall('item') if item.text]
                    
                    # 创建Server对象并添加到Broker
                    server = Server(name, trading_items, market_data_items, se, is_new, auth_code2, auth_code3)
                    broker.add_server(server)
            
            # 将Broker添加到字典
            self.brokers[broker_id] = broker
    
    def _get_element_text(self, elem: ET.Element, tag_name: str) -> Optional[str]:
        """获取元素的文本，如果元素不存在则返回None"""
        sub_elem = elem.find(tag_name)
        return sub_elem.text if sub_elem is not None else None
    
    def get_broker_by_id(self, broker_id: str) -> Optional[Broker]:
        """通过ID获取券商信息"""
        return self.brokers.get(broker_id)
    
    def get_brokers_by_name(self, name: str) -> List[Broker]:
        """通过名称或拼音搜索券商"""
        results = []
        for broker in self.brokers.values():
            if name.lower() in broker.broker_name.lower() or name.lower() in broker.broker_ename.lower():
                results.append(broker)
        return results
    
    def get_all_brokers(self) -> List[Broker]:
        """获取所有券商"""
        return list(self.brokers.values())
    
    def filter_servers_by_name(self, broker_id: str, server_name: str = None) -> Dict[str, Server]:
        """
        筛选指定券商的服务器
        
        Args:
            broker_id: 券商ID
            server_name: 服务器名称，如果为None则返回所有服务器
            
        Returns:
            匹配的服务器字典
        """
        broker = self.get_broker_by_id(broker_id)
        if not broker:
            return {}
        
        if server_name is None:
            return broker.servers
        
        result = {}
        for name, server in broker.servers.items():
            # 确保name不为None
            if name is not None:
                if server_name.lower() in name.lower():
                    result[name] = server
            else:
                # 如果name是None，但server_name也是空字符串或None，则也匹配
                if not server_name or server_name == "":
                    result[name] = server
        
        return result
    
    def get_trading_addresses(self, broker_id: str, server_name: str = None) -> Dict[str, List[str]]:
        """
        获取指定券商和服务器的交易地址
        
        Args:
            broker_id: 券商ID
            server_name: 服务器名称，如果为None则返回所有服务器的交易地址
            
        Returns:
            服务器名称到交易地址的映射
        """
        servers = self.filter_servers_by_name(broker_id, server_name)
        return {name: server.trading_items for name, server in servers.items()}
    
    def get_market_data_addresses(self, broker_id: str, server_name: str = None) -> Dict[str, List[str]]:
        """
        获取指定券商和服务器的行情数据地址
        
        Args:
            broker_id: 券商ID
            server_name: 服务器名称，如果为None则返回所有服务器的行情数据地址
            
        Returns:
            服务器名称到行情数据地址的映射
        """
        servers = self.filter_servers_by_name(broker_id, server_name)
        return {name: server.market_data_items for name, server in servers.items()}


# 示例用法
def main():
    # 获取当前脚本所在目录的路径
    script_dir = os.path.dirname(os.path.abspath(__file__))
    xml_path = os.path.join(script_dir, "broker.xml")
    
    # 解析broker.xml
    parser = BrokerParser(xml_path)
    
    # 获取所有券商
    brokers = parser.get_all_brokers()
    print(f"总共有{len(brokers)}个券商:")
    for i, broker in enumerate(brokers[:5]):  # 只显示前5个
        print(f"{i+1}. {broker}")
    print("...")
    
    # 根据ID获取券商
    broker_id = "0089"
    broker = parser.get_broker_by_id(broker_id)
    if broker:
        print(f"\n券商ID {broker_id}:")
        print(f"  名称: {broker.broker_name}")
        print(f"  英文名称: {broker.broker_ename}")
        print(f"  服务器数量: {len(broker.servers)}")
        
        # 显示该券商的所有服务器
        print("\n  服务器列表:")
        for name, server in broker.servers.items():
            print(f"    {name}:")
            print(f"      交易地址数量: {len(server.trading_items)}")
            if server.trading_items:
                print(f"      交易地址示例: {server.trading_items[0]}")
            print(f"      行情数据地址数量: {len(server.market_data_items)}")
            if server.market_data_items:
                print(f"      行情数据地址示例: {server.market_data_items[0]}")
    

if __name__ == "__main__":
    main() 
package ws_hub

import (
	"encoding/json"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"go.uber.org/zap"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
)

// OrderMessageEnvelope 定义了Order客户端与服务器之间消息的基础结构
type OrderMessageEnvelope struct {
	Event     string      `json:"event"`
	Payload   interface{} `json:"payload,omitempty"`
	Seq       *int64      `json:"seq,omitempty"`
	Timestamp int64       `json:"timestamp"`
	Source    string      `json:"source"` // 标识消息来源，如 "order_system"
}

// OrderClientState 客户端状态枚举
type OrderClientState int

const (
	StateDisconnected OrderClientState = iota // 0: 未连接
	StateConnected                            // 1: 已连接但未认证
	StateAuthenticated                        // 2: 已连接且已认证
)

// OrderClient 代表一个Order端的WebSocket连接
type OrderClient struct {
	hub           *OrderHub
	conn          *websocket.Conn
	send          chan []byte
	clientID      string
	accountID     string                // 账户ID
	userClaims    *request.CustomClaims // JWT claims
	state         OrderClientState      // 连接状态
	subscriptions map[string]bool
	mu            sync.RWMutex
	lastHeartbeat time.Time // 最后心跳时间
}

// OrderHub 负责管理所有Order客户端连接和消息广播
type OrderHub struct {
	clients    map[*OrderClient]bool
	broadcast  chan []byte
	register   chan *OrderClient
	unregister chan *OrderClient
}

// NewOrderHub 创建新的Order端WebSocket Hub
func NewOrderHub() *OrderHub {
	return &OrderHub{
		broadcast:  make(chan []byte),
		register:   make(chan *OrderClient),
		unregister: make(chan *OrderClient),
		clients:    make(map[*OrderClient]bool),
	}
}

// Run 启动Order Hub的主循环
func (h *OrderHub) Run() {
	for {
		select {
		case client := <-h.register:
			h.clients[client] = true
			global.GVA_LOG.Info("Order client connected", zap.String("clientID", client.clientID))
		case client := <-h.unregister:
			if _, ok := h.clients[client]; ok {
				delete(h.clients, client)
				close(client.send)
				global.GVA_LOG.Info("Order client disconnected", zap.String("clientID", client.clientID))
			}
		case message := <-h.broadcast:
			for client := range h.clients {
				select {
				case client.send <- message:
				default:
					close(client.send)
					delete(h.clients, client)
				}
			}
		}
	}
}

// ServeWsForOrder 处理Order端的WebSocket连接请求
func ServeWsForOrder(hub *OrderHub, c *gin.Context) {
	// 检查Origin，允许跨域连接
	upgrader := websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return true // 在生产环境中应该更严格地检查Origin
		},
	}
	
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		global.GVA_LOG.Error("Failed to upgrade Order connection", zap.Error(err))
		return
	}

	client := &OrderClient{
		hub:           hub,
		conn:          conn,
		send:          make(chan []byte, 256),
		state:         StateConnected, // 初始状态为已连接但未认证
		subscriptions: make(map[string]bool),
		lastHeartbeat: time.Now(),
	}
	client.hub.register <- client

	global.GVA_LOG.Info("Order client connected", zap.String("remote_addr", conn.RemoteAddr().String()))

	go client.writePump()
	go client.readPump()
}

func (c *OrderClient) readPump() {
	defer func() {
		c.hub.unregister <- c
		c.conn.Close()
	}()

	c.conn.SetReadLimit(maxMessageSize)
	c.conn.SetReadDeadline(time.Now().Add(pongWait))
	c.conn.SetPongHandler(func(string) error {
		c.conn.SetReadDeadline(time.Now().Add(pongWait))
		return nil
	})

	for {
		_, message, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				global.GVA_LOG.Warn("Order WebSocket unexpected close error", zap.Error(err))
			}
			break
		}
		c.handleMessageForOrder(message)
	}
}

func (c *OrderClient) writePump() {
	ticker := time.NewTicker(pingPeriod)
	defer func() {
		ticker.Stop()
		c.conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.send:
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if !ok {
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := c.conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)

			if err := w.Close(); err != nil {
				return
			}

		case <-ticker.C:
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

func (c *OrderClient) handleMessageForOrder(message []byte) {
	var msg OrderMessageEnvelope
	if err := json.Unmarshal(message, &msg); err != nil {
		global.GVA_LOG.Error("Failed to unmarshal Order message", zap.Error(err))
		c.sendErrorResponse("invalid_message", "Invalid JSON format")
		return
	}

	global.GVA_LOG.Info("Order Received Event", 
		zap.String("event", msg.Event), 
		zap.String("source", msg.Source),
		zap.String("clientID", c.clientID),
		zap.String("state", c.getStateString()))

	switch msg.Event {
	case "ping":
		c.lastHeartbeat = time.Now()
		c.sendOrderResponse("pong", map[string]interface{}{
			"server_time": time.Now().UnixMilli(),
		}, nil)
		
	case "auth":
		c.handleAuthentication(msg)
		
	case "register":
		// 只有认证通过的客户端才能注册
		if c.state != StateAuthenticated {
			c.sendErrorResponse("auth_required", "Authentication required before registration")
			return
		}
		c.handleRegistration(msg)
		
	case "order_update":
		// 只有认证通过的客户端才能处理订单
		if c.state != StateAuthenticated {
			c.sendErrorResponse("auth_required", "Authentication required")
			return
		}
		if payload, ok := msg.Payload.(map[string]interface{}); ok {
			global.GVA_LOG.Info("Order Client received order update", 
				zap.String("clientID", c.clientID), 
				zap.Any("payload", payload))
		}
		
	case "market_data":
		// 只有认证通过的客户端才能处理市场数据
		if c.state != StateAuthenticated {
			c.sendErrorResponse("auth_required", "Authentication required")
			return
		}
		if payload, ok := msg.Payload.(map[string]interface{}); ok {
			global.GVA_LOG.Info("Order Client received market data", 
				zap.String("clientID", c.clientID), 
				zap.Any("payload", payload))
		}
		
	case "subscribe_orders":
		if c.state != StateAuthenticated {
			c.sendErrorResponse("auth_required", "Authentication required")
			return
		}
		c.handleSubscription(msg, true)
		
	case "unsubscribe_orders":
		if c.state != StateAuthenticated {
			c.sendErrorResponse("auth_required", "Authentication required")
			return
		}
		c.handleSubscription(msg, false)
		
	default:
		c.sendErrorResponse("unknown_event", "Unknown event type: "+msg.Event)
	}
}

// handleAuthentication 处理客户端认证
func (c *OrderClient) handleAuthentication(msg OrderMessageEnvelope) {
	payload, ok := msg.Payload.(map[string]interface{})
	if !ok {
		c.sendErrorResponse("invalid_payload", "Invalid authentication payload")
		return
	}

	token, ok := payload["token"].(string)
	if !ok || token == "" {
		c.sendErrorResponse("missing_token", "Token is required for authentication")
		return
	}

	// 验证JWT token
	j := utils.NewJWT()
	claims, err := j.ParseToken(token)
	if err != nil {
		global.GVA_LOG.Warn("Invalid token provided", 
			zap.Error(err), 
			zap.String("remote_addr", c.conn.RemoteAddr().String()))
		c.sendErrorResponse("invalid_token", "Invalid or expired token")
		return
	}

	// 检查token是否在黑名单中
	if isBlacklist(token) {
		global.GVA_LOG.Warn("Blacklisted token provided", 
			zap.String("username", claims.Username), 
			zap.String("remote_addr", c.conn.RemoteAddr().String()))
		c.sendErrorResponse("token_blacklisted", "Token has been blacklisted")
		return
	}

	// 认证成功，更新客户端状态
	c.mu.Lock()
	c.userClaims = claims
	c.state = StateAuthenticated
	c.accountID = claims.Username // 使用username作为accountID
	c.mu.Unlock()

	global.GVA_LOG.Info("Order client authenticated", 
		zap.String("username", claims.Username),
		zap.Uint("userID", claims.BaseClaims.ID),
		zap.String("remote_addr", c.conn.RemoteAddr().String()))

	// 发送认证成功响应
	c.sendOrderResponse("auth_response", map[string]interface{}{
		"success":    true,
		"username":   claims.Username,
		"user_id":    claims.BaseClaims.ID,
		"expires_at": claims.ExpiresAt.Unix(),
	}, nil)
}

// handleRegistration 处理客户端注册
func (c *OrderClient) handleRegistration(msg OrderMessageEnvelope) {
	payload, ok := msg.Payload.(map[string]interface{})
	if !ok {
		c.sendErrorResponse("invalid_payload", "Invalid registration payload")
		return
	}

	clientID, ok := payload["clientID"].(string)
	if !ok || clientID == "" {
		c.sendErrorResponse("missing_client_id", "Client ID is required")
		return
	}

	c.mu.Lock()
	c.clientID = clientID
	c.mu.Unlock()

	global.GVA_LOG.Info("Order client registered", 
		zap.String("clientID", clientID),
		zap.String("username", c.userClaims.Username))

	c.sendOrderResponse("register_response", map[string]interface{}{
		"success":  true,
		"clientID": clientID,
	}, nil)
}

// handleSubscription 处理订阅/取消订阅
func (c *OrderClient) handleSubscription(msg OrderMessageEnvelope, subscribe bool) {
	payload, ok := msg.Payload.(map[string]interface{})
	if !ok {
		c.sendErrorResponse("invalid_payload", "Invalid subscription payload")
		return
	}

	channel, ok := payload["channel"].(string)
	if !ok || channel == "" {
		c.sendErrorResponse("missing_channel", "Channel is required")
		return
	}

	c.mu.Lock()
	if subscribe {
		c.subscriptions[channel] = true
		global.GVA_LOG.Info("Order Client subscribed", 
			zap.String("clientID", c.clientID), 
			zap.String("channel", channel))
	} else {
		if _, exists := c.subscriptions[channel]; exists {
			delete(c.subscriptions, channel)
			global.GVA_LOG.Info("Order Client unsubscribed", 
				zap.String("clientID", c.clientID), 
				zap.String("channel", channel))
		} else {
			global.GVA_LOG.Warn("Attempted to unsubscribe from non-existent channel", 
				zap.String("clientID", c.clientID), 
				zap.String("channel", channel))
		}
	}
	c.mu.Unlock()

	eventType := "unsubscribe_response"
	if subscribe {
		eventType = "subscribe_response"
	}

	c.sendOrderResponse(eventType, map[string]interface{}{
		"success": true,
		"channel": channel,
	}, nil)
}

// sendErrorResponse 发送错误响应
func (c *OrderClient) sendErrorResponse(errorType, message string) {
	c.sendOrderResponse("error", map[string]interface{}{
		"error_type": errorType,
		"message":    message,
	}, nil)
}

// getStateString 获取状态字符串表示
func (c *OrderClient) getStateString() string {
	switch c.state {
	case StateDisconnected:
		return "disconnected"
	case StateConnected:
		return "connected"
	case StateAuthenticated:
		return "authenticated"
	default:
		return "unknown"
	}
}

// isBlacklist 检查token是否在黑名单中
func isBlacklist(jwt string) bool {
	_, ok := global.BlackCache.Get(jwt)
	return ok
}

func (c *OrderClient) sendOrderResponse(event string, payload interface{}, seq *int64) {
	resp := OrderMessageEnvelope{
		Event:     event,
		Payload:   payload,
		Seq:       seq,
		Timestamp: time.Now().UnixMilli(),
		Source:    "order_hub",
	}
	msgBytes, _ := json.Marshal(resp)
	c.send <- msgBytes
}

// BroadcastToOrder 向所有Order客户端广播消息
func (h *OrderHub) BroadcastToOrder(event string, payload interface{}) {
	msg := OrderMessageEnvelope{
		Event:     event,
		Payload:   payload,
		Timestamp: time.Now().UnixMilli(),
		Source:    "order_hub",
	}
	msgBytes, _ := json.Marshal(msg)
	h.broadcast <- msgBytes
}

// GetOrderClientCount 获取当前Order客户端连接数
func (h *OrderHub) GetOrderClientCount() int {
	return len(h.clients)
}

// GetAuthenticatedClientCount 获取已认证的客户端数量
func (h *OrderHub) GetAuthenticatedClientCount() int {
	count := 0
	for client := range h.clients {
		if client.state == StateAuthenticated {
			count++
		}
	}
	return count
}

// GetClientInfo 获取所有客户端信息
func (h *OrderHub) GetClientInfo() []map[string]interface{} {
	var clients []map[string]interface{}
	for client := range h.clients {
		clientInfo := map[string]interface{}{
			"client_id":      client.clientID,
			"account_id":     client.accountID,
			"state":          client.getStateString(),
			"remote_addr":    client.conn.RemoteAddr().String(),
			"last_heartbeat": client.lastHeartbeat.Format("2006-01-02 15:04:05"),
			"subscriptions":  client.getSubscriptionList(),
		}
		if client.userClaims != nil {
			clientInfo["username"] = client.userClaims.Username
			clientInfo["user_id"] = client.userClaims.BaseClaims.ID
		}
		clients = append(clients, clientInfo)
	}
	return clients
}

// getSubscriptionList 获取订阅列表
func (c *OrderClient) getSubscriptionList() []string {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	subscriptions := make([]string, 0, len(c.subscriptions))
	for channel := range c.subscriptions {
		subscriptions = append(subscriptions, channel)
	}
	return subscriptions
}

// IsAuthenticated 检查客户端是否已认证
func (c *OrderClient) IsAuthenticated() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.state == StateAuthenticated
}

// GetAccountID 获取账户ID
func (c *OrderClient) GetAccountID() string {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.accountID
}

// SendToSpecificOrderClient 向特定的Order客户端发送消息
func (h *OrderHub) SendToSpecificOrderClient(clientID string, event string, payload interface{}) {
	msg := OrderMessageEnvelope{
		Event:     event,
		Payload:   payload,
		Timestamp: time.Now().UnixMilli(),
		Source:    "order_hub",
	}
	msgBytes, _ := json.Marshal(msg)

	for client := range h.clients {
		if client.clientID == clientID {
			select {
			case client.send <- msgBytes:
				log.Printf("Message sent to Order client %s", clientID)
			default:
				log.Printf("Failed to send message to Order client %s", clientID)
			}
			break
		}
	}
}

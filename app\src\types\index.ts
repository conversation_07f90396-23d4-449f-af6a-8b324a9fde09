/**
 * 类型定义统一导出
 * 提供项目中所有类型的统一入口
 */

// 用户相关类型
export type {
  IUser,
  IUserInfoVo,
  IUserLogin,
  ILoginResponse,
  IUpdateProfileRequest,
  IUploadSuccessInfo,
  IUpdateInfo,
  IUpdatePassword,
  IUserSelectable
} from './user'

// 认证相关类型
export type {
  ISendCodeRequest,
  IPhoneLoginRequest,
  IUsernameLoginRequest,
  IWechatLoginRequest,
  ICaptchaResponse,
} from './auth'

// 商品相关类型
export type {
  ICommodity,
  ICommodityRequest,
  ICommodityListRequest,
  ICommodityListResponse,
} from './commodity'

// 期货合约相关类型
export type {
  IInstrument,
  IInstrumentRequest,
  IInstrumentListRequest,
  IInstrumentListResponse,
  IInstrumentSelectRequest,
  IInstrumentSelectItem,
  IInstrumentSelectorProps,
  IInstrumentSelectorEmits,
  IInstrumentsByExchange,
} from './instrument'

// 合同相关类型
export type {
  ContractStatus,
  ContractPriceType,
  IContract,
  IContractResponse,
  ICreateContractRequest,
  IUpdateContractRequest,
  IContractListRequest,
  IContractListResponse,
  IContractDetailParams,
  UserRole,
  // 重新导出的交易相关类型
  TradeRequestType,
  TradeRequestStatus,
  ExecutionType,
  ExecutionStatus,
  ITradeRequest,
  IExecutionDetail,
  IExecutionSummary,
  ICreateTradeRequestRequest,
  ITradeRequestListRequest,
  IExecutionDetailListRequest,
} from './contract'

// 交易请求相关类型
export type {
  ITradeRequest as ITradeRequestCore,
  ITradeRequestResponse,
  ITradeRequestListResponse,
  IUpdateTradeRequestRequest,
  ITodayTradeRequestsRequest,
} from './trade-request'

// 执行明细相关类型
export type {
  IExecutionDetail as IExecutionDetailCore,
  IExecutionDetailResponse,
  IExecutionDetailListResponse,
  ICreateExecutionDetailRequest,
  IUpdateExecutionDetailRequest,
} from './execution-detail'

// 报价相关类型
export type {
  QuotationStatus,
  QuotationPriceType,
  IQuotation,
  IQuotationResponse,
  ICreateQuotationRequest,
  IUpdateQuotationRequest,
  IPublishQuotationRequest,
  IQuotationListRequest,
  IQuotationListResponse,
  IQuotationExpiryOption,
} from './quotation'

// 常量导出
export {
  ProductClassMap,
  InstLifePhaseMap,
  ExchangeMap,
} from './instrument'
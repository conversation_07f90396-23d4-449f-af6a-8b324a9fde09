package response

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
)

type SysUserResponse struct {
	User system.SysUser `json:"user"`
}

type LoginResponse struct {
	User      system.SysUser `json:"user"`
	Token     string         `json:"token"`
	ExpiresAt int64          `json:"expiresAt"`
}

type SysUserSelectableResponse struct {
	ID       uint   `json:"ID"`
	NickName string `json:"nickName"`
	Phone    string `json:"phone"`
}

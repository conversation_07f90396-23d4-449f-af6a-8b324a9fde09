"""
合约信息获取模块
负责从OpenCTP API获取期货合约信息
"""
import requests
import json
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
import asyncio
import aiohttp


@dataclass
class InstrumentInfo:
    """合约信息数据类"""
    ExchangeID: str  # 交易所ID
    InstrumentID: str  # 合约ID
    ProductID: str  # 产品ID
    ProductClass: str  # 产品类别
    LastPrice: Optional[float] = None  # 最新价
    Volume: Optional[int] = None  # 成交量
    Turnover: Optional[float] = None  # 成交额
    OpenInterest: Optional[int] = None  # 持仓量
    OpenPrice: Optional[float] = None  # 开盘价
    HighestPrice: Optional[float] = None  # 最高价
    LowestPrice: Optional[float] = None  # 最低价
    ClosePrice: Optional[float] = None  # 收盘价
    AveragePrice: Optional[float] = None  # 均价
    SettlementPrice: Optional[float] = None  # 结算价
    PreClosePrice: Optional[float] = None  # 昨收价
    PreSettlementPrice: Optional[float] = None  # 昨结算
    UpperLimitPrice: Optional[float] = None  # 涨停价
    LowerLimitPrice: Optional[float] = None  # 跌停价
    UpdateTime: Optional[str] = None  # 更新时间
    UpdateDate: Optional[str] = None  # 更新日期
    TradingDay: Optional[str] = None  # 交易日

    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'symbol': self.InstrumentID,  # 兼容旧系统的字段名
            'exchange': self.ExchangeID,  # 兼容旧系统的字段名
            'ExchangeID': self.ExchangeID,
            'InstrumentID': self.InstrumentID,
            'ProductID': self.ProductID,
            'ProductClass': self.ProductClass,
            'LastPrice': self.LastPrice,
            'Volume': self.Volume,
            'Turnover': self.Turnover,
            'OpenInterest': self.OpenInterest,
            'OpenPrice': self.OpenPrice,
            'HighestPrice': self.HighestPrice,
            'LowestPrice': self.LowestPrice,
            'ClosePrice': self.ClosePrice,
            'AveragePrice': self.AveragePrice,
            'SettlementPrice': self.SettlementPrice,
            'PreClosePrice': self.PreClosePrice,
            'PreSettlementPrice': self.PreSettlementPrice,
            'UpperLimitPrice': self.UpperLimitPrice,
            'LowerLimitPrice': self.LowerLimitPrice,
            'UpdateTime': self.UpdateTime,
            'UpdateDate': self.UpdateDate,
            'TradingDay': self.TradingDay
        }

    @classmethod
    def from_dict(cls, data: Dict) -> 'InstrumentInfo':
        """从字典创建实例"""
        return cls(
            ExchangeID=data.get('ExchangeID', ''),
            InstrumentID=data.get('InstrumentID', ''),
            ProductID=data.get('ProductID', ''),
            ProductClass=data.get('ProductClass', ''),
            LastPrice=data.get('LastPrice'),
            Volume=data.get('Volume'),
            Turnover=data.get('Turnover'),
            OpenInterest=data.get('OpenInterest'),
            OpenPrice=data.get('OpenPrice'),
            HighestPrice=data.get('HighestPrice'),
            LowestPrice=data.get('LowestPrice'),
            ClosePrice=data.get('ClosePrice'),
            AveragePrice=data.get('AveragePrice'),
            SettlementPrice=data.get('SettlementPrice'),
            PreClosePrice=data.get('PreClosePrice'),
            PreSettlementPrice=data.get('PreSettlementPrice'),
            UpperLimitPrice=data.get('UpperLimitPrice'),
            LowerLimitPrice=data.get('LowerLimitPrice'),
            UpdateTime=data.get('UpdateTime'),
            UpdateDate=data.get('UpdateDate'),
            TradingDay=data.get('TradingDay')
        )


class InstrumentFetcher:
    """合约信息获取器"""
    
    def __init__(self, api_url: str = "http://dict.openctp.cn/prices?types=futures", 
                 timeout: int = 30):
        """
        初始化合约信息获取器
        
        Args:
            api_url: OpenCTP API地址
            timeout: 请求超时时间（秒）
        """
        self.api_url = api_url
        self.timeout = timeout
    
    def fetch_instruments_sync(self) -> Tuple[List[InstrumentInfo], Optional[str]]:
        """
        同步方式获取合约信息
        
        Returns:
            tuple: (合约信息列表, 错误信息)
        """
        try:
            print(f"正在从API获取合约信息: {self.api_url}")
            response = requests.get(self.api_url, timeout=self.timeout)
            response.raise_for_status()
            
            data = response.json()
            
            # 检查API响应格式
            if data.get('rsp_code') != 0:
                error_msg = f"API返回错误: {data.get('rsp_message', '未知错误')}"
                print(f"错误: {error_msg}")
                return [], error_msg
            
            # 解析合约数据
            instruments_data = data.get('data', [])
            instruments = []
            
            for item in instruments_data:
                try:
                    instrument = InstrumentInfo.from_dict(item)
                    instruments.append(instrument)
                except Exception as e:
                    print(f"警告: 解析合约数据失败: {item}, 错误: {e}")
                    continue
            
            print(f"成功获取 {len(instruments)} 个合约信息")
            return instruments, None
            
        except requests.exceptions.Timeout:
            error_msg = f"请求超时 ({self.timeout}秒)"
            print(f"错误: {error_msg}")
            return [], error_msg
        except requests.exceptions.ConnectionError:
            error_msg = "网络连接失败"
            print(f"错误: {error_msg}")
            return [], error_msg
        except requests.exceptions.HTTPError as e:
            error_msg = f"HTTP错误: {e}"
            print(f"错误: {error_msg}")
            return [], error_msg
        except json.JSONDecodeError:
            error_msg = "API返回的数据格式不正确"
            print(f"错误: {error_msg}")
            return [], error_msg
        except Exception as e:
            error_msg = f"未知错误: {str(e)}"
            print(f"错误: {error_msg}")
            return [], error_msg
    
    async def fetch_instruments_async(self) -> Tuple[List[InstrumentInfo], Optional[str]]:
        """
        异步方式获取合约信息
        
        Returns:
            tuple: (合约信息列表, 错误信息)
        """
        try:
            print(f"正在异步获取合约信息: {self.api_url}")
            
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(self.api_url) as response:
                    response.raise_for_status()
                    data = await response.json()
            
            # 检查API响应格式
            if data.get('rsp_code') != 0:
                error_msg = f"API返回错误: {data.get('rsp_message', '未知错误')}"
                print(f"错误: {error_msg}")
                return [], error_msg
            
            # 解析合约数据
            instruments_data = data.get('data', [])
            instruments = []
            
            for item in instruments_data:
                try:
                    instrument = InstrumentInfo.from_dict(item)
                    instruments.append(instrument)
                except Exception as e:
                    print(f"警告: 解析合约数据失败: {item}, 错误: {e}")
                    continue
            
            print(f"成功异步获取 {len(instruments)} 个合约信息")
            return instruments, None
            
        except asyncio.TimeoutError:
            error_msg = f"异步请求超时 ({self.timeout}秒)"
            print(f"错误: {error_msg}")
            return [], error_msg
        except aiohttp.ClientConnectionError:
            error_msg = "网络连接失败"
            print(f"错误: {error_msg}")
            return [], error_msg
        except aiohttp.ClientResponseError as e:
            error_msg = f"HTTP错误: {e}"
            print(f"错误: {error_msg}")
            return [], error_msg
        except json.JSONDecodeError:
            error_msg = "API返回的数据格式不正确"
            print(f"错误: {error_msg}")
            return [], error_msg
        except Exception as e:
            error_msg = f"未知错误: {str(e)}"
            print(f"错误: {error_msg}")
            return [], error_msg
    
    def get_instruments_list(self, instruments: List[InstrumentInfo]) -> List[Tuple[str, str]]:
        """
        将合约信息转换为旧系统兼容的格式
        
        Args:
            instruments: 合约信息列表
            
        Returns:
            list: [(symbol, exchange), ...] 格式的合约列表
        """
        return [(inst.InstrumentID, inst.ExchangeID) for inst in instruments]


# 示例用法
async def main():
    """示例用法"""
    fetcher = InstrumentFetcher()
    
    # 同步获取
    print("=== 同步获取合约信息 ===")
    instruments, error = fetcher.fetch_instruments_sync()
    if error:
        print(f"同步获取失败: {error}")
    else:
        print(f"同步获取成功，共 {len(instruments)} 个合约")
        if instruments:
            print(f"示例合约: {instruments[0].InstrumentID} ({instruments[0].ExchangeID})")
    
    # 异步获取
    print("\n=== 异步获取合约信息 ===")
    instruments, error = await fetcher.fetch_instruments_async()
    if error:
        print(f"异步获取失败: {error}")
    else:
        print(f"异步获取成功，共 {len(instruments)} 个合约")
        if instruments:
            print(f"示例合约: {instruments[0].InstrumentID} ({instruments[0].ExchangeID})")
        
        # 转换为旧格式
        instrument_list = fetcher.get_instruments_list(instruments)
        print(f"兼容格式: {instrument_list[:3]}...")


if __name__ == "__main__":
    asyncio.run(main())
"""
登录功能测试脚本

用于测试登录UI和功能的独立脚本。
"""

import sys
import logging
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent.parent
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

from PySide6.QtWidgets import QApplication
from vnpy.event import EventEngine

# 导入认证和UI模块
from auth import LoginManager
from ui import LoginWindow


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )


def test_login_window():
    """测试登录窗口"""
    logger = logging.getLogger(__name__)
    logger.info("开始测试登录窗口")
    
    # 创建QApplication
    app = QApplication(sys.argv)
    app.setApplicationName("Order登录测试")
    
    # 创建事件引擎
    event_engine = EventEngine()
    event_engine.start()
    
    try:
        # 创建登录管理器
        login_manager = LoginManager(event_engine)
        
        # 创建登录窗口
        login_window = LoginWindow(event_engine, login_manager)
        
        # 连接信号
        def on_login_success(result):
            logger.info(f"登录成功：{result.user_info}")
            app.quit()
        
        def on_login_cancelled():
            logger.info("用户取消登录")
            app.quit()
        
        login_window.login_success.connect(on_login_success)
        login_window.login_cancelled.connect(on_login_cancelled)
        
        # 显示登录窗口
        login_window.show_centered()
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        logger.error(f"测试过程中发生错误：{str(e)}")
        return 1
    
    finally:
        # 停止事件引擎
        event_engine.stop()
        logger.info("测试结束")


def main():
    """主函数"""
    setup_logging()
    return test_login_window()


if __name__ == "__main__":
    sys.exit(main())

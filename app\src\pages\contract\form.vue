<script lang="ts" setup>
import type {
  ICreateContractRequest,
  IUpdateContractRequest,
  ContractPriceType,
} from '@/types/contract'
import type { IInstrumentSelectItem } from '@/types/instrument'
import type { IUser } from '@/types/user'
import { onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  createContract,
  getContractDetail,
  updateContract,
} from '@/api/contract'
import InstrumentSelector from '@/components/InstrumentSelector.vue'
import UserSelector from '@/components/UserSelector.vue'

const router = useRouter()
const route = useRoute()

// 响应式数据
const formRef = ref()
const isEdit = ref(false)
const contractId = ref<number>(0)
const submitLoading = ref(false)

// 表单数据
const formData = reactive<ICreateContractRequest>({
  contractCode: '',
  pricerID: 0,
  instrumentRefID: 0,
  instrument: null,
  remarks: '',
  totalQuantity: 0,
  priceType: 'basis',
  priceValue: 0,
})

// 用户选择相关
const selectedPricer = ref<IUser | null>(null)

// 表单验证规则
const formRules = reactive({
  contractCode: [
    { required: true, message: '请输入合同编码' },
  ],
  totalQuantity: [
    { required: true, message: '请输入合同总数量', trigger: 'blur' },
    { pattern: /^(0|[1-9]\d*)$/ as RegExp, message: '数量必须为非负整数', trigger: 'blur' },
  ],
  priceValue: [
    { required: true, message: '请输入价格/基差值', trigger: 'blur' },
  ],
} as any)

// 方法
async function loadContractDetail() {
  if (!contractId.value)
    return

  try {
    const response = await getContractDetail(contractId.value)

    if (response.code === 0) {
      const contract = response.data

      // 检查合同状态，只有 Unexecuted 状态的合同才能编辑
      if (contract.status !== 'Unexecuted') {
        uni.showToast({
          title: '只有未执行状态的合同才能编辑',
          icon: 'error',
        })
        setTimeout(() => {
          router.back()
        }, 1500)
        return
      }

      // 确保 priceType 是字符串类型
      let priceType: 'basis' | 'fixed' = 'basis'
      if (typeof contract.priceType === 'string') {
        priceType = contract.priceType as 'basis' | 'fixed'
      } else if (typeof contract.priceType === 'object' && contract.priceType && 'value' in contract.priceType) {
        // 如果 priceType 是对象，尝试提取 value 属性
        priceType = (contract.priceType as any).value as 'basis' | 'fixed'
      }

      // 填充表单数据
      Object.assign(formData, {
        contractCode: contract.contractCode,
        pricerID: contract.pricerID,
        instrumentRefID: contract.instrumentRefID,
        instrument: contract.instrument || null,
        remarks: contract.remarks,
        totalQuantity: contract.totalQuantity,
        priceType: priceType,
        priceValue: contract.priceValue,
      })

      selectedPricer.value = contract.pricer

      // 如果有点价方信息，触发 UserSelector 组件的用户变化事件
      if (contract.pricer) {
        onUserChange(contract.pricer)
      }
    }
    else {
      uni.showToast({
        title: response.msg || '获取合同详情失败',
        icon: 'error',
      })
    }
  }
  catch (error) {
    console.error('获取合同详情失败:', error)
    uni.showToast({
      title: '网络错误',
      icon: 'error',
    })
  }
}

function onPriceTypeChange(value: any) {
  // 正确提取字符串值
  let newPriceType: 'basis' | 'fixed'

  if (typeof value === 'string') {
    newPriceType = value as 'basis' | 'fixed'
  } else if (typeof value === 'object' && value && 'value' in value) {
    newPriceType = value.value as 'basis' | 'fixed'
  } else {
    newPriceType = 'basis' // 默认值
  }

  formData.priceType = newPriceType
}

// 处理用户选择变化
function onUserChange(user: IUser | null) {
  selectedPricer.value = user
  formData.pricerID = user ? user.ID : 0
}

// 处理期货合约选择变化
function onInstrumentChange(selectedInstrumentId: number, instrumentData?: IInstrumentSelectItem | null) {
  formData.instrumentRefID = selectedInstrumentId || 0
  formData.instrument = instrumentData || null
}

async function submitForm() {
  // 表单验证
  const valid = await formRef.value.validate()
  if (!valid.valid)
    return

  if (!formData.instrumentRefID ) {
    uni.showToast({
      title: '请选择期货合约',
      icon: 'error',
    })
    return
  }

  if (!formData.totalQuantity || formData.totalQuantity <= 0) {
    uni.showToast({
      title: '请输入有效的合同数量',
      icon: 'error',
    })
    return
  }

  if (!formData.priceValue || formData.priceValue === 0) {
    uni.showToast({
      title: '请输入有效的价格/基差值',
      icon: 'error',
    })
    return
  }

  if (!formData.pricerID) {
    uni.showToast({
      title: '请选择点价方',
      icon: 'error',
    })
    return
  }

  submitLoading.value = true

  try {
    let response: IResData<any>

    // 准备提交数据，排除前端辅助字段
    const { instrument, ...submitData } = formData

    if (isEdit.value) {
      const updateData: IUpdateContractRequest = {
        id: contractId.value,
        ...submitData,
      }
      response = await updateContract(updateData)
    }
    else {
      // 新创建的合同状态默认为 Unexecuted（由后端处理）
      response = await createContract(submitData)
    }

    if (response.code === 0) {
      uni.showToast({
        title: isEdit.value ? '更新成功' : '创建成功',
        icon: 'success',
      })

      setTimeout(() => {
        router.back()
      }, 1500)
    }
    else {
      uni.showToast({
        title: response.msg || (isEdit.value ? '更新失败' : '创建失败'),
        icon: 'error',
      })
    }
  }
  catch (error) {
    console.error('提交合同失败:', error)
    uni.showToast({
      title: '网络错误',
      icon: 'error',
    })
  }
  finally {
    submitLoading.value = false
  }
}

function goBack() {
  router.back()
}

// 生命周期
onMounted(() => {
  // 检查是否为编辑模式
  const id = route.query.id
  if (id) {
    contractId.value = Number(id)
    isEdit.value = true
    loadContractDetail()
  }
})
</script>

<template>
  <view class="contract-form-page">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">
        {{ isEdit ? '编辑合同' : '创建合同' }}
      </text>
    </view>

    <!-- 表单内容 -->
    <view class="form-container">
      <wd-form ref="formRef" :model="formData" :rules="formRules">

                <!-- 参与方设置 -->
        <view class="form-section">
          <view class="section-title">
            参与方设置
          </view>

          <UserSelector
            v-model="formData.pricerID"
            label="选择点价方"
            placeholder="暂未选择点价方"
            @change="onUserChange"
          />

            <InstrumentSelector
              v-model="formData.instrumentRefID" label="期货合约" placeholder="请输入期货合约"
              custom-class="dj-form-field" custom-label-class="dj-form-label" custom-value-class="dj-form-input"
              @change="onInstrumentChange"
            />
        </view>
        <!-- 基本信息 -->
        <view class="form-section">
          <view class="section-title">
            基本信息
          </view>

          <view class="form-item-container">
            <wd-input
              v-model="formData.contractCode" label="合同编码" placeholder="请输入合同编码" required prop="contractCode"
              custom-class="dj-form-field" custom-label-class="dj-form-label" custom-input-class="dj-form-input"
            />


            <wd-input
              v-model.number="formData.totalQuantity" label="合同总数量" type="number" placeholder="请输入合同总数量"
              prop="totalQuantity" custom-class="dj-form-field" custom-label-class="dj-form-label"
              custom-input-class="dj-form-input"
            />

            <view class="price-type-selector">
              <view class="price-type-tabs">
                <wd-radio-group v-model="formData.priceType" @change="onPriceTypeChange">
                  <wd-radio value="basis">基差合同（点价）</wd-radio>
                  <wd-radio value="fixed">固定价合同（洗基差）</wd-radio>
                </wd-radio-group>
              </view>
            </view>

            <wd-input
              v-model.number="formData.priceValue" :label="formData.priceType === 'basis' ? '基差值' : '固定价格'" 
              type="number" :placeholder="formData.priceType === 'basis' ? '请输入基差值' : '请输入固定价格'"
              prop="priceValue" custom-class="dj-form-field" custom-label-class="dj-form-label"
              custom-input-class="dj-form-input"
            />

            <wd-textarea
              v-model="formData.remarks" label="备注" placeholder="请输入备注信息" :maxlength="500"
              :auto-height="true" custom-class="dj-form-textarea" custom-label-class="dj-form-label"
              custom-textarea-class="dj-form-input" custom-textarea-container-class="dj-form-textarea-container"
            />
          </view>
        </view>



      </wd-form>
    </view>

    <!-- 底部操作按钮 -->
    <view class="form-actions">
      <wd-button type="info" custom-class="dj-btn-secondary" @click="goBack">
        取消
      </wd-button>
      <wd-button type="primary" custom-class="dj-btn-primary" :loading="submitLoading" @click="submitForm">
        {{ isEdit ? '更新合同' : '创建合同' }}
      </wd-button>
    </view>


  </view>
</template>

<route lang="jsonc" type="page">
{
  "style": {
    "navigationBarTitleText": "合同表单"
  }
}
</route>

<style lang="scss" scoped>
// 基础变量
$primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$primary-color: #667eea;
$secondary-color: #764ba2;
$text-primary: #303133;
$text-secondary: #606266;
$text-light: #909399;
$border-color: #e0e3ea;
$box-shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
$box-shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
$transition-base: all 0.3s ease;
$font-size-large: 32rpx;
$font-size-medium: 28rpx;
$font-size-small: 24rpx;
$font-size-title: 36rpx;
$font-size-page-title: 40rpx;

// 页面基本布局
.contract-form-page {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 120rpx;
}

.page-header {
  padding: 30rpx 20rpx;
  background: $primary-gradient;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 20rpx;

  .page-title {
    font-size: $font-size-page-title;
    font-weight: bold;
    color: #ffffff;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  }
}

.form-container {
  padding: 20rpx;
}

// 表单分区
.form-section {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

  .section-title {
    font-size: $font-size-title;
    font-weight: bold;
    color: $text-primary;
    margin-bottom: 30rpx;
    padding-bottom: 16rpx;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 80rpx;
      height: 4rpx;
      background: linear-gradient(90deg, $primary-color, $secondary-color);
      border-radius: 2rpx;
    }
  }
}

// 价格类型选择器
.price-type-selector {
  margin-bottom: 30rpx;

  .selector-label {
    display: block;
    font-size: $font-size-large;
    color: $text-primary;
    font-weight: 500;
    margin-bottom: 20rpx;
  }

  .price-type-tabs {
    background-color: #f5f7fa;
    border-radius: 12rpx;
    padding: 8rpx;
    box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.06);

    :deep(.wd-radio-group) {
      display: flex;
      flex-direction: row;
      gap: 0;
      width: 100%;
    }

    :deep(.wd-radio) {
      flex: 1;
      margin-bottom: 0;
      position: relative;

      .wd-radio__input {
        display: none;
      }

      .wd-radio__label {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20rpx 16rpx;
        border-radius: 8rpx;
        background-color: transparent;
        color: #606266;
        font-size: $font-size-medium;
        font-weight: 500;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
        min-height: 80rpx;
        box-sizing: border-box;
        border: 2rpx solid transparent;

        &:hover {
          color: $primary-color;
          background-color: rgba(102, 126, 234, 0.08);
        }
      }

      &.is-checked {
        .wd-radio__label {
          background-color: white;
          color: $primary-color;
          font-weight: 600;
          border-color: rgba(102, 126, 234, 0.2);
          box-shadow:
            0 2rpx 8rpx rgba(102, 126, 234, 0.15),
            0 1rpx 3rpx rgba(0, 0, 0, 0.1);
          transform: translateY(-1rpx);
        }
      }

      // 移除原有的单选按钮样式
      .wd-radio__shape {
        display: none;
      }
    }
  }
}



// 底部操作按钮区
.form-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background: white;
  display: flex;
  gap: 20rpx;
  z-index: 10;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);

  :deep(.wd-button) {
    flex: 1;
  }
}



// 统一表单组件样式
:deep() {
  // 表单字段容器基本样式
  .dj-form-field,
  .dj-form-textarea {
    background: white;
    border-radius: 8rpx;
    transition: $transition-base;
    position: relative;
    overflow: hidden;

    &:hover {
      background-color: #f9fafc;
    }

    &:focus-within {
      background-color: #f9fafc;
      box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
    }

    &.is-disabled {
      background-color: #f5f7fa;
      cursor: not-allowed;
    }

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 4rpx;
      background: linear-gradient(180deg, $primary-color, $secondary-color);
    }
  }

  // 表单标签
  .dj-form-label {
    color: $text-primary;
    text-align: right;
    justify-content: flex-end;
    width: 200rpx;
    padding-right: 24rpx;
    font-size: $font-size-medium;
  }

  // 表单输入
  .dj-form-input {
    color: $text-primary;
    font-size: $font-size-medium;

    &.is-placeholder {
      color: $text-light;
      font-style: italic;
    }

    &:focus {
      color: $primary-color;
    }
  }

  // 文本区容器
  .dj-form-textarea-container {
    width: 100%;
  }

  // 按钮样式基础
  %button-base {
    border-radius: 8rpx;
    font-weight: 500;
    font-size: $font-size-large;
    transition: $transition-base;
    padding: 20rpx;

    &:hover {
      opacity: 0.9;
    }

    &:active {
      opacity: 1;
    }
  }

  // 主按钮
  .dj-btn-primary {
    @extend %button-base;
    background: $primary-gradient;
    border: none;
    color: white;
    box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.2);

    &:hover {
      box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
      opacity: 0.95;
    }

    &:active {
      box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.2);
      opacity: 1;
    }

    &.is-loading {
      background: linear-gradient(135deg, #a5b0f3 0%, #a586c0 100%);
      opacity: 0.8;
    }
  }

  // 次按钮
  .dj-btn-secondary {
    @extend %button-base;
    background: white;
    color: $text-secondary;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);

    &:hover {
      color: $text-primary;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
    }

    &:active {
      box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
    }
  }

  // 危险按钮
  .dj-btn-danger {
    @extend %button-base;
    background: linear-gradient(135deg, #f56c6c 0%, #e64242 100%);
    border: none;
    color: white;
    box-shadow: 0 2rpx 8rpx rgba(245, 108, 108, 0.2);

    &:hover {
      box-shadow: 0 4rpx 12rpx rgba(245, 108, 108, 0.3);
      opacity: 0.95;
    }

    &:active {
      box-shadow: 0 2rpx 8rpx rgba(245, 108, 108, 0.2);
      opacity: 1;
    }
  }

  // 复选框和单选框的共同样式
  %checkbox-radio-common {
    .wd-checkbox__label,
    .wd-radio__label {
      color: $text-primary;
      font-size: $font-size-medium;
      font-weight: 500;
      padding-left: 12rpx;
    }

    .wd-checkbox__shape,
    .wd-radio__shape {
      border-color: #c0c4cc;
      transition: $transition-base;

      &.is-checked {
        background-color: $primary-color;
        border-color: $primary-color;
        box-shadow: 0 0 4rpx rgba(102, 126, 234, 0.3);
      }
    }
  }

  // 复选框
  .dj-checkbox {
    @extend %checkbox-radio-common;

    .wd-checkbox__shape {
      border-radius: 4rpx;
    }
  }

  // 单选框
  .dj-radio {
    @extend %checkbox-radio-common;
  }



  // 搜索框
  .dj-search {
    background: white;
    border-radius: 40rpx;
    padding: 0 16rpx;
    box-shadow: $box-shadow-sm;
    transition: $transition-base;

    &:focus-within {
      box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
    }

    .wd-search__input {
      background: transparent;
      color: $text-primary;
      font-size: $font-size-medium;
      height: 80rpx;
    }

    .wd-search__icon {
      color: $primary-color;
    }

    .wd-search__placeholder {
      color: $text-light;
      font-size: $font-size-medium;
    }
  }

  // 弹窗
  .dj-popup {
    .wd-popup__container {
      border-radius: 16rpx 16rpx 0 0;
      background: #ffffff;
      box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
      overflow: hidden;
    }
  }
}
</style>

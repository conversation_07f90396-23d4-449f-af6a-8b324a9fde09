package router

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/ws_hub"
	"github.com/gin-gonic/gin"
)

func InitWsRouter(Router *gin.RouterGroup) {
	wsRouter := Router.Group("ws")
	{
		// App 端 WebSocket 路由
		wsRouter.GET("/app", func(c *gin.Context) {
			if ws_hub.GLOBAL_APP_HUB == nil {
				global.GVA_LOG.Error("App WebSocket Hub not initialized")
				c.<PERSON>(500, gin.H{"error": "App WebSocket Hub not initialized"})
				return
			}
			ws_hub.ServeWsForApp(ws_hub.GLOBAL_APP_HUB, c)
		})

		// Order 端 WebSocket 路由
		wsRouter.GET("/order", func(c *gin.Context) {
			if ws_hub.GLOBAL_ORDER_HUB == nil {
				global.GVA_LOG.Error("Order WebSocket Hub not initialized")
				c.JSO<PERSON>(500, gin.H{"error": "Order WebSocket Hub not initialized"})
				return
			}
			ws_hub.ServeWsForOrder(ws_hub.GLOBAL_ORDER_HUB, c)
		})
	}
}

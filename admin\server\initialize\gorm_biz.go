package initialize

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/dianjia"
)

func bizModel() error {
	db := global.GVA_DB
	err := db.AutoMigrate(
		dianjia.Contract{},
		dianjia.TradeRequest{},
		dianjia.ExecutionDetail{},
		dianjia.Commodity{},
		dianjia.Instrument{},
		dianjia.ContractCancelRecord{},
		dianjia.Quotation{},
	)
	if err != nil {
		return err
	}
	return nil
}

<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "报价详情",
    "navigationStyle": "default"
  }
}
</route>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useUserStore } from '@/store/user'
import {
  getQuotationDetail,
  publishQuotation,
  toggleQuotationStatus,
  deleteQuotation
} from '@/api/quotation'
import type { 
  IQuotationResponse,
  QuotationStatus,
  QUOTATION_STATUS_CONFIG
} from '@/types/quotation'

defineOptions({
  name: 'QuotationDetail'
})
function goBack() {
  uni.navigateBack()
}
// Store
const userStore = useUserStore()

// 页面状态
const quotationId = ref<number>()
const isLoading = ref(false)
const quotation = ref<IQuotationResponse>()

// 状态配置
const statusConfig = {
  Draft: {
    label: '草稿',
    color: '#909399',
    bgColor: '#f4f4f5',
    description: '报价草稿，未公开发布'
  },
  Active: {
    label: '激活中',
    color: '#67C23A',
    bgColor: '#f0f9ff',
    description: '报价已公开，可被其他用户查看'
  }
}

// 计算属性
const isOwner = computed(() => {
  return quotation.value && userStore.userInfo &&
         quotation.value.userID === userStore.userInfo.ID
})

const isPublicView = computed(() => {
  return !isOwner.value
})

const canEdit = computed(() => {
  return isOwner.value && quotation.value?.status === 'Draft'
})

const canPublish = computed(() => {
  return isOwner.value && quotation.value?.status === 'Draft'
})

const canToggleStatus = computed(() => {
  return isOwner.value && (quotation.value?.status === 'Active' || quotation.value?.status === 'Draft')
})

const canDelete = computed(() => {
  return isOwner.value && quotation.value?.status === 'Draft'
})

const showContactButton = computed(() => {
  return isPublicView.value && quotation.value?.status === 'Active'
})

// 生命周期
onLoad((options) => {
  if (options?.id) {
    quotationId.value = parseInt(options.id)
    loadQuotationDetail()
  } else {
    // 如果没有ID参数，返回上一页
    uni.showToast({
      title: '报价ID不存在',
      icon: 'error'
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
})

// 加载报价详情
async function loadQuotationDetail() {
  if (!quotationId.value) return
  
  try {
    isLoading.value = true
    const res = await getQuotationDetail(quotationId.value)
    quotation.value = res.data
    
    // 设置页面标题
    uni.setNavigationBarTitle({
      title: quotation.value.title || '报价详情'
    })
    
  } catch (error) {
    console.error('加载报价详情失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error'
    })
    
    // 延迟返回
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  } finally {
    isLoading.value = false
  }
}

// 编辑报价
function editQuotation() {
  if (!quotation.value) return
  
  uni.navigateTo({
    url: `/pages/quotes/edit?id=${quotation.value.id}`
  })
}

// 发布报价
async function publishQuotationItem() {
  if (!quotation.value) return
  
  try {
    uni.showLoading({ title: '发布中...' })

    // 发布报价需要设置过期时间，这里使用默认的7天后过期
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 7)

    await publishQuotation({
      id: quotation.value.id,
      expiresAt: expiresAt.toISOString()
    })

    uni.showToast({
      title: '发布成功',
      icon: 'success'
    })

    // 重新加载详情
    await loadQuotationDetail()

  } catch (error) {
    console.error('发布报价失败:', error)
    uni.showToast({
      title: '发布失败',
      icon: 'error'
    })
  } finally {
    uni.hideLoading()
  }
}

// 切换报价状态
async function toggleQuotationStatusItem() {
  if (!quotation.value) return

  const isActive = quotation.value.status === 'Active'
  const actionText = isActive ? '设为草稿' : '激活报价'
  const confirmText = isActive ? '确定要将此报价设为草稿吗？设为草稿后将不再对外展示。' : '确定要激活此报价吗？激活后将对外展示。'

  const res = await new Promise<boolean>((resolve) => {
    uni.showModal({
      title: `确认${actionText}`,
      content: confirmText,
      success: (modalRes) => {
        resolve(modalRes.confirm)
      }
    })
  })

  if (!res) return

  try {
    uni.showLoading({ title: `${actionText}中...` })

    await toggleQuotationStatus(quotation.value.id)

    uni.showToast({
      title: `${actionText}成功`,
      icon: 'success'
    })

    // 重新加载详情
    await loadQuotationDetail()

  } catch (error) {
    console.error('切换状态失败:', error)
    uni.showToast({
      title: `${actionText}失败`,
      icon: 'error'
    })
  } finally {
    uni.hideLoading()
  }
}

// 删除报价
async function deleteQuotationItem() {
  if (!quotation.value) return
  
  const res = await new Promise<boolean>((resolve) => {
    uni.showModal({
      title: '确认删除',
      content: '确定要删除这个报价草稿吗？删除后无法恢复。',
      success: (modalRes) => {
        resolve(modalRes.confirm)
      }
    })
  })

  if (!res) return

  try {
    uni.showLoading({ title: '删除中...' })
    
    await deleteQuotation(quotation.value.id)
    
    uni.showToast({
      title: '删除成功',
      icon: 'success'
    })
    
    // 返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
    
  } catch (error) {
    console.error('删除报价失败:', error)
    uni.showToast({
      title: '删除失败',
      icon: 'error'
    })
  } finally {
    uni.hideLoading()
  }
}

// 联系发布者/发起点价
function contactPublisher() {
  if (!quotation.value) return
  
  // TODO: 实现发起点价请求功能
  uni.showToast({
    title: '功能开发中',
    icon: 'none'
  })
}

// 格式化价格显示
function formatPrice(quotation: IQuotationResponse): string {
  if (quotation.priceType === 'Fixed') {
    return `¥ ${quotation.price.toFixed(2)}`
  } else if (quotation.priceType === 'Basis' && quotation.instrumentRef) {
    if (quotation.price >= 0) {
      return `${quotation.instrumentRef.instrument_id} + ${quotation.price}`
    } else {
      return `${quotation.instrumentRef.instrument_id} ${quotation.price}`
    }
  } else {
    return quotation.price.toString()
  }
}

// 格式化时间
function formatDateTime(dateTime: string): string {
  const date = new Date(dateTime)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 格式化剩余时间
function formatRemainingTime(): string {
  if (!quotation.value) return ''
  
  if (quotation.value.status !== 'Active') return ''
  
  if (quotation.value.isExpired) {
    return '已过期'
  }
  
  if (quotation.value.remainingHours <= 0) {
    return '即将过期'
  } else if (quotation.value.remainingHours < 24) {
    return `剩余 ${quotation.value.remainingHours} 小时`
  } else {
    const days = Math.floor(quotation.value.remainingHours / 24)
    return `剩余 ${days} 天`
  }
}

// 分享报价
function shareQuotation() {
  if (!quotation.value) return
  
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 0,
    href: `https://your-domain.com/pages/quotes/detail?id=${quotation.value.id}`,
    title: quotation.value.title,
    summary: `${quotation.value.commodity?.name} - ${formatPrice(quotation.value)}`,
    imageUrl: '/static/images/share-logo.png'
  })
}
</script>

<template>
  <view class="page-container">
    <!-- 加载状态 -->
    <view v-if="isLoading" class="loading-container">
      <wd-loading />
      <text class="loading-text">加载中...</text>
    </view>
    
    <!-- 详情内容 -->
    <view v-else-if="quotation" class="detail-container">
      <!-- 主要信息区域 -->
      <view class="main-info-section">
        <!-- 状态标签 -->
        <view 
          class="status-tag"
          :style="{ 
            color: statusConfig[quotation.status].color,
            backgroundColor: statusConfig[quotation.status].bgColor 
          }"
        >
          {{ statusConfig[quotation.status].label }}
        </view>
        
        <!-- 标题和价格 -->
        <view class="title-price-section">
          <text class="quotation-title">{{ quotation.title }}</text>
          <text class="quotation-price">{{ formatPrice(quotation) }}</text>
        </view>
        
        <!-- 核心交易信息 -->
        <view class="core-info-card">
          <view class="info-row main-info">
            <text class="label">商品种类：</text>
            <text class="value highlight">{{ quotation.commodity?.name || '-' }}</text>
          </view>
          <view class="info-row main-info">
            <text class="label">交货地点：</text>
            <text class="value highlight">{{ quotation.deliveryLocation }}</text>
          </view>
          <view class="info-row main-info">
            <text class="label">价格类型：</text>
            <text class="value highlight">{{ quotation.priceType === 'Fixed' ? '一口价' : '基差报价' }}</text>
          </view>
          <view v-if="quotation.priceType === 'Basis' && quotation.instrumentRef" class="info-row main-info">
            <text class="label">参考合约：</text>
            <text class="value highlight">{{ quotation.instrumentRef.instrument_name }}</text>
          </view>
        </view>
      </view>
      
      <!-- 次要信息区域 -->
      <view class="secondary-info-section">
        <wd-cell-group>
          <wd-cell v-if="quotation.brand" title="品牌" :value="quotation.brand" />
          
          <!-- 发布者信息（仅公众视角显示） -->
          <wd-cell v-if="isPublicView" title="发布企业" :value="quotation.user?.nickName || '未知企业'" />
          
          <wd-cell title="发布时间" :value="formatDateTime(quotation.createdAt)" />
          <wd-cell title="过期时间" :value="formatDateTime(quotation.expiresAt)" />
          
          <wd-cell v-if="quotation.status === 'Active'" title="剩余有效期">
            <text 
              class="remaining-time" 
              :class="{ expired: quotation.isExpired }"
            >
              {{ formatRemainingTime() }}
            </text>
          </wd-cell>
        </wd-cell-group>
      </view>
      
      <!-- 详细信息区域 -->
      <view v-if="quotation.specifications || quotation.description" class="detail-info-section">
        <wd-cell-group>
          <wd-cell v-if="quotation.specifications" title="规格说明">
            <text class="multi-line-text">{{ quotation.specifications }}</text>
          </wd-cell>

          <wd-cell v-if="quotation.description" title="补充说明">
            <text class="multi-line-text">{{ quotation.description }}</text>
          </wd-cell>
        </wd-cell-group>
      </view>
      
      <!-- 操作按钮区域 -->
      <view class="action-section">
        <!-- 发布者视角 -->
        <view v-if="isOwner" class="owner-actions">
          <wd-button 
            v-if="canEdit" 
            type="info" 
            size="large" 
            @click="editQuotation"
          >
            编辑报价
          </wd-button>
          
          <wd-button 
            v-if="canPublish" 
            type="primary" 
            size="large" 
            @click="publishQuotationItem"
          >
            发布报价
          </wd-button>
          
          <wd-button
            v-if="canToggleStatus"
            type="warning"
            size="large"
            @click="toggleQuotationStatusItem"
          >
            {{ quotation.status === 'Active' ? '设为草稿' : '激活报价' }}
          </wd-button>
          
          <wd-button 
            v-if="canDelete" 
            type="error" 
            size="large" 
            @click="deleteQuotationItem"
          >
            删除报价
          </wd-button>
          
          <wd-button 
            v-if="quotation.status === 'Active'" 
            type="success" 
            plain 
            size="large" 
            @click="shareQuotation"
          >
            分享报价
          </wd-button>
        </view>
        
        <!-- 公众视角 -->
        <view v-else class="public-actions">
          <wd-button 
            v-if="showContactButton" 
            type="primary" 
            size="large" 
            @click="contactPublisher"
          >
            对此报价感兴趣，发起点价
          </wd-button>
          
          <wd-button 
            type="success" 
            plain 
            size="large" 
            @click="shareQuotation"
          >
            分享给朋友
          </wd-button>
        </view>
      </view>
    </view>
    
    <!-- 错误状态 -->
    <view v-else class="error-container">
      <wd-img
        src="/static/images/error-404.png"
        width="200rpx"
        height="200rpx"
        mode="aspectFit"
      />
      <text class="error-text">报价不存在或已被删除</text>
      <wd-button type="primary" @click="goBack">
        返回上一页
      </wd-button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
  
  .loading-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #999;
  }
}

.detail-container {
  padding-bottom: 200rpx; // 为固定按钮留空间
}

.main-info-section {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  position: relative;
  
  .status-tag {
    position: absolute;
    top: 24rpx;
    right: 24rpx;
    padding: 12rpx 20rpx;
    border-radius: 24rpx;
    font-size: 24rpx;
    font-weight: 500;
  }
  
  .title-price-section {
    margin-bottom: 32rpx;
    padding-right: 120rpx; // 为状态标签留空间
    
    .quotation-title {
      display: block;
      font-size: 36rpx;
      font-weight: 700;
      color: #333;
      line-height: 1.4;
      margin-bottom: 16rpx;
    }
    
    .quotation-price {
      font-size: 48rpx;
      font-weight: 700;
      color: #007aff;
    }
  }
  
  .core-info-card {
    background: #f8f9fa;
    border-radius: 12rpx;
    padding: 24rpx;
    
    .info-row {
      display: flex;
      margin-bottom: 16rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      &.main-info {
        font-size: 30rpx;
        
        .label {
          color: #666;
          width: 160rpx;
          font-weight: 500;
        }
        
        .value {
          color: #333;
          flex: 1;
          
          &.highlight {
            color: #007aff;
            font-weight: 600;
          }
        }
      }
    }
  }
}

.secondary-info-section,
.detail-info-section {
  margin: 20rpx;
  
  :deep(.wd-cell-group) {
    border-radius: 16rpx;
    overflow: hidden;
  }
}

.multi-line-text {
  max-width: 400rpx;
  line-height: 1.5;
  word-break: break-all;
  white-space: pre-wrap;
}

.remaining-time {
  color: #67C23A;
  font-weight: 500;
  
  &.expired {
    color: #F56C6C;
  }
}

.action-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 24rpx 20rpx;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
  
  .owner-actions,
  .public-actions {
    display: flex;
    gap: 16rpx;
    
    :deep(.wd-button) {
      flex: 1;
    }
  }
  
  .owner-actions {
    flex-wrap: wrap;
    
    :deep(.wd-button) {
      min-width: calc(50% - 8rpx);
    }
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  padding: 40rpx;
  
  .error-text {
    font-size: 28rpx;
    color: #999;
    margin: 24rpx 0 40rpx;
    text-align: center;
  }
}
</style>
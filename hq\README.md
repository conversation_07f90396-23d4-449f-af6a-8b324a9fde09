# hq - 量化交易行情引擎

## 简介

`hq` 是一个基于 Python 的量化交易行情引擎。它被设计用于在特定的交易时段自动连接、接收和处理行情数据，并将数据存储或转发到其他系统。该项目集成了 `vn.py` 生态，利用其强大的交易接口和工具。

## 技术栈

*   **核心框架**: Python 3
*   **虚拟环境与包管理**: UV
*   **交易库**: [vn.py](https://www.vnpy.com/) (`vnpy_ctp`)
*   **消息队列/通信**: `pynng` (NNG - Nanomsg Next Gen)
*   **数据库**: MySQL (`PyMySQL`), Redis (`redis-py`)
*   **任务调度**: `APScheduler`
*   **配置**: INI 文件 (`config.ini`)

## 项目结构

```
.
├── config/           # 配置文件模块
├── engine/           # 核心引擎模块
│   ├── address.py    # 地址/席位处理逻辑
│   ├── instrument.py # 合约信息处理
│   ├── md.py         # 行情数据处理核心
│   └── nng.py        # NNG 通信封装
├── vnpy/             # vn.py 框架集成代码
├── main.py           # 主程序入口
├── requirements.txt  # Python 依赖列表
├── config.ini        # 应用程序配置文件
├── pyproject.toml    # 项目元数据 (PEP 621)
└── install.bat       # Windows 环境安装脚本
└── install.sh        # Linux/macOS 环境安装脚本
```

## 配置

在运行程序之前，您需要配置 `config.ini` 文件，以确保所有服务都能正确连接。

```ini
[database]
host = localhost
port = 3306
user = root
password = 123456
database = dianhaojia

[address]
like_broker_id = 3003
exclude_broker_id = 00001


[redis]
host = localhost
port = 6379
db = 0
```

-   **[database]**: 配置您的 MySQL 数据库连接信息。
-   **[address]**: 配置经纪商（Broker）席位相关信息。
-   **[nng]**: 配置 `nng` 服务的监听地址，用于进程间通信。
-   **[redis]**: 配置 Redis 数据库连接信息。

## 安装与运行

本项目推荐使用 `uv` 进行虚拟环境管理和包安装，以获得极速的体验。

### 1. 安装 uv

如果您尚未安装 `uv`, 可以通过以下方式安装：

```shell
# macOS & Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# Windows
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```

### 2. 创建虚拟环境并激活

```shell
# 创建虚拟环境
uv venv

# 激活虚拟环境
# Windows
.venv\Scripts\activate
# macOS / Linux
source .venv/bin/activate
```

### 3. 安装依赖

使用 `uv` 安装 `requirements.txt` 中列出的所有依赖。

```shell
uv pip install -r requirements.txt
```

*注意：`install.bat` 和 `install.sh` 脚本提供了传统的 `pip` 安装方式，并包含了安装 `TA-Lib` 的额外步骤。如果遇到 `TA-Lib` 相关问题，可以参考这两个脚本。*

### 4. 运行引擎

完成安装和配置后，通过以下命令启动行情引擎：

```shell
python main.py
```

程序启动后，会根据 `main.py` 中定义的交易时段 (`is_trading_time` 函数) 自动启动和停止核心引擎。

## 核心逻辑

`main.py` 是项目的入口文件，其核心逻辑如下：

1.  **自动调度**: `schedule_engine` 函数使用 `APScheduler` 创建一个定时任务，每分钟检查一次当前时间。
2.  **交易时间判断**: `is_trading_time` 函数判断当前是否处于预设的日间或夜间交易时段。
3.  **引擎启停**:
    -   如果当前是交易时段且引擎未运行，则调用 `start_engine` 创建并启动 `Engine` 实例。
    -   如果当前不在交易时段且引擎正在运行，则调用 `stop_engine` 来停止引擎。

这种设计确保了资源在非交易时间被释放，并在需要时自动准备就绪。在调试或持续运行时，可以直接执行 `start_engine()`，如 `main.py` 的 `if __name__ == "__main__"` 部分所示。

---
instruments 数据库数据结构
CREATE TABLE `instruments` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(3) DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL,
  `exchange_id` varchar(10) COLLATE utf8mb4_general_ci NOT NULL COMMENT '交易所ID',
  `product_id` varchar(10) COLLATE utf8mb4_general_ci NOT NULL COMMENT '产品ID',
  `product_name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '产品名称',
  `instrument_id` varchar(30) COLLATE utf8mb4_general_ci NOT NULL COMMENT '合约ID',
  `instrument_name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '合约名称',
  `product_class` varchar(1) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '商品类别',
  `volume_multiple` bigint NOT NULL COMMENT '合约乘数',
  `price_tick` decimal(10,4) NOT NULL COMMENT '最小变动价位',
  `long_margin_ratio` decimal(10,6) DEFAULT NULL COMMENT '做多保证金率',
  `short_margin_ratio` decimal(10,6) DEFAULT NULL COMMENT '做空保证金率',
  `open_ratio_by_money` decimal(10,8) DEFAULT NULL COMMENT '开仓手续费率',
  `close_ratio_by_money` decimal(10,8) DEFAULT NULL COMMENT '平仓手续费率',
  `close_today_ratio` decimal(10,8) DEFAULT NULL COMMENT '平今手续费率',
  `delivery_year` bigint DEFAULT NULL COMMENT '交割年份',
  `delivery_month` bigint DEFAULT NULL COMMENT '交割月份',
  `open_date` datetime(3) DEFAULT NULL COMMENT '上市日期',
  `expire_date` datetime(3) DEFAULT NULL COMMENT '最后交易日',
  `inst_life_phase` varchar(1) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '合约状态',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_instruments_instrument_id` (`instrument_id`),
  KEY `idx_instruments_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
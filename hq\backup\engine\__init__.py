from datetime import datetime
import json
import asyncio

from config import get_address_config, get_redis_config
from core.server_manager import ServerManager
from core.instrument_manager import get_instrument
from vnpy.event.engine import EventEngine, EVENT_TIMER
from engine.md import CtpMdApi
from vnpy.trader.constant import Exchange
from vnpy.trader.event import EVENT_TICK, EVENT_LOG
import redis

from vnpy.trader.object import SubscribeRequest


class Engine:
    def __init__(self):
        self.engine = EventEngine()
        self.md = CtpMdApi(self.engine)
        self.redis = None
        self.server_manager = ServerManager()

    def register_event(self):
        self.engine.register(EVENT_TICK, self.handle_on_tick)
        # self.engine.register(EVENT_LOG, self.handle_on_tick)

    def unregister_event(self):
        self.engine.unregister(EVENT_TICK, self.handle_on_tick)
        # self.engine.unregister(EVENT_LOG, self.handle_on_tick)

    def handle_on_tick(self, event):
        if event.data is None:
            return
        try:
            if event.data.symbol == "IF2503":
                now = datetime.now()
                print(now.strftime("%H:%M:%S"), "接收到IF2503行情：", event.data)

            data_dict = {k: (v.isoformat() if isinstance(v, datetime) else v) for k, v in
                         event.data.__dict__.items()}
            json_str = json.dumps(data_dict)

            # 使用Redis存储并发布行情数据
            key = f"tick:{event.data.symbol}"
            # 直接覆盖为最新数据，并设置30天过期时间（秒）
            self.redis.setex(key, 30 * 24 * 3600, json_str)
            # 发布更新通知
            self.redis.publish("tick_update", json_str)
        except Exception as e:
            print(e)

    def start(self):
        redis_config = get_redis_config()
        print("redis_config:", redis_config)
        self.redis = redis.Redis(
            host=redis_config['host'],
            port=redis_config['port'],
            db=redis_config['db'],
            password=redis_config['password'] or None,
            decode_responses=True
        )
        self.register_event()
        self.engine.start()
        
        # 使用ServerManager获取最佳行情服务器地址
        address_config = get_address_config()
        like_broker_id = address_config.get("like_broker_id")
        exclude_broker_id = address_config.get("exclude_broker_id")  # 列表或者None
        print("exclude_broker_id:", exclude_broker_id)
        
        # 选择最佳期货公司服务器
        broker_ids = [like_broker_id] if like_broker_id else None
        best_info = asyncio.run(self.server_manager.select_best_broker(
            broker_ids=broker_ids, 
            exclude_broker_ids=exclude_broker_id
        ))
        
        if best_info is None:
            print("错误: 未找到可用的期货公司服务器")
            return
        
        broker_id = best_info["broker_id"]
        addresses = best_info["addresses"]
        print(f"使用期货公司: {best_info['broker_name']} ({broker_id})")
        print(f"服务器: {best_info['server_name']}")
        print(f"连接地址: {addresses}")
        
        # 连接到最佳服务器
        self.md.connect(addresses, "000000", "000000", broker_id)
        
        # 订阅合约
        instruments = get_instrument()
        for ins in instruments:
            self.md.subscribe(SubscribeRequest(symbol=ins[0], exchange=Exchange(ins[1])))

    def stop(self):
        if self.redis:
            try:
                self.redis.close()
            except Exception:
                pass
        self.unregister_event()
        self.engine.stop()
        self.md.close()


if __name__ == "__main__":
    import time
    from config import get_config

    config = get_config()

    dial_address = "tcp://127.0.0.1:40899"
    engine = Engine()
    engine.start()
    while True:
        time.sleep(1)

"""
配置管理模块
"""
import configparser
import os
from pathlib import Path
from typing import Optional, Dict, Any

# 用于存储配置的私有全局变量
_config: Optional[Dict[str, Any]] = None
_initialized: bool = False

def _get_default_config_path() -> str:
    """获取默认配置文件路径 (项目根目录下的 config.ini)"""
    current_dir = Path(__file__).parent.parent
    return str(current_dir / "config.ini")

def _parse_config(parser: configparser.ConfigParser) -> Dict[str, Any]:
    """将 ConfigParser 对象解析为字典"""
    config_dict = {}
    for section in parser.sections():
        config_dict[section] = {}
        for key, value in parser.items(section):
            config_dict[section][key] = value
    return config_dict

def init_config(config_path: Optional[str] = None):
    """
    初始化配置系统。

    从指定的 .ini 文件读取配置并加载到内存中。
    此函数应在应用程序启动时调用一次。
    """
    global _config, _initialized
    if _initialized:
        print("配置已经初始化，跳过。")
        return

    path = config_path or _get_default_config_path()
    if not os.path.exists(path):
        # 如果配置文件不存在，创建一个空的 _config，并标记为已初始化
        print(f"警告: 配置文件不存在于 {path}。将使用空配置。")
        _config = {}
        _initialized = True
        return

    try:
        parser = configparser.ConfigParser()
        parser.read(path, encoding='utf-8')
        _config = _parse_config(parser)
        _initialized = True
        print(f"配置系统已从 {path} 初始化")
    except Exception as e:
        print(f"错误: 初始化配置失败: {e}")
        _config = {} # 即使失败也设置为空字典，避免None
        _initialized = True # 标记为已初始化以防止重试

def get_config() -> Dict[str, Any]:
    """
    获取已加载的配置。

    如果配置未初始化，将引发 RuntimeError。
    """
    if not _initialized or _config is None:
        raise RuntimeError("配置系统尚未初始化。请在程序启动时调用 init_config()")
    return _config

def get_section(name: str) -> Dict[str, Any]:
    """
    辅助函数，用于从配置中获取特定部分。

    如果部分不存在，则返回一个空字典。
    """
    config = get_config()
    return config.get(name, {})

# 功能文档：实时行情订阅与推送

## 1. 功能概述

本功能旨在建立一个从后端数据源 (`@hq` 模块) 到前端应用 (`@app`) 的完整、高效的实时行情数据链路。用户在 App 端可以按需订阅或取消订阅指定交易合约的行情数据。订阅成功后，将立即收到该合约的最新行情，并在此后持续接收由服务器推送的实时更新。

## 2. 核心流程

1.  **用户订阅**: 用户在 App 页面操作，触发对某个合约（如 `IF2508`）的订阅请求。
2.  **前端发送**: App `marketStore` 调用 `socketStore`，通过 WebSocket 发送 `subscribe_market` 事件到服务器。
3.  **后端处理订阅**: `@admin/server` 的 `ws_hub` 模块收到请求，记录下该客户端与合约的订阅关系。
4.  **首次数据推送**: 服务器立即从 Redis 中获取该合约的当前最新行情（`tick:IF2508`），并封装成 `market_update` 事件，发送给发起订阅的客户端。
5.  **行情源更新**: `@hq` 模块获取到新的行情，将数据更新到 Redis 的 `tick:IF2508` 键，并向 `tick_update` 频道 (Pub/Sub) 发布更新通知。
6.  **后端监听与推送**: `ws_hub` 中常驻的 Redis 监听器订阅 `tick_update` 频道，收到更新通知后获取最新行情。
7.  **分发更新**: 服务器根据订阅关系，将新行情封装成 `market_update` 事件，推送给所有订阅了该合约的客户端。
8.  **前端更新**: App `socketStore` 收到消息，分发给 `marketStore`，后者更新状态，从而驱动 UI 实时刷新。

## 3. 模块设计

### 3.1. 前端 App (@app)

#### 3.1.1. 新增文件: `app/src/store/market.ts`

-   **定位**: 专门用于管理行情数据的状态和业务逻辑，与 `socketStore` 解耦，后者仅负责连接和原始消息收发。
-   **State (状态)**:
    -   `marketData`: `Map<string, object>`，键为合约代码 (e.g., `IF2508`)，值为该合约的详细行情对象。
    -   `subscribedSymbols`: `Set<string>`，用于存储当前已订阅的合约代码，防止重复订阅和方便管理。
-   **Actions (动作)**:
    -   `subscribe(symbol: string, exchange: string)`:
        -   检查 `subscribedSymbols`，若已订阅则直接返回。
        -   调用 `socketStore.sendMessage('subscribe_market', { symbol, exchange })`。
        -   将 `symbol` 添加到 `subscribedSymbols`。
    -   `unsubscribe(symbol: string)`:
        -   检查 `subscribedSymbols`，若未订阅则直接返回。
        -   调用 `socketStore.sendMessage('unsubscribe_market', { symbol })`。
        -   从 `subscribedSymbols` 和 `marketData` 中移除该合约。
    -   `_handleMarketUpdate(data: object)`:
        -   内部方法，由 `socketStore` 在收到行情消息时调用。
        -   根据 `data.symbol` 更新 `marketData` 中对应的行情。

#### 3.1.2. 修改文件: `app/src/store/socket.ts`

-   **定位**: 保持其作为通用 WebSocket 连接管理器的角色。
-   **修改内容**:
    -   在 `handleMessage` 函数的 `switch` 语句中，增加一个 `case` 来处理行情更新事件。
    -   **`case 'market_update':`**:
        -   获取 `marketStore` 的实例。
        -   调用 `marketStore._handleMarketUpdate(message.payload)`，将业务处理逻辑委托给 `marketStore`。

### 3.2. 后端 Server (@admin/server)

#### 3.2.1. 修改模块: `ws_hub`

-   **定位**: 扩展 WebSocket 核心枢纽，增加行情订阅管理和 Redis 数据同步逻辑。
-   **订阅关系管理**:
    -   在 `Hub` 结构体中，增加一个数据结构来维护订阅关系，建议使用 `map[string]map[*Client]bool`。
    -   结构为：`{ "合约代码": { "客户端1": true, "客户端2": true } }`。
-   **消息处理逻辑**:
    -   **`subscribe_market` 事件**:
        1.  解析出 `payload` 中的 `symbol` (合约代码) 和 `exchange`。
        2.  将当前客户端注册到订阅关系 `map` 中。
        3.  **立即**从 Redis 中使用 `HGETALL` 获取 `tick:{symbol}` 的最新完整行情。
        4.  封装成 `market_update` 消息，立即发送给该客户端。
    -   **`unsubscribe_market` 事件**:
        1.  解析出 `symbol`。
        2.  从订阅关系 `map` 中移除该客户端。
-   **Redis 实时监听**:
    -   需要启动一个常驻的、独立的 goroutine。
    -   此 goroutine 使用 Redis 的 `SUBSCRIBE` 命令监听 `tick_update` 频道。
    -   收到消息后，解析出合约代码和数据，查找所有订阅了该合约的客户端，并向他们推送 `market_update` 消息。

## 4. 通信协议 (WebSocket Events)

-   **客户端 -> 服务端**:
    -   订阅: `{ "event": "subscribe_market", "payload": { "symbol": "IF2508", "exchange": "CFFEX" } }`
    -   取消订阅: `{ "event": "unsubscribe_market", "payload": { "symbol": "IF2508" } }`

-   **服务端 -> 客户端**:
    -   行情更新 (包含首次推送): `{ "event": "market_update", "payload": { ... } }`

## 5. 数据模型 (Data Model)

`market_update` 事件的 `payload` 结构应与 `@hq` 模块写入 Redis 的行情数据结构保持一致。

**参考结构**:
```json
{
  "symbol": "IF2508",
  "exchange": "CFFEX",
  "last_price": 68000.50,
  "last_volume": 12345.67,
  "turnover": 839512345.67,
  "open_interest": 9876,
  "timestamp": 1678886400000,
  "ask_price_1": 68000.60,
  "ask_volume_1": 10.5,
  "bid_price_1": 68000.40,
  "bid_volume_1": 8.8
}
```

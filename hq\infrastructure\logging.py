"""
结构化日志系统 - 使用loguru实现全局日志管理
提供统一的日志配置、格式化输出、自动轮转等功能
"""
import sys
import os
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime

try:
    from loguru import logger
    LOGURU_AVAILABLE = True
except ImportError:
    LOGURU_AVAILABLE = False
    import logging


class LogConfig:
    """日志配置类"""
    
    def __init__(self):
        # 默认日志目录
        self.log_dir = Path(__file__).parent.parent / "logs"
        self.log_dir.mkdir(exist_ok=True)
        
        # 日志级别
        self.console_level = "INFO"
        self.file_level = "DEBUG"
        
        # 日志格式
        self.log_format = (
            "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )
        
        # 文件轮转配置
        self.rotation = "100 MB"  # 文件大小轮转
        self.retention = "30 days"  # 保留时间
        self.compression = "zip"  # 压缩格式
        
        # 是否启用彩色输出
        self.colorize = True
        
        # 是否启用回溯信息
        self.backtrace = True
        self.diagnose = True


class StructuredLogger:
    """结构化日志器"""
    
    def __init__(self, config: LogConfig = None):
        """
        初始化日志器
        
        Args:
            config: 日志配置，如果为None则使用默认配置
        """
        self.config = config or LogConfig()
        self._initialized = False
        self._loguru_available = LOGURU_AVAILABLE
        
        if not self._loguru_available:
            print("警告: loguru未安装，使用标准logging模块")
            self._setup_standard_logging()
        else:
            self._setup_loguru()
    
    def _setup_loguru(self):
        """设置loguru日志系统"""
        if self._initialized:
            return
        
        # 移除默认handler
        logger.remove()
        
        # 添加控制台handler
        logger.add(
            sys.stdout,
            level=self.config.console_level,
            format=self.config.log_format,
            colorize=self.config.colorize,
            backtrace=self.config.backtrace,
            diagnose=self.config.diagnose
        )
        
        # 添加文件handler - 主日志文件
        main_log_file = self.config.log_dir / "hq_system.log"
        logger.add(
            str(main_log_file),
            level=self.config.file_level,
            format=self.config.log_format,
            rotation=self.config.rotation,
            retention=self.config.retention,
            compression=self.config.compression,
            encoding="utf-8",
            backtrace=self.config.backtrace,
            diagnose=self.config.diagnose
        )
        
        # 添加错误专用日志文件
        error_log_file = self.config.log_dir / "hq_errors.log"
        logger.add(
            str(error_log_file),
            level="ERROR",
            format=self.config.log_format,
            rotation=self.config.rotation,
            retention=self.config.retention,
            compression=self.config.compression,
            encoding="utf-8",
            backtrace=self.config.backtrace,
            diagnose=self.config.diagnose
        )
        
        # 添加性能日志文件
        perf_log_file = self.config.log_dir / "hq_performance.log"
        logger.add(
            str(perf_log_file),
            level="INFO",
            format=self.config.log_format,
            rotation="1 day",
            retention="7 days",
            compression=self.config.compression,
            encoding="utf-8",
            filter=lambda record: "PERF" in record["extra"]
        )
        
        self._initialized = True
        logger.info("Loguru日志系统初始化完成")
    
    def _setup_standard_logging(self):
        """设置标准logging模块（备用方案）"""
        if self._initialized:
            return
        
        # 创建根logger
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)
        
        # 清除现有handlers
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 创建格式器
        formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(name)s:%(funcName)s:%(lineno)d | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 控制台handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, self.config.console_level))
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
        
        # 文件handler
        log_file = self.config.log_dir / "hq_system.log"
        file_handler = logging.FileHandler(str(log_file), encoding='utf-8')
        file_handler.setLevel(getattr(logging, self.config.file_level))
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
        
        self._initialized = True
        logging.info("标准logging系统初始化完成")
    
    def get_logger(self, name: str = None):
        """
        获取logger实例
        
        Args:
            name: logger名称
            
        Returns:
            logger实例
        """
        if self._loguru_available:
            if name:
                return logger.bind(name=name)
            return logger
        else:
            return logging.getLogger(name or __name__)
    
    def log_performance(self, operation: str, duration: float, **kwargs):
        """
        记录性能日志
        
        Args:
            operation: 操作名称
            duration: 耗时（秒）
            **kwargs: 额外参数
        """
        perf_logger = self.get_logger("performance")
        if self._loguru_available:
            perf_logger.bind(PERF=True).info(
                f"性能统计 | 操作: {operation} | 耗时: {duration:.3f}s | 详情: {kwargs}"
            )
        else:
            perf_logger.info(
                f"性能统计 | 操作: {operation} | 耗时: {duration:.3f}s | 详情: {kwargs}"
            )
    
    def log_tick_data(self, symbol: str, price: float, volume: int):
        """
        记录行情数据（示例专用日志方法）
        
        Args:
            symbol: 合约代码
            price: 价格
            volume: 成交量
        """
        tick_logger = self.get_logger("tick")
        if self._loguru_available:
            tick_logger.bind(symbol=symbol, price=price, volume=volume).debug(
                f"行情数据 | {symbol} | 价格: {price} | 成交量: {volume}"
            )
        else:
            tick_logger.debug(
                f"行情数据 | {symbol} | 价格: {price} | 成交量: {volume}"
            )
    
    def log_connection_event(self, event_type: str, details: Dict[str, Any]):
        """
        记录连接事件
        
        Args:
            event_type: 事件类型 (connect, disconnect, error, etc.)
            details: 事件详情
        """
        conn_logger = self.get_logger("connection")
        if self._loguru_available:
            conn_logger.bind(event_type=event_type, **details).info(
                f"连接事件 | {event_type} | {details}"
            )
        else:
            conn_logger.info(
                f"连接事件 | {event_type} | {details}"
            )
    
    def set_level(self, level: str):
        """
        动态设置日志级别
        
        Args:
            level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        """
        if self._loguru_available:
            # loguru需要重新配置
            logger.remove()
            self.config.console_level = level
            self.config.file_level = level
            self._initialized = False
            self._setup_loguru()
        else:
            logging.getLogger().setLevel(getattr(logging, level.upper()))


# 全局日志器实例
_global_logger: Optional[StructuredLogger] = None


def get_logger(name: str = None):
    """
    获取全局logger实例
    
    Args:
        name: logger名称
        
    Returns:
        logger实例
    """
    global _global_logger
    if _global_logger is None:
        _global_logger = StructuredLogger()
    
    return _global_logger.get_logger(name)


def init_logging(config: LogConfig = None) -> StructuredLogger:
    """
    初始化全局日志系统
    
    Args:
        config: 日志配置
        
    Returns:
        StructuredLogger: 日志器实例
    """
    global _global_logger
    _global_logger = StructuredLogger(config)
    return _global_logger


def log_performance(operation: str, duration: float, **kwargs):
    """记录性能日志的便捷函数"""
    global _global_logger
    if _global_logger:
        _global_logger.log_performance(operation, duration, **kwargs)
    else:
        print(f"性能统计 | {operation} | {duration:.3f}s | {kwargs}")


def log_connection_event(event_type: str, details: Dict[str, Any]):
    """记录连接事件的便捷函数"""
    global _global_logger
    if _global_logger:
        _global_logger.log_connection_event(event_type, details)
    else:
        print(f"连接事件 | {event_type} | {details}")


# 替换print的装饰器
def replace_print_with_logger(func):
    """
    装饰器：将函数内的print替换为logger调用
    
    注意：这是一个示例，实际使用时应该手动替换print语句
    """
    def wrapper(*args, **kwargs):
        # 这里可以添加逻辑来捕获print并转发到logger
        # 实际应用中建议直接使用logger而不是依赖这种替换
        return func(*args, **kwargs)
    return wrapper


if __name__ == "__main__":
    """测试代码"""
    # 初始化日志系统
    config = LogConfig()
    config.console_level = "DEBUG"
    
    structured_logger = init_logging(config)
    
    # 获取不同类型的logger
    main_logger = get_logger("main")
    perf_logger = get_logger("performance")
    
    # 测试基本日志
    if LOGURU_AVAILABLE:
        main_logger.debug("这是一个调试消息")
        main_logger.info("这是一个信息消息")
        main_logger.warning("这是一个警告消息")
        main_logger.error("这是一个错误消息")
    else:
        main_logger.debug("这是一个调试消息")
        main_logger.info("这是一个信息消息")
        main_logger.warning("这是一个警告消息")
        main_logger.error("这是一个错误消息")
    
    # 测试性能日志
    import time
    start_time = time.time()
    time.sleep(0.1)  # 模拟操作
    duration = time.time() - start_time
    log_performance("测试操作", duration, status="success", records=100)
    
    # 测试连接事件日志
    log_connection_event("connect", {
        "server": "ctp.server.com",
        "port": 41213,
        "broker_id": "0037",
        "user_id": "test_user"
    })
    
    # 测试行情数据日志
    structured_logger.log_tick_data("IF2508", 4000.5, 1000)
    
    print("日志系统测试完成，请检查logs目录下的日志文件")
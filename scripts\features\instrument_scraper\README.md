# 交易合约信息同步器 (Instrument Synchronizer)

## 1. 功能概述 (Overview)

本功能旨在从 **OpenCTP 交易合约信息接口** 获取最新的金融合约数据，经过解析和格式化后，对项目数据库进行 **全量同步**。这确保了服务端应用始终能够获取到准确、及时的合约信息，用于交易、风控和分析。

**核心任务:**

1.  **API 数据获取 (Fetching):** 从指定的 URL API 获取原始 JSON 数据。
2.  **数据解析与转换 (Parsing & Transformation):** 将 JSON 数据转换为符合数据库结构的 Python 对象。
3.  **全量数据同步 (Full Synchronization):** 以接口最新数据为准，对本地数据库进行增、删、改操作。

## 2. API 接口说明 (API Endpoint)

-   **请求 URL:** `http://dict.openctp.cn/instruments`
-   **请求方法:** `GET`
-   **应答格式:** `JSON`

**URL 参数:**

-   `types`: 可选，商品类型，如 `stock`, `bond`, `fund`, `futures`, `option` 等（注意只有 `futures` 是复数），多个值之间用 `,` 号隔开。
-   `areas`: 可选，国家/地区，如 `China`, `USA`, `UK`, `Singapore` 等，多个值之间用 `,` 号隔开。
-   `markets`: 可选，交易所列表，如 `SHFE`, `CFFEX`, `SSE`, `SZSE` 等，多个值之间用 `,` 号隔开。
-   `products`: 可选，品种列表，如 `au`, `rb`, `TA`, `IF` 等，多个值之间用 `,` 号隔开。

**请求示例:**
`http://dict.openctp.cn/instruments?types=futures&markets=SHFE,CFFEX&products=au,rb,IF,IM`

## 3. 技术栈与依赖 (Tech Stack & Dependencies)

-   **HTTP 请求库:** `requests` (或 `httpx` for async)
-   **数据操作:** `pandas` (用于数据比对和转换，可选)
-   **数据库接口:** `sqlalchemy` 或其他数据库驱动

*所有依赖项都应在项目根目录的 `pyproject.toml` 中明确声明。*

## 4. 数据处理流程 (Data Processing Workflow)

**Step 1: 从 API 获取数据 (Fetch from API)**
-   使用 `requests` 库向 `http://dict.openctp.cn/instruments` 发送 GET 请求。
-   可以根据需要组合 `types`, `markets` 等参数来过滤数据。
-   检查响应状态码，确保请求成功 (e.g., `200 OK`)。

**Step 2: 解析与转换数据 (Parse & Transform Data)**
-   将收到的 JSON 响应解析为 Python 字典或对象列表。
-   根据数据库 `Instrument` 表结构，对数据进行字段映射和类型转换。参考映射关系如下：

| API 字段 (推测)    | 数据库字段          | 数据类型  | 备注                               |
| :----------------- | :------------------ | :-------- | :--------------------------------- |
| `instrument_id`    | `instrument_id`     | `String`  | 主键                               |
| `exchange_id`      | `exchange_id`       | `String`  |                                    |
| `instrument_name`  | `instrument_name`   | `String`  |                                    |
| `product_id`       | `product_id`        | `String`  |                                    |
| `product_class`    | `product_class`     | `String`  | e.g., 'futures', 'option'        |
| `delivery_year`    | `delivery_year`     | `Integer` |                                    |
| `delivery_month`   | `delivery_month`    | `Integer` |                                    |
| `create_date`      | `create_date`       | `Date`    | 格式: YYYY-MM-DD                   |
| `open_date`        | `start_date`        | `Date`    | 映射为 `start_date`                |
| `expire_date`      | `end_date`          | `Date`    | 映射为 `end_date`                  |
| `volume_multiple`  | `multiplier`        | `Integer` | 映射为 `multiplier`                |
| `price_tick`       | `price_tick`        | `Float`   |                                    |

**数据库表 `instruments` 字段规格:**

// Instrument 期货合约表 - 具体的合约信息
type Instrument struct {
	global.GVA_MODEL
	InstrumentID      string     `json:"instrument_id" gorm:"comment:合约ID;size:30;not null;uniqueIndex" validate:"required"`
	InstrumentName    string     `json:"instrument_name" gorm:"comment:合约名称;size:50;not null" validate:"required"`
	CommodityID       uint       `json:"commodity_id" gorm:"comment:关联的商品ID;not null" validate:"required"`
	ProductClass      string     `json:"product_class" gorm:"comment:商品类别;size:1;not null;default:'1'" validate:"required"`
	VolumeMultiple    int        `json:"volume_multiple" gorm:"comment:合约乘数;not null" validate:"required"`
	PriceTick         float64    `json:"price_tick" gorm:"comment:最小变动价位;type:decimal(10,4);not null" validate:"required"`
	LongMarginRatio   *float64   `json:"long_margin_ratio" gorm:"comment:做多保证金率;type:decimal(10,6)"`
	ShortMarginRatio  *float64   `json:"short_margin_ratio" gorm:"comment:做空保证金率;type:decimal(10,6)"`
	OpenRatioByMoney  *float64   `json:"open_ratio_by_money" gorm:"comment:开仓手续费率;type:decimal(10,8)"`
	CloseRatioByMoney *float64   `json:"close_ratio_by_money" gorm:"comment:平仓手续费率;type:decimal(10,8)"`
	CloseTodayRatio   *float64   `json:"close_today_ratio" gorm:"comment:平今手续费率;type:decimal(10,8)"`
	DeliveryYear      *int       `json:"delivery_year" gorm:"comment:交割年份"`
	DeliveryMonth     *int       `json:"delivery_month" gorm:"comment:交割月份"`
	OpenDate          *time.Time `json:"open_date" gorm:"comment:上市日期"`
	ExpireDate        *time.Time `json:"expire_date" gorm:"comment:最后交易日"`
	InstLifePhase     string     `json:"inst_life_phase" gorm:"comment:合约状态;size:1;not null;default:'0'"`
	CreatedAt         time.Time  `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt         time.Time  `json:"updated_at" gorm:"comment:更新时间"`
}

| 字段名            | 数据类型         | 主键 | 索引 | 注释         |
| :---------------- | :--------------- | :--- | :--- | :----------- |
| `instrument_id`   | `VARCHAR`        | ✅   | ✅   | 合约代码     |
| `exchange_id`     | `VARCHAR`        |      | ✅   | 交易所       |
| `instrument_name` | `VARCHAR`        |      |      | 合约名称     |
| `product_id`      | `VARCHAR`        |      | ✅   | 产品代码     |
| `product_class`   | `VARCHAR`        |      |      | 产品类型     |
| `delivery_year`   | `INTEGER`        |      |      | 交割年份     |
| `delivery_month`  | `INTEGER`        |      |      | 交割月份     |
| `create_date`     | `DATE`           |      |      | 创建日期     |
| `start_date`      | `DATE`           |      |      | 上市日       |
| `end_date`        | `DATE`           |      |      | 到期日       |
| `multiplier`      | `INTEGER`        |      |      | 合约乘数     |
| `price_tick`      | `FLOAT`          |      |      | 最小变动价位 |

**Step 3: 全量同步至数据库 (Full Sync to Database)**
-   **事务管理:** 整个同步过程应在单个数据库事务中完成，确保数据一致性。
-   **获取数据:**
    1.  从 API 获取所有最新的合约数据 (Remote Data)。
    2.  从数据库查询出所有现存的合约记录 (Local Data)。
-   **数据比对:**
    -   **新增 (Add):** 存在于 Remote Data，但不存在于 Local Data 的合约。
    -   **更新 (Update):** 同时存在于 Remote 和 Local Data 的合约。应使用最新数据更新本地记录。
    -   **删除 (Delete):** 存在于 Local Data，但不存在于 Remote Data 的合约。
-   **执行操作:**
    1.  执行 `DELETE` 操作，删除所有需要移除的合约。
    2.  执行 `UPDATE` 操作，更新所有需要变更的合约。
    3.  执行 `INSERT` 操作，添加所有新发现的合约。
-   **提交事务:** 若所有操作成功，则提交事务；若有任何失败，则回滚。

## 5. 日志与报告 (Logging & Reporting)

-   在脚本执行的关键步骤（开始、API 请求成功、同步完成、错误发生）记录详细日志。
-   执行结束后，输出一份简短的报告，说明本次同步的结果，例如：
    -   `Total instruments from API: 530`
    -   `New instruments added: 12`
    -   `Existing instruments updated: 5`
    -   `Stale instruments deleted: 7`
    -   `Errors: 0`

## 6. 脚本执行方式 (Execution)

此功能将作为 `main.py` 的一个子命令来调用。

```bash
# 完整执行同步
uv run python main.py sync-instruments

# (可选) 仅获取并打印 API 结果，不写入数据库 (用于调试)
uv run python main.py sync-instruments --dry-run

# (可选) 指定输出文件，而不是数据库
uv run python main.py sync-instruments --output-file instruments.json
```

## 7. 错误处理与容错 (Error Handling)

-   **API 请求失败:** 实现重试机制，例如在请求失败 (如 5xx 错误) 时等待几秒后重试。
-   **API 响应异常:** 如果响应内容不是有效的 JSON 或结构不符合预期，脚本应立即停止并记录错误。
-   **数据库连接失败:** 记录错误并退出，不进行后续操作。
-   **数据验证失败:** 在插入或更新前，对数据进行基本验证 (如主键是否存在)，防止脏数据入库。
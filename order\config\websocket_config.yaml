# WebSocket配置文件

websocket:
  # 服务器配置
  server:
    url: "ws://localhost:8888/ws/order"  # WebSocket服务器地址
    timeout: 10                          # 连接超时时间（秒）
    
  # 心跳配置
  heartbeat:
    interval: 30                         # 心跳间隔（秒）
    timeout: 45                          # 心跳超时（秒）
    
  # 重连配置
  reconnect:
    enabled: true                        # 是否启用自动重连
    max_attempts: 10                     # 最大重连次数
    initial_delay: 1                     # 初始重连延迟（秒）
    max_delay: 60                        # 最大重连延迟（秒）
    backoff_factor: 2.0                  # 退避因子
    
  # 自动连接配置
  auto_connect:
    enabled: true                        # 登录后是否自动连接WebSocket
    
# 开发环境配置
development:
  websocket:
    server:
      url: "ws://localhost:8888/ws/order"
    heartbeat:
      interval: 10                       # 开发环境心跳更频繁
      timeout: 15
    reconnect:
      max_attempts: 5
      initial_delay: 0.5
      
# 生产环境配置  
production:
  websocket:
    server:
      url: "wss://api.production.com/ws/order"
    heartbeat:
      interval: 30
      timeout: 45
    reconnect:
      max_attempts: 20
      initial_delay: 2
      max_delay: 120
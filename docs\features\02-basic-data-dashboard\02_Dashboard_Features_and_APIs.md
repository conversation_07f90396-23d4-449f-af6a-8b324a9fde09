# 子功能：看板核心功能与接口

## 1. 功能设计 (Functional Design)

### 1.1 核心设计理念

- **品种驱动**: 所有数据和图表都将以具体的期货品种（如：螺纹钢、铝、铜）为核心进行组织和筛选。
- **时序数据可视化**: 功能的核心是处理和展示时序数据。
- **灵活组合**: 前端提供灵活的选项，允许用户对数据进行多种组合和叠加，以适应不同的分析场景。

### 1.2 期限结构分析 (Term Structure)

- **描述**: 在同一时间点，展示选定品种的所有主力合约或连续合约的价格曲线。
- **交互**:
  - 用户选择一个期货品种（如“螺纹钢”）。
  - 用户选择一个日期。
  - 系统生成一张图表，X轴为合约的到期月份，Y轴为该合约在选定日期的收盘价。
- **视觉效果**: 一条连接各合约价格点的曲线，直观展示市场的 Contango 或 Backwardation 结构。

### 1.3 合约历史叠加对比 (Historical Comparison)

- **描述**: 对比同一月份（如05合约）在不同年份的价格或持仓量走势。
- **交互**:
  - 用户选择一个期货品种（如“螺纹钢”）。
  - 用户选择一个合约月份（如“05”）。
  - 用户选择多个年份（如“2022”, “2023”, “2024”）。
  - 用户选择要叠加的数据类型（“价格”或“持仓量”）。
- **视觉效果**:
  - **价格叠加**: 在同一张图表中绘制多年的价格曲线，X轴为日期（例如，从合约上市前N天到交割日），Y轴为价格。方便对比不同年份的季节性规律或趋势。
  - **持仓量叠加**: 类似价格叠加，Y轴替换为持仓量，用于分析资金在不同年份的参与度的变化。

## 2. 接口定义 (API Definition)

| 功能描述 | HTTP 方法 | 路径 (Endpoint) |
|---|---|---|
| 查询期限结构数据 | GET | `/api/v1/dashboard/term-structure` |
| 查询历史叠加数据 | GET | `/api/v1/dashboard/historical-comparison` |

## 3. 相关页面 (Related Pages)

- `src/views/dashboard/BasicDataDashboard.vue`
- `src/components/charts/TermStructureChart.vue`
- `src/components/charts/HistoricalComparisonChart.vue`

## 4. 测试用例 (Test Cases)

| 用例ID | 场景描述 | 测试步骤 | 预期结果 |
|---|---|---|---|
| TC-DASH-001 | 查看螺纹钢2024-07-19的期限结构 | 1. 选择品种“螺纹钢” 2. 选择日期“2024-07-19” | 图表正确显示所有螺纹钢合约在当日的收盘价曲线 |
| TC-DASH-002 | 对比螺纹钢05合约22,23,24三年的价格 | 1. 选品种“螺纹钢” 2. 选合约“05” 3. 选年份“2022,2023,2024” 4. 选数据“价格” | 图表正确绘制三条历史价格曲线 |
| TC-DASH-003 | 查询一个不存在数据的品种或日期 | 1. 选择一个无交易记录的品种 | 系统应提示“暂无数据”或返回空图表 |
| TC-DASH-004 | 切换数据类型从“价格”到“持仓量” | 1. 完成TC-DASH-002的步骤 2. 切换数据类型为“持仓量” | 图表Y轴和数据更新为持仓量数据 |

## 5. 注意事项 (Notes/Caveats)

- **前端性能**: 大量数据点的渲染可能导致前端性能问题，需要考虑数据降采样或懒加载等优化策略。
- **后端查询效率**: 后端接口需要针对时序数据库的特性进行优化，确保在大数据量下的查询性能。

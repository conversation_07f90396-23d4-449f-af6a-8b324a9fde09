package dianjia

import (
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type AuthRouter struct{}

func (a *AuthRouter) InitAuthRouter(Router *gin.RouterGroup) {
	authRouter := Router.Group("user")
	authRouterWithAuth := Router.Group("user").Use(middleware.JWTAuth())
	{
		// 不需要认证的路由
		authRouter.POST("sendLoginCode", v1.ApiGroupApp.DianjiaApiGroup.AuthApi.SendLoginCode)             // 发送登录验证码
		authRouter.POST("loginByPhone", v1.ApiGroupApp.DianjiaApiGroup.AuthApi.LoginByPhone)               // 手机号登录
		authRouter.POST("login", v1.ApiGroupApp.DianjiaApiGroup.AuthApi.LoginByUsernamePassword)           // 用户名密码登录
		authRouter.POST("loginByWechat", v1.ApiGroupApp.DianjiaApiGroup.AuthApi.LoginByWechat)             // 微信登录
	}
	{
		// 需要JWT认证的路由
		authRouterWithAuth.GET("getProfile", v1.ApiGroupApp.DianjiaApiGroup.AuthApi.GetProfile)       // 获取用户信息
		authRouterWithAuth.PUT("updateProfile", v1.ApiGroupApp.DianjiaApiGroup.AuthApi.UpdateProfile) // 更新用户信息
	}
}
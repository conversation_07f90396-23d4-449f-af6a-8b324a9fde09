package dianjia

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// ExecutionType 执行类型枚举
type ExecutionType string

const (
	ExecutionTypeOnline  ExecutionType = "Online"  // 线上执行
	ExecutionTypeOffline ExecutionType = "Offline" // 线下执行
)

// ExecutionStatus 执行状态枚举
type ExecutionStatus string

const (
	ExecutionStatusSuccess ExecutionStatus = "Success" // 成功
	ExecutionStatusFailed  ExecutionStatus = "Failed"  // 失败
)

// ExecutionDetail 执行明细表 - 连接交易请求和合同的桥梁
type ExecutionDetail struct {
	global.GVA_MODEL

	// 关联ID
	TradeRequestID uint         `json:"tradeRequestID" gorm:"column:trade_request_id;not null;comment:关联的交易请求ID"`
	TradeRequest   TradeRequest `json:"tradeRequest" gorm:"foreignKey:TradeRequestID"`

	ContractID uint     `json:"contractID" gorm:"column:contract_id;not null;comment:关联的合同ID"`
	Contract   Contract `json:"contract" gorm:"foreignKey:ContractID"`

	// 执行信息
	ExecutedQuantity int             `json:"executedQuantity" gorm:"column:executed_quantity;not null;comment:从该合同执行的数量"`
	ExecutedPrice    float64         `json:"executedPrice" gorm:"column:executed_price;type:decimal(10,2);not null;comment:本次执行的成交价"`
	ExecutionType    ExecutionType   `json:"executionType" gorm:"column:execution_type;type:varchar(50);not null;comment:执行类型(Online/Offline)"`
	ContractPrice    float64         `json:"contractPrice" gorm:"column:contract_price;type:decimal(10,2);not null;comment:执行时使用的合同价格"`
	ResultPrice      float64         `json:"resultPrice" gorm:"column:result_price;type:decimal(10,2);not null;comment:执行结果价格"`
	Status           ExecutionStatus `json:"status" gorm:"column:status;type:varchar(50);not null;comment:执行状态(Success/Failed)"`

	// 备注信息
	Remarks string `json:"remarks" gorm:"column:remarks;type:text;comment:执行备注"`
}

// CreateExecutionDetailRequest 创建执行明细的请求结构
type CreateExecutionDetailRequest struct {
	TradeRequestID   uint          `json:"tradeRequestID" binding:"required"`
	ContractID       uint          `json:"contractID" binding:"required"`
	ExecutedQuantity int           `json:"executedQuantity" binding:"required,min=1"`
	ExecutedPrice    float64       `json:"executedPrice" binding:"required"`
	ExecutionType    ExecutionType `json:"executionType" binding:"required,oneof=Online Offline"`
	Remarks          string        `json:"remarks"`
}

// UpdateExecutionDetailRequest 更新执行明细的请求结构
type UpdateExecutionDetailRequest struct {
	ID               uint            `json:"id" binding:"required"`
	ExecutedQuantity int             `json:"executedQuantity" binding:"required,min=1"`
	ExecutedPrice    float64         `json:"executedPrice" binding:"required"`
	ExecutionType    ExecutionType   `json:"executionType" binding:"required,oneof=Online Offline"`
	Status           ExecutionStatus `json:"status" binding:"required,oneof=Success Failed"`
	Remarks          string          `json:"remarks"`
}

func (ExecutionDetail) TableName() string {
	return "dj_execution_details"
}

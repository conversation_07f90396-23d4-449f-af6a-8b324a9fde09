# Order客户端日志配置文件

version: 1
disable_existing_loggers: false

formatters:
  standard:
    format: '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'
    
  detailed:
    format: '%(asctime)s [%(levelname)s] %(name)s:%(lineno)d: %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'
    
  simple:
    format: '[%(levelname)s] %(message)s'

handlers:
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: standard
    stream: ext://sys.stdout
    
  file:
    class: logging.handlers.RotatingFileHandler
    level: DEBUG
    formatter: detailed
    filename: logs/order_auth.log
    maxBytes: 10485760  # 10MB
    backupCount: 5
    encoding: utf-8
    
  error_file:
    class: logging.handlers.RotatingFileHandler
    level: ERROR
    formatter: detailed
    filename: logs/order_auth_error.log
    maxBytes: 5242880  # 5MB
    backupCount: 3
    encoding: utf-8

loggers:
  order.auth:
    level: DEBUG
    handlers: [console, file, error_file]
    propagate: false
    
  order.auth.managers:
    level: DEBUG
    handlers: [console, file]
    propagate: false
    
  order.auth.services:
    level: DEBUG
    handlers: [console, file]
    propagate: false
    
  order.auth.utils:
    level: INFO
    handlers: [console, file]
    propagate: false
    
  order.ui:
    level: INFO
    handlers: [console, file]
    propagate: false

root:
  level: WARNING
  handlers: [console]

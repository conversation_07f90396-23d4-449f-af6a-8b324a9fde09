"""
状态指示器组件

显示连接和登录状态，提供视觉反馈。
"""

from PySide6.QtWidgets import QWidget, QHBoxLayout, QVBoxLayout, QLabel, QProgressBar
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont


class StatusIndicator(QWidget):
    """状态指示器组件
    
    显示连接和登录状态，包括：
    - 状态图标显示
    - 状态文本描述
    - 进度条显示
    - 动态状态更新
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.set_status("disconnected")
    
    def setup_ui(self):
        """设置状态指示器UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(2)
        
        # 状态显示区域
        status_widget = QWidget()
        status_layout = QHBoxLayout(status_widget)
        status_layout.setContentsMargins(0, 0, 0, 0)
        status_layout.setSpacing(8)
        
        # 状态图标
        self.status_icon = QLabel()
        self.status_icon.setFixedSize(16, 16)
        self.status_icon.setAlignment(Qt.AlignCenter)
        self.status_icon.setObjectName("statusIcon")
        
        # 状态文本
        self.status_text = QLabel("未连接")
        self.status_text.setObjectName("statusText")
        
        # 添加到状态布局
        status_layout.addWidget(QLabel("状态:"))
        status_layout.addWidget(self.status_icon)
        status_layout.addWidget(self.status_text)
        status_layout.addStretch()
        
        # 进度条（用于显示加载状态）
        self.progress_bar = QProgressBar()
        self.progress_bar.setFixedHeight(4)
        self.progress_bar.setTextVisible(False)
        self.progress_bar.hide()
        
        # 添加到主布局
        main_layout.addWidget(status_widget)
        main_layout.addWidget(self.progress_bar)
        
        # 设置对象名称
        self.setObjectName("statusIndicator")
    
    def set_status(self, status: str, message: str = ""):
        """设置状态
        
        Args:
            status: 状态类型
            message: 自定义消息
        """
        status_config = {
            "disconnected": ("🔴", "未连接", False),
            "connecting": ("🟡", "连接中", True),
            "connected": ("🟢", "已连接", False),
            "authenticating": ("🟡", "登录中", True),
            "authenticated": ("🟢", "已登录", False),
            "error": ("🔴", "连接失败", False),
            "warning": ("🟠", "警告", False)
        }
        
        if status in status_config:
            icon, default_text, show_progress = status_config[status]
            self.status_icon.setText(icon)
            self.status_text.setText(message or default_text)
            
            if show_progress:
                self.progress_bar.show()
                self.progress_bar.setRange(0, 0)  # 无限进度条
            else:
                self.progress_bar.hide()
        else:
            # 未知状态
            self.status_icon.setText("❓")
            self.status_text.setText(message or "未知状态")
            self.progress_bar.hide()
    
    def set_progress(self, value: int, maximum: int = 100):
        """设置进度值
        
        Args:
            value: 当前进度值
            maximum: 最大进度值
        """
        self.progress_bar.setRange(0, maximum)
        self.progress_bar.setValue(value)
        self.progress_bar.show()
    
    def show_loading(self, message: str = "加载中..."):
        """显示加载状态
        
        Args:
            message: 加载消息
        """
        self.set_status("connecting", message)
    
    def show_success(self, message: str = "操作成功"):
        """显示成功状态
        
        Args:
            message: 成功消息
        """
        self.set_status("authenticated", message)
    
    def show_error(self, message: str = "操作失败"):
        """显示错误状态
        
        Args:
            message: 错误消息
        """
        self.set_status("error", message)
    
    def show_warning(self, message: str = "警告"):
        """显示警告状态
        
        Args:
            message: 警告消息
        """
        self.set_status("warning", message)
    
    def clear_status(self):
        """清除状态显示"""
        self.set_status("disconnected")
    
    def start_pulse_animation(self):
        """开始脉冲动画（用于加载状态）"""
        if not hasattr(self, '_pulse_timer'):
            self._pulse_timer = QTimer()
            self._pulse_timer.timeout.connect(self._pulse_update)
            self._pulse_opacity = 1.0
            self._pulse_direction = -1
        
        self._pulse_timer.start(100)  # 100ms间隔
    
    def stop_pulse_animation(self):
        """停止脉冲动画"""
        if hasattr(self, '_pulse_timer'):
            self._pulse_timer.stop()
            self.setStyleSheet("")  # 清除透明度样式
    
    def _pulse_update(self):
        """更新脉冲动画"""
        self._pulse_opacity += self._pulse_direction * 0.1
        
        if self._pulse_opacity <= 0.3:
            self._pulse_direction = 1
        elif self._pulse_opacity >= 1.0:
            self._pulse_direction = -1
        
        self.setStyleSheet(f"QWidget {{ opacity: {self._pulse_opacity}; }}")
    
    def get_current_status(self) -> dict:
        """获取当前状态信息
        
        Returns:
            dict: 当前状态信息
        """
        return {
            "icon": self.status_icon.text(),
            "text": self.status_text.text(),
            "progress_visible": self.progress_bar.isVisible(),
            "progress_value": self.progress_bar.value() if self.progress_bar.isVisible() else None
        }

<route lang="json5" type="page">
{
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "登录"
  }
}
</route>

<script lang="ts" setup>
import type { IPhoneLoginRequest, IUsernameLoginRequest } from '@/types'
import { getCaptcha } from '@/api/auth'
import { computed, ref, watch, onMounted } from 'vue'
import { useUserStore } from '@/store/user'
import { toast } from '@/utils/toast'

// 登录方式切换
const loginType = ref<'phone' | 'username'>('username')

// 手机号登录表单数据
const phoneForm = ref({
  phone: '',
  code: '',
})

// 用户名密码登录表单数据
const usernameForm = ref({
  username: '',
  password: '',
  captcha: '',
  captchaId: '',
})

// 验证码相关
const captchaData = ref({
  captchaId: '',
  picPath: '',
  captchaLength: 0,
  openCaptcha: false,
})

// 加载状态
const isSending = ref(false) // 发送验证码中
const isLoggingIn = ref(false) // 登录中
const isWxLogging = ref(false) // 微信登录中

// 倒计时相关
const isCountdown = ref(false)
const countdown = ref(60)

// 表单验证错误
const phoneError = ref('')
const codeError = ref('')
const usernameError = ref('')
const passwordError = ref('')

// 用户Store
const userStore = useUserStore()

// 验证规则
const phoneRules = [
  {
    required: true,
    message: '请输入手机号',
  },
  {
    pattern: /^1[3-9]\d{9}$/,
    message: '请输入正确的手机号',
  },
]

const codeRules = [
  {
    required: true,
    message: '请输入验证码',
  },
  {
    pattern: /^\d{6}$/,
    message: '请输入6位数字验证码',
  },
]

// 计算属性
const canSendCode = computed(() => {
  return /^1[3-9]\d{9}$/.test(phoneForm.value.phone)
})

const canPhoneLogin = computed(() => {
  return canSendCode.value && /^\d{6}$/.test(phoneForm.value.code)
})

const canUsernameLogin = computed(() => {
  return usernameForm.value.username.trim() && usernameForm.value.password.trim()
})

const countdownText = computed(() => {
  return isCountdown.value ? `${countdown.value}s后重发` : '发送验证码'
})

// 监听手机号变化，清除错误
watch(() => phoneForm.value.phone, () => {
  phoneError.value = ''
})

// 监听验证码变化，清除错误
watch(() => phoneForm.value.code, () => {
  codeError.value = ''
})

// 监听用户名变化，清除错误
watch(() => usernameForm.value.username, () => {
  usernameError.value = ''
})

// 监听密码变化，清除错误
watch(() => usernameForm.value.password, () => {
  passwordError.value = ''
})

// 切换登录方式
function switchLoginType(type: 'phone' | 'username') {
  loginType.value = type
  // 清除错误信息
  phoneError.value = ''
  codeError.value = ''
  usernameError.value = ''
  passwordError.value = ''

  // 切换到用户名密码登录时，获取验证码
  if (type === 'username') {
    fetchCaptcha()
  }
}

// 获取图形验证码
async function fetchCaptcha() {
  try {
    const res = await getCaptcha()
    if (res.code === 0) {
      captchaData.value = res.data
      usernameForm.value.captchaId = res.data.captchaId
      usernameForm.value.captcha = '' // 清空验证码输入
    }
  }
  catch (error: any) {
    console.error('获取验证码失败:', error)
    toast.error('获取验证码失败')
  }
}

// 刷新验证码
function refreshCaptcha() {
  fetchCaptcha()
}

// 组件挂载时获取验证码
onMounted(() => {
  if (loginType.value === 'username') {
    fetchCaptcha()
  }
})

// 发送验证码
async function handleSendCode() {
  if (!canSendCode.value) {
    toast.error('请输入正确的手机号')
    return
  }

  try {
    isSending.value = true
    await userStore.sendVerificationCode(phoneForm.value.phone)
    startCountdown()
  }
  catch (error: any) {
    console.error('发送验证码失败:', error)
    toast.error(error.message || '发送验证码失败')
  }
  finally {
    isSending.value = false
  }
}

// 开始倒计时
function startCountdown() {
  isCountdown.value = true
  countdown.value = 60

  const timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      isCountdown.value = false
      clearInterval(timer)
    }
  }, 1000)
}

// 手机号登录
async function handlePhoneLogin() {
  if (!canPhoneLogin.value) {
    toast.error('请填写完整的登录信息')
    return
  }

  try {
    isLoggingIn.value = true

    const loginData: IPhoneLoginRequest = {
      phone: phoneForm.value.phone,
      code: phoneForm.value.code,
    }

    await userStore.phoneLogin(loginData)

    // 登录成功后跳转
    uni.switchTab({
      url: '/pages/index/index',
    })
  }
  catch (error: any) {
    console.error('手机号登录失败:', error)
    toast.error(error.message || '登录失败')
  }
  finally {
    isLoggingIn.value = false
  }
}

// 用户名密码登录
async function handleUsernameLogin() {
  if (!canUsernameLogin.value) {
    toast.error('请填写完整的登录信息')
    return
  }

  try {
    isLoggingIn.value = true

    const loginData: IUsernameLoginRequest = {
      username: usernameForm.value.username,
      password: usernameForm.value.password,
      captcha: usernameForm.value.captcha,
      captchaId: usernameForm.value.captchaId,
    }

    await userStore.usernameLogin(loginData)

    // 登录成功后跳转
    uni.switchTab({
      url: '/pages/index/index',
    })
  }
  catch (error: any) {
    console.error('用户名密码登录失败:', error)
    toast.error(error.message || '登录失败')
  }
  finally {
    isLoggingIn.value = false
  }
}

// 微信登录
async function handleWxLogin() {
  try {
    isWxLogging.value = true
    await userStore.newWxLogin()

    // 登录成功后跳转
    uni.switchTab({
      url: '/pages/index/index',
    })
  }
  catch (error: any) {
    console.error('微信登录失败:', error)
    toast.error(error.message || '微信登录失败')
  }
  finally {
    isWxLogging.value = false
  }
}

// 显示用户协议
function showAgreement() {
  uni.showModal({
    title: '用户协议',
    content: '这里是用户协议的内容...',
    showCancel: false,
  })
}

// 显示隐私政策
function showPrivacy() {
  uni.showModal({
    title: '隐私政策',
    content: '这里是隐私政策的内容...',
    showCancel: false,
  })
}
</script>

<template>
  <view class="login-container">
    <!-- 顶部Logo区域 -->
    <view class="logo-section">
      <image class="logo" src="/static/logo.svg" mode="aspectFit" />
      <text class="app-name">
        点价系统
      </text>
    </view>

    <!-- 登录表单 -->
    <view class="login-form">
      <!-- 登录方式选择 -->
      <view class="login-tabs">
        <view class="tab-item" :class="{ active: loginType === 'username' }" @click="switchLoginType('username')">
          账号密码登录
        </view>
        <view class="tab-item" :class="{ active: loginType === 'phone' }" @click="switchLoginType('phone')">
          手机号登录
        </view>
      </view>

      <!-- 手机号登录表单 -->
      <view v-if="loginType === 'phone'" class="form-content">
        <!-- 手机号输入 -->
        <view class="form-item">
          <text class="form-label">
            手机号
          </text>
          <view class="form-control">
            <input v-model="phoneForm.phone" type="number" :maxlength="11" placeholder="请输入手机号"
              placeholder-class="placeholder" class="form-input">
          </view>
        </view>

        <!-- 验证码输入 --> 
        <view class="form-item">
          <text class="form-label">
            验证码
          </text>
          <view class="form-control">
            <input v-model="phoneForm.code" type="number" :maxlength="6" placeholder="6位数字"
              placeholder-class="placeholder" class="form-input">
            <wd-button :disabled="!canSendCode || isCountdown" :loading="isSending" type="text" size="small"
              class="code-btn" @click="handleSendCode">
              {{ countdownText }}
            </wd-button>
          </view>
        </view>

        <!-- 手机号登录按钮 -->
        <wd-button type="primary" block :loading="isLoggingIn" :disabled="!canPhoneLogin" class="login-btn"
          @click="handlePhoneLogin">
          登录
        </wd-button>
      </view>

      <!-- 用户名密码登录表单 -->
      <view v-if="loginType === 'username'" class="form-content">
        <!-- 用户名输入 -->
        <view class="form-item">
          <text class="form-label">
            用户名
          </text>
          <view class="form-control">
            <input v-model="usernameForm.username" type="text" placeholder="请输入用户名" placeholder-class="placeholder"
              class="form-input">
          </view>
        </view>

        <!-- 密码输入 -->
        <view class="form-item">
          <text class="form-label">
            密码
          </text>
          <view class="form-control">
            <input v-model="usernameForm.password" type="password" placeholder="请输入密码" placeholder-class="placeholder"
              class="form-input">
          </view>
        </view>

        <!-- 验证码输入 -->
        <view v-if="captchaData.openCaptcha" class="form-item">
          <text class="form-label">
            验证码
          </text>
          <view class="form-control">
            <input v-model="usernameForm.captcha" type="text" :maxlength="captchaData.captchaLength"
              placeholder="请输入验证码" placeholder-class="placeholder" class="form-input captcha-input">
            <view class="captcha-image" @click="refreshCaptcha">
              <image v-if="captchaData.picPath" :src="captchaData.picPath" mode="aspectFit" class="captcha-img" />
              <text v-else class="captcha-loading">获取中...</text>
            </view>
          </view>
        </view>

        <!-- 用户名密码登录按钮 -->
        <wd-button type="primary" block :loading="isLoggingIn" :disabled="!canUsernameLogin" class="login-btn"
          @click="handleUsernameLogin">
          登录
        </wd-button>
      </view>

      <!-- 微信登录 -->
      <view class="divider">
        <text>或</text>
      </view>

      <wd-button type="default" block :loading="isWxLogging" class="wx-login-btn" @click="handleWxLogin">
        <text class="i-carbon-logo-wechat" />
        微信登录
      </wd-button>
    </view>

    <!-- 用户协议 -->
    <view class="agreement">
      <text class="agreement-text">
        登录即表示同意
        <text class="link" @click="showAgreement">
          《用户协议》
        </text>
        和
        <text class="link" @click="showPrivacy">
          《隐私政策》
        </text>
      </text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  padding: 100rpx 48rpx 60rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  .logo-section {
    text-align: center;
    margin-bottom: 100rpx;

    .logo {
      width: 120rpx;
      height: 120rpx;
      margin-bottom: 30rpx;
    }

    .app-name {
      display: block;
      font-size: 44rpx;
      font-weight: bold;
      color: #fff;
    }
  }

  .login-form {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20rpx;
    padding: 60rpx 40rpx;
    backdrop-filter: blur(10rpx);
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);

    // Tab切换样式
    .login-tabs {
      display: flex;
      margin-bottom: 40rpx;
      border-radius: 12rpx;
      background: #f5f7fa;
      padding: 6rpx;

      .tab-item {
        flex: 1;
        text-align: center;
        padding: 20rpx 0;
        font-size: 28rpx;
        color: #909399;
        border-radius: 8rpx;
        transition: all 0.3s ease;
        cursor: pointer;

        &.active {
          background: #fff;
          color: #667eea;
          font-weight: 600;
          box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.15);
        }

        &:hover:not(.active) {
          color: #667eea;
          background: rgba(102, 126, 234, 0.05);
        }
      }
    }

    .form-content {
      // 动画效果
      animation: fadeIn 0.3s ease-in-out;
    }

    // 统一的表单项样式
    .form-item {
      margin-bottom: 36rpx;

      .form-label {
        display: block;
        font-size: 28rpx;
        color: #606266;
        font-weight: 500;
        margin-bottom: 12rpx;
        line-height: 1;
      }

      .form-control {
        display: flex;
        align-items: center;
        gap: 20rpx;

        .form-input {
          flex: 1;
          height: 88rpx;
          padding: 0 24rpx;
          font-size: 30rpx;
          border: 2rpx solid #e4e7ed;
          border-radius: 12rpx;
          background: #fff;
          box-sizing: border-box;
          transition: all 0.3s ease;

          &:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
          }
        }

        .placeholder {
          color: #c0c4cc;
        }

        .code-btn {
          flex-shrink: 0;
          min-width: 140rpx;
          height: 88rpx;
          color: #667eea;
          font-size: 26rpx;
          border-radius: 12rpx;
          border: 2rpx solid #e4e7ed;
          background: #fff;

          &:disabled {
            color: #c0c4cc;
            background: #f5f7fa;
          }

          &:not(:disabled):hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
          }
        }

        // 验证码输入框样式
        .captcha-input {
          flex: 1;
          margin-right: 20rpx;
        }

        .captcha-image {
          flex-shrink: 0;
          width: 200rpx;
          height: 88rpx;
          border: 2rpx solid #e4e7ed;
          border-radius: 12rpx;
          overflow: hidden;
          cursor: pointer;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #fff;

          &:hover {
            border-color: #667eea;
            box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
          }

          .captcha-img {
            width: 100%;
            height: 100%;
          }

          .captcha-loading {
            font-size: 24rpx;
            color: #909399;
          }
        }
      }
    }

    .login-btn {
      margin: 48rpx 0 36rpx;
      height: 88rpx;
      font-size: 32rpx;
      border-radius: 44rpx;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      font-weight: 600;

      &:disabled {
        background: #e4e7ed;
        opacity: 0.6;
      }
    }

    .divider {
      text-align: center;
      position: relative;
      margin: 36rpx 0;
      color: #909399;
      font-size: 26rpx;

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1rpx;
        background: linear-gradient(90deg, transparent, #e4e7ed, transparent);
      }

      text {
        background: rgba(255, 255, 255, 0.95);
        padding: 0 20rpx;
        position: relative;
        z-index: 1;
      }
    }

    .wx-login-btn {
      height: 80rpx;
      font-size: 30rpx;
      border-radius: 40rpx;
      border: 2rpx solid #07c160;
      color: #07c160;
      background: #fff;

      .i-carbon-logo-wechat {
        font-size: 32rpx;
        margin-right: 8rpx;
      }
    }
  }

  .agreement {
    text-align: center;
    margin-top: 60rpx;

    .agreement-text {
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.8);

      .link {
        color: #87ceeb;
        text-decoration: underline;
      }
    }
  }
}

// 添加动画
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>

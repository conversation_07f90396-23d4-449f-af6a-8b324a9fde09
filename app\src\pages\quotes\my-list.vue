<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "我的报价",
    "navigationStyle": "default"
  }
}
</route>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/store/user'
import {
  getMyQuotationList,
  publishQuotation,
  toggleQuotationStatus,
  deleteQuotation
} from '@/api/quotation'
import type { 
  IQuotationResponse,
  IMyQuotationListRequest,
  QuotationStatus,
  QUOTATION_STATUS_CONFIG
} from '@/types/quotation'

defineOptions({
  name: 'MyQuotationList'
})

// Store
const userStore = useUserStore()

// 页面状态
const isLoading = ref(false)
const isRefreshing = ref(false)
const hasMore = ref(true)

// 筛选状态
const activeFilter = ref<'all' | 'valid' | 'invalid'>('all')
const filterOptions = [
  { label: '全部', value: 'all' },
  { label: '有效报价', value: 'valid' },
  { label: '无效报价', value: 'invalid' }
]

// 列表数据
const quotationList = ref<IQuotationResponse[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 状态配置
const statusConfig = {
  Draft: {
    label: '草稿',
    color: '#909399',
    bgColor: '#f4f4f5',
    description: '报价草稿，未公开发布'
  },
  Active: {
    label: '激活中',
    color: '#67C23A',
    bgColor: '#f0f9ff',
    description: '报价已公开，可被其他用户查看'
  }
}

// 计算属性
const filteredList = computed(() => {
  if (activeFilter.value === 'all') {
    return quotationList.value
  }
  return quotationList.value // API已经根据filter参数返回过滤后的数据
})

const validCount = computed(() => {
  return quotationList.value.filter(q => q.status === 'Active').length
})

const invalidCount = computed(() => {
  return quotationList.value.filter(q => q.status === 'Draft').length
})

// 生命周期
onMounted(() => {
  loadQuotationList()
})

// 加载报价列表
async function loadQuotationList(refresh = false) {
  if (refresh) {
    currentPage.value = 1
    quotationList.value = []
    isRefreshing.value = true
  } else {
    isLoading.value = true
  }

  try {
    const params: IMyQuotationListRequest = {
      page: currentPage.value,
      pageSize: pageSize.value
    }

    // 根据筛选条件设置filter参数
    if (activeFilter.value === 'valid') {
      params.filter = 'valid'
    } else if (activeFilter.value === 'invalid') {
      params.filter = 'invalid'
    }

    const res = await getMyQuotationList(params)
    const { list, total: totalCount } = res.data

    if (refresh) {
      quotationList.value = list
    } else {
      quotationList.value.push(...list)
    }

    total.value = totalCount
    hasMore.value = quotationList.value.length < totalCount

  } catch (error) {
    console.error('加载报价列表失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error'
    })
  } finally {
    isLoading.value = false
    isRefreshing.value = false
  }
}

// 加载更多
async function loadMore() {
  if (!hasMore.value || isLoading.value) return
  
  currentPage.value++
  await loadQuotationList()
}

// 下拉刷新
async function onRefresh() {
  await loadQuotationList(true)
}

// 筛选改变
async function onFilterChange(filter: 'all' | 'valid' | 'invalid') {
  activeFilter.value = filter
  await loadQuotationList(true)
}

// 创建新报价
function createQuotation() {
  uni.navigateTo({
    url: '/pages/quotes/edit'
  })
}

// 编辑报价
function editQuotation(quotation: IQuotationResponse) {
  uni.navigateTo({
    url: `/pages/quotes/edit?id=${quotation.id}`
  })
}

// 查看详情
function viewDetail(quotation: IQuotationResponse) {
  uni.navigateTo({
    url: `/pages/quotes/detail?id=${quotation.id}`
  })
}

// 发布报价
async function publishQuotationItem(quotation: IQuotationResponse) {
  try {
    uni.showLoading({ title: '发布中...' })

    // 发布报价需要设置过期时间，这里使用默认的7天后过期
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 7)

    await publishQuotation({
      id: quotation.id,
      expiresAt: expiresAt.toISOString()
    })

    uni.showToast({
      title: '发布成功',
      icon: 'success'
    })

    // 刷新列表
    await loadQuotationList(true)

  } catch (error) {
    console.error('发布报价失败:', error)
    uni.showToast({
      title: '发布失败',
      icon: 'error'
    })
  } finally {
    uni.hideLoading()
  }
}

// 切换报价状态（激活 <-> 草稿）
async function toggleQuotationStatusItem(quotation: IQuotationResponse) {
  const isActive = quotation.status === 'Active'
  const actionText = isActive ? '设为草稿' : '激活报价'
  const confirmText = isActive ? '确定要将此报价设为草稿吗？设为草稿后将不再对外展示。' : '确定要激活此报价吗？激活后将对外展示。'

  const res = await new Promise<boolean>((resolve) => {
    uni.showModal({
      title: `确认${actionText}`,
      content: confirmText,
      success: (modalRes) => {
        resolve(modalRes.confirm)
      }
    })
  })

  if (!res) return

  try {
    uni.showLoading({ title: `${actionText}中...` })

    await toggleQuotationStatus(quotation.id)

    uni.showToast({
      title: `${actionText}成功`,
      icon: 'success'
    })

    // 刷新列表
    await loadQuotationList(true)

  } catch (error) {
    console.error('切换状态失败:', error)
    uni.showToast({
      title: `${actionText}失败`,
      icon: 'error'
    })
  } finally {
    uni.hideLoading()
  }
}

// 格式化价格显示
function formatPrice(quotation: IQuotationResponse): string {
  if (quotation.priceType === 'Fixed') {
    return `¥ ${quotation.price.toFixed(2)}`
  } else if (quotation.priceType === 'Basis' && quotation.instrumentRef) {
    if (quotation.price >= 0) {
      return `${quotation.instrumentRef.instrument_id} + ${quotation.price}`
    } else {
      return `${quotation.instrumentRef.instrument_id} ${quotation.price}`
    }
  } else {
    return quotation.price.toString()
  }
}

// 删除报价
async function deleteQuotationItem(quotation: IQuotationResponse) {
  const res = await new Promise<boolean>((resolve) => {
    uni.showModal({
      title: '确认删除',
      content: '确定要删除这个报价草稿吗？删除后无法恢复。',
      success: (modalRes) => {
        resolve(modalRes.confirm)
      }
    })
  })

  if (!res) return

  try {
    uni.showLoading({ title: '删除中...' })
    
    await deleteQuotation(quotation.id)
    
    uni.showToast({
      title: '删除成功',
      icon: 'success'
    })
    
    // 刷新列表
    await loadQuotationList(true)
    
  } catch (error) {
    console.error('删除报价失败:', error)
    uni.showToast({
      title: '删除失败',
      icon: 'error'
    })
  } finally {
    uni.hideLoading()
  }
}

// 格式化剩余时间
function formatRemainingTime(quotation: IQuotationResponse): string {
  if (quotation.status !== 'Active') return ''
  
  if (quotation.isExpired) {
    return '已过期'
  }
  
  if (quotation.remainingHours <= 0) {
    return '即将过期'
  } else if (quotation.remainingHours < 24) {
    return `剩余 ${quotation.remainingHours} 小时`
  } else {
    const days = Math.floor(quotation.remainingHours / 24)
    return `剩余 ${days} 天`
  }
}

// 获取操作按钮
function getActionButtons(quotation: IQuotationResponse) {
  const buttons = []
  
  if (quotation.status === 'Draft') {
    buttons.push(
      { text: '编辑', type: 'primary', action: () => editQuotation(quotation) },
      { text: '发布', type: 'success', action: () => publishQuotationItem(quotation) },
      { text: '删除', type: 'danger', action: () => deleteQuotationItem(quotation) }
    )
  } else if (quotation.status === 'Active') {
    buttons.push(
      { text: '查看', type: 'info', action: () => viewDetail(quotation) },
      { text: '设为草稿', type: 'warning', action: () => toggleQuotationStatusItem(quotation) }
    )
  } else {
    buttons.push(
      { text: '查看', type: 'info', action: () => viewDetail(quotation) }
    )
  }
  
  return buttons
}
</script>

<template>
  <view class="page-container">
    <!-- 顶部操作栏 -->
    <view class="header-section">
      <wd-button type="primary" size="large" @click="createQuotation">
        创建新报价
      </wd-button>
      
      <!-- 筛选标签 -->
      <view class="filter-tabs">
        <view 
          v-for="option in filterOptions" 
          :key="option.value"
          class="filter-tab"
          :class="{ active: activeFilter === option.value }"
          @click="onFilterChange(option.value as any)"
        >
          {{ option.label }}
          <text v-if="option.value === 'valid'" class="count-badge">{{ validCount }}</text>
          <text v-if="option.value === 'invalid'" class="count-badge">{{ invalidCount }}</text>
        </view>
      </view>
    </view>

    <!-- 列表内容 -->
    <scroll-view 
      class="scroll-container"
      scroll-y
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="loadMore"
    >
      <view class="list-container">
        <!-- 报价列表 -->
        <view v-if="quotationList.length > 0" class="quotation-list">
          <view 
            v-for="quotation in filteredList" 
            :key="quotation.id"
            class="quotation-card"
            @click="viewDetail(quotation)"
          >
            <!-- 状态标签 -->
            <view 
              class="status-tag"
              :style="{ 
                color: statusConfig[quotation.status].color,
                backgroundColor: statusConfig[quotation.status].bgColor 
              }"
            >
              {{ statusConfig[quotation.status].label }}
            </view>
            
            <!-- 主要信息 -->
            <view class="card-header">
              <text class="quotation-title">{{ quotation.title }}</text>
              <text class="quotation-price">{{ formatPrice(quotation) }}</text>
            </view>
            
            <!-- 次要信息 -->
            <view class="card-content">
              <view class="info-row">
                <text class="label">商品：</text>
                <text class="value">{{ quotation.commodity?.name || '-' }}</text>
              </view>
              <view class="info-row">
                <text class="label">地点：</text>
                <text class="value">{{ quotation.deliveryLocation }}</text>
              </view>
              <view v-if="quotation.brand" class="info-row">
                <text class="label">品牌：</text>
                <text class="value">{{ quotation.brand }}</text>
              </view>
            </view>
            
            <!-- 时间信息 -->
            <view class="card-footer">
              <text class="create-time">{{ quotation.createdAt.split('T')[0] }}</text>
              <text 
                v-if="quotation.status === 'Active'"
                class="remaining-time"
                :class="{ expired: quotation.isExpired }"
              >
                {{ formatRemainingTime(quotation) }}
              </text>
            </view>
            
            <!-- 操作按钮 -->
            <view class="action-buttons" @click.stop>
              <wd-button
                v-for="button in getActionButtons(quotation)"
                :key="button.text"
                :type="button.type"
                size="small"
                @click="button.action"
              >
                {{ button.text }}
              </wd-button>
            </view>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view v-else-if="!isLoading" class="empty-state">
          <wd-img
            src="/static/images/empty-quotation.png"
            width="200rpx"
            height="200rpx"
            mode="aspectFit"
          />
          <text class="empty-text">暂无报价</text>
          <wd-button type="primary" @click="createQuotation">
            创建第一个报价
          </wd-button>
        </view>
        
        <!-- 加载更多 -->
        <view v-if="isLoading && quotationList.length > 0" class="loading-more">
          <wd-loading size="24rpx" />
          <text class="loading-text">加载中...</text>
        </view>
        
        <!-- 没有更多 -->
        <view v-if="!hasMore && quotationList.length > 0" class="no-more">
          <text>没有更多数据了</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<style lang="scss" scoped>
.page-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.header-section {
  background: white;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.filter-tabs {
  display: flex;
  margin-top: 20rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  padding: 8rpx;
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: 16rpx 8rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s;
  position: relative;
  
  &.active {
    background: white;
    color: #007aff;
    font-weight: 500;
    box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  }
}

.count-badge {
  position: absolute;
  top: 8rpx;
  right: 16rpx;
  background: #ff4757;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  text-align: center;
  line-height: 1;
}

.scroll-container {
  flex: 1;
  padding: 20rpx;
}

.quotation-list {
  .quotation-card {
    background: white;
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
    position: relative;
    
    .status-tag {
      position: absolute;
      top: 16rpx;
      right: 16rpx;
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      font-size: 22rpx;
      font-weight: 500;
    }
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16rpx;
      padding-right: 120rpx; // 为状态标签留空间
      
      .quotation-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        flex: 1;
        line-height: 1.4;
      }
      
      .quotation-price {
        font-size: 36rpx;
        font-weight: 700;
        color: #007aff;
        margin-left: 16rpx;
      }
    }
    
    .card-content {
      margin-bottom: 16rpx;
      
      .info-row {
        display: flex;
        margin-bottom: 8rpx;
        font-size: 26rpx;
        
        .label {
          color: #666;
          width: 80rpx;
        }
        
        .value {
          color: #333;
          flex: 1;
        }
      }
    }
    
    .card-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16rpx;
      font-size: 24rpx;
      
      .create-time {
        color: #999;
      }
      
      .remaining-time {
        color: #67C23A;
        
        &.expired {
          color: #F56C6C;
        }
      }
    }
    
    .action-buttons {
      display: flex;
      gap: 16rpx;
      justify-content: flex-end;
      
      :deep(.wd-button) {
        min-width: 120rpx;
      }
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  
  .empty-text {
    font-size: 28rpx;
    color: #999;
    margin: 24rpx 0 40rpx;
  }
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  
  .loading-text {
    margin-left: 16rpx;
    font-size: 26rpx;
    color: #999;
  }
}

.no-more {
  text-align: center;
  padding: 40rpx;
  font-size: 26rpx;
  color: #999;
}
</style>
#!/usr/bin/env python
"""
步骤2验证测试脚本
验证新的合约系统是否能与主引擎正确集成
"""
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_engine_integration():
    """测试与主引擎的集成"""
    print("=== 验证新合约系统与主引擎集成 ===\n")
    
    try:
        # 测试引擎能否正确导入新的合约系统
        print("1. 测试引擎导入...")
        from engine import Engine
        print("[OK] 成功导入Engine类")
        
        # 测试能否获取合约信息
        print("\n2. 测试合约获取...")
        from core.instrument_manager import get_instrument
        instruments = get_instrument()
        
        if instruments:
            print(f"[OK] 成功获取 {len(instruments)} 个合约")
            print(f"[OK] 合约格式示例: {instruments[0]}")
            
            # 验证格式
            if isinstance(instruments[0], tuple) and len(instruments[0]) == 2:
                symbol, exchange = instruments[0]
                print(f"[OK] 合约格式正确: symbol={symbol}, exchange={exchange}")
            else:
                print(f"[FAIL] 合约格式不正确: {type(instruments[0])}")
                return False
        else:
            print("[FAIL] 未获取到合约信息")
            return False
        
        # 测试引擎初始化（不实际启动）
        print("\n3. 测试引擎初始化...")
        try:
            engine = Engine()
            print("[OK] 引擎初始化成功")
            print(f"[OK] ServerManager已集成: {hasattr(engine, 'server_manager')}")
        except Exception as e:
            print(f"[FAIL] 引擎初始化失败: {e}")
            return False
        
        print("\n4. 验证数据库依赖移除...")
        try:
            # 确认旧的instrument模块已被移除
            import importlib.util
            spec = importlib.util.find_spec("engine.instrument")
            if spec is None:
                print("[OK] 旧的instrument模块已被移除")
            else:
                print("[FAIL] 旧的instrument模块仍然存在")
                return False
        except Exception as e:
            print(f"[OK] 模块检查通过: {e}")
        
        # 确认PyMySQL不再被导入
        try:
            import pymysql
            print("[WARN] PyMySQL仍然可以导入，但系统不再使用它")
        except ImportError:
            print("[OK] PyMySQL已从环境中移除")
        
        print("\n=== 验证完成 ===")
        print("[OK] 新合约系统与主引擎集成成功")
        print("[OK] 数据库依赖已成功移除")
        print("[OK] 系统可以无数据库独立运行")
        return True
        
    except Exception as e:
        print(f"[FAIL] 集成验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_cache_persistence():
    """测试缓存持久性"""
    print("\n=== 缓存持久性测试 ===")
    
    try:
        from utils.cache_manager import CacheManager
        from core.instrument_manager import InstrumentManager
        
        # 清空缓存
        cache_manager = CacheManager()
        cache_manager.clear_cache()
        print("[OK] 清空旧缓存")
        
        # 第一次加载（应从API获取）
        print("\n1. 第一次加载（从API）...")
        manager1 = InstrumentManager()
        success1 = manager1.load_instruments_sync()
        
        if success1:
            count1 = len(manager1.get_instruments())
            print(f"[OK] 第一次加载成功: {count1} 个合约")
        else:
            print("[FAIL] 第一次加载失败")
            return False
        
        # 第二次加载（应从缓存获取）
        print("\n2. 第二次加载（从缓存）...")
        manager2 = InstrumentManager()
        success2 = manager2.load_instruments_sync()
        
        if success2:
            count2 = len(manager2.get_instruments())
            print(f"[OK] 第二次加载成功: {count2} 个合约")
            
            if count1 == count2:
                print("[OK] 缓存数据一致性验证通过")
            else:
                print(f"[WARN] 缓存数据不一致: 第一次{count1}, 第二次{count2}")
        else:
            print("[FAIL] 第二次加载失败")
            return False
        
        print("[OK] 缓存持久性测试通过")
        return True
        
    except Exception as e:
        print(f"[FAIL] 缓存持久性测试失败: {e}")
        return False


if __name__ == "__main__":
    print("开始步骤2验证测试...\n")
    
    # 运行集成测试
    integration_success = test_engine_integration()
    
    # 运行缓存持久性测试
    cache_success = test_cache_persistence()
    
    if integration_success and cache_success:
        print("\n[SUCCESS] 步骤2验证通过！新合约系统完全正常工作。")
        print("[INFO] 可以继续进行后续开发工作。")
        exit(0)
    else:
        print("\n[ERROR] 步骤2验证失败，请检查并修复问题。")
        exit(1)
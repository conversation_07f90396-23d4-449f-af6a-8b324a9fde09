"""
认证模块异常类定义

定义了认证相关的所有异常类，提供清晰的错误层次结构和详细的错误信息。
"""

from typing import Optional, Dict, Any


class AuthException(Exception):
    """认证异常基类
    
    所有认证相关异常的基类，提供统一的异常处理接口。
    """
    
    def __init__(self, message: str, error_code: Optional[str] = None, 
                 details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def __str__(self):
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "exception_type": self.__class__.__name__,
            "message": self.message,
            "error_code": self.error_code,
            "details": self.details
        }


class LoginFailedException(AuthException):
    """登录失败异常
    
    当用户登录失败时抛出，包含具体的失败原因。
    """
    
    def __init__(self, message: str, error_code: Optional[str] = None, 
                 login_type: Optional[str] = None, username: Optional[str] = None):
        super().__init__(message, error_code)
        self.login_type = login_type
        self.username = username
        self.details.update({
            "login_type": login_type,
            "username": username
        })


class TokenException(AuthException):
    """Token相关异常基类"""
    
    def __init__(self, message: str, error_code: Optional[str] = None, 
                 token_type: Optional[str] = None):
        super().__init__(message, error_code)
        self.token_type = token_type
        self.details.update({
            "token_type": token_type
        })


class TokenExpiredException(TokenException):
    """Token过期异常
    
    当Token过期且无法刷新时抛出。
    """
    
    def __init__(self, message: str = "Token已过期", token_type: str = "access_token"):
        super().__init__(message, "TOKEN_EXPIRED", token_type)


class TokenInvalidException(TokenException):
    """Token无效异常
    
    当Token格式错误或验证失败时抛出。
    """
    
    def __init__(self, message: str = "Token无效", token_type: str = "access_token"):
        super().__init__(message, "TOKEN_INVALID", token_type)


class TokenRefreshFailedException(TokenException):
    """Token刷新失败异常
    
    当Token刷新操作失败时抛出。
    """
    
    def __init__(self, message: str = "Token刷新失败", reason: Optional[str] = None):
        super().__init__(message, "TOKEN_REFRESH_FAILED", "refresh_token")
        self.reason = reason
        self.details.update({
            "reason": reason
        })


class NetworkException(AuthException):
    """网络异常
    
    当网络请求失败时抛出。
    """
    
    def __init__(self, message: str, error_code: Optional[str] = None, 
                 url: Optional[str] = None, status_code: Optional[int] = None):
        super().__init__(message, error_code or "NETWORK_ERROR")
        self.url = url
        self.status_code = status_code
        self.details.update({
            "url": url,
            "status_code": status_code
        })


class TimeoutException(NetworkException):
    """超时异常
    
    当网络请求超时时抛出。
    """
    
    def __init__(self, message: str = "请求超时", url: Optional[str] = None, 
                 timeout_seconds: Optional[int] = None):
        super().__init__(message, "TIMEOUT_ERROR", url)
        self.timeout_seconds = timeout_seconds
        self.details.update({
            "timeout_seconds": timeout_seconds
        })


class ValidationException(AuthException):
    """验证异常
    
    当输入验证失败时抛出。
    """
    
    def __init__(self, message: str, field_name: Optional[str] = None, 
                 field_value: Optional[str] = None):
        super().__init__(message, "VALIDATION_ERROR")
        self.field_name = field_name
        self.field_value = field_value
        self.details.update({
            "field_name": field_name,
            "field_value": field_value
        })


class SMSException(AuthException):
    """短信相关异常基类"""
    
    def __init__(self, message: str, error_code: Optional[str] = None, 
                 phone: Optional[str] = None):
        super().__init__(message, error_code)
        self.phone = phone
        self.details.update({
            "phone": phone
        })


class SMSCodeInvalidException(SMSException):
    """短信验证码无效异常"""
    
    def __init__(self, message: str = "验证码错误", phone: Optional[str] = None):
        super().__init__(message, "SMS_CODE_INVALID", phone)


class SMSCodeExpiredException(SMSException):
    """短信验证码过期异常"""
    
    def __init__(self, message: str = "验证码已过期", phone: Optional[str] = None):
        super().__init__(message, "SMS_CODE_EXPIRED", phone)


class SMSSendFailedException(SMSException):
    """短信发送失败异常"""
    
    def __init__(self, message: str = "短信发送失败", phone: Optional[str] = None, 
                 reason: Optional[str] = None):
        super().__init__(message, "SMS_SEND_FAILED", phone)
        self.reason = reason
        self.details.update({
            "reason": reason
        })


class ConfigException(AuthException):
    """配置异常
    
    当配置加载或解析失败时抛出。
    """
    
    def __init__(self, message: str, config_file: Optional[str] = None, 
                 config_key: Optional[str] = None):
        super().__init__(message, "CONFIG_ERROR")
        self.config_file = config_file
        self.config_key = config_key
        self.details.update({
            "config_file": config_file,
            "config_key": config_key
        })


class CryptoException(AuthException):
    """加密异常
    
    当加密或解密操作失败时抛出。
    """
    
    def __init__(self, message: str, operation: Optional[str] = None):
        super().__init__(message, "CRYPTO_ERROR")
        self.operation = operation
        self.details.update({
            "operation": operation
        })


class WebSocketException(AuthException):
    """WebSocket异常
    
    当WebSocket连接或通信失败时抛出。
    """
    
    def __init__(self, message: str, error_code: Optional[str] = None, 
                 ws_url: Optional[str] = None):
        super().__init__(message, error_code or "WEBSOCKET_ERROR")
        self.ws_url = ws_url
        self.details.update({
            "ws_url": ws_url
        })


# 异常处理工具函数
def handle_auth_exception(func):
    """认证异常处理装饰器
    
    用于统一处理认证相关的异常，记录日志并转换为标准格式。
    """
    from functools import wraps
    import logging
    
    logger = logging.getLogger(__name__)
    
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except AuthException as e:
            logger.error(f"认证异常: {e}")
            raise
        except Exception as e:
            logger.error(f"未知异常: {e}")
            raise AuthException(f"操作失败: {str(e)}", "UNKNOWN_ERROR")
    
    return wrapper


def handle_async_auth_exception(func):
    """异步认证异常处理装饰器"""
    from functools import wraps
    import logging
    
    logger = logging.getLogger(__name__)
    
    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except AuthException as e:
            logger.error(f"认证异常: {e}")
            raise
        except Exception as e:
            logger.error(f"未知异常: {e}")
            raise AuthException(f"操作失败: {str(e)}", "UNKNOWN_ERROR")
    
    return wrapper


# 异常映射字典
EXCEPTION_MAPPING = {
    "INVALID_CREDENTIALS": LoginFailedException,
    "TOKEN_EXPIRED": TokenExpiredException,
    "TOKEN_INVALID": TokenInvalidException,
    "TOKEN_REFRESH_FAILED": TokenRefreshFailedException,
    "NETWORK_ERROR": NetworkException,
    "TIMEOUT_ERROR": TimeoutException,
    "VALIDATION_ERROR": ValidationException,
    "SMS_CODE_INVALID": SMSCodeInvalidException,
    "SMS_CODE_EXPIRED": SMSCodeExpiredException,
    "SMS_SEND_FAILED": SMSSendFailedException,
    "CONFIG_ERROR": ConfigException,
    "CRYPTO_ERROR": CryptoException,
    "WEBSOCKET_ERROR": WebSocketException
}


def create_exception_from_code(error_code: str, message: str, **kwargs) -> AuthException:
    """根据错误码创建对应的异常实例"""
    exception_class = EXCEPTION_MAPPING.get(error_code, AuthException)
    return exception_class(message, error_code, **kwargs)

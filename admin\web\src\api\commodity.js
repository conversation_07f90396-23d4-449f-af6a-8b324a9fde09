import service from '@/utils/request'

// @Tags Commodity
// @Summary 创建商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Commodity true "商品模型"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /commodity/createCommodity [post]
export const createCommodity = (data) => {
  return service({
    url: '/commodity/createCommodity',
    method: 'post',
    data
  })
}

// @Tags Commodity
// @Summary 删除商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除商品"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /commodity/deleteCommodity [delete]
export const deleteCommodity = (params) => {
  return service({
    url: '/commodity/deleteCommodity',
    method: 'delete',
    params
  })
}

// @Tags Commodity
// @Summary 批量删除商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除商品"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /commodity/deleteCommodityByIds [delete]
export const deleteCommodityByIds = (params) => {
  return service({
    url: '/commodity/deleteCommodityByIds',
    method: 'delete',
    params
  })
}

// @Tags Commodity
// @Summary 更新商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Commodity true "商品模型"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /commodity/updateCommodity [put]
export const updateCommodity = (data) => {
  return service({
    url: '/commodity/updateCommodity',
    method: 'put',
    data
  })
}

// @Tags Commodity
// @Summary 用id查询商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.Commodity true "用id查询商品"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /commodity/findCommodity [get]
export const findCommodity = (params) => {
  return service({
    url: '/commodity/findCommodity',
    method: 'get',
    params
  })
}

// @Tags Commodity
// @Summary 分页获取商品列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取商品列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /commodity/getCommodityList [get]
export const getCommodityList = (params) => {
  return service({
    url: '/commodity/getCommodityList',
    method: 'get',
    params
  })
}

// @Tags Commodity
// @Summary 获取所有商品列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /commodity/getAllCommodityList [get]
export const getAllCommodityList = () => {
  return service({
    url: '/commodity/getAllCommodityList',
    method: 'get'
  })
}
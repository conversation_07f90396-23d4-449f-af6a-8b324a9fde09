"""
VNPY适配器 - 封装CTP行情接口
负责与CTP行情接口的实际交互（连接、订阅、断开等）
合并了原md.py和vnpy_adapter.py的功能
"""
import logging
from datetime import datetime
from typing import List, Optional, Callable, Dict, Any
import time
import sys
from pathlib import Path

from vnpy.event import EventEngine, Event
from vnpy.trader.constant import Exchange
from vnpy.trader.object import TickData, LogData
from vnpy.trader.event import EVENT_TICK, EVENT_LOG
from vnpy.trader.utility import get_folder_path, ZoneInfo
from vnpy_ctp.api import MdApi

# 常量定义
MAX_FLOAT = sys.float_info.max  # 浮点数极限值
CHINA_TZ = ZoneInfo("Asia/Shanghai")  # 中国时区


class VnpyAdapter(MdApi):
    """VNPY CTP适配器 - 提供统一的行情接口"""
    
    def __init__(self, event_engine: EventEngine = None):
        """
        初始化适配器
        
        Args:
            event_engine: 事件引擎实例，如果为None则创建新的
        """
        super().__init__()
        
        # 事件引擎
        self.engine = event_engine or EventEngine()
        
        # CTP API 请求ID
        self.reqid: int = 0
        
        # 连接状态
        self.connect_status: bool = False
        self.login_status: bool = False
        
        # 连接参数
        self.addresses: List[str] = []
        self.userid: str = ""
        self.password: str = ""
        self.brokerid: str = ""
        
        # 订阅管理
        self.subscribed: set = set()
        self.symbol_exchange_map: Dict[str, Exchange] = {}
        
        # 回调函数
        self.tick_callback: Optional[Callable[[TickData], None]] = None
        self.log_callback: Optional[Callable[[LogData], None]] = None
        self.connection_callback: Optional[Callable[[bool], None]] = None
        
        # 初始化事件处理
        self._setup_event_handlers()
    
    def _setup_event_handlers(self):
        """设置事件处理器"""
        self.engine.register(EVENT_TICK, self._on_tick_event)
        self.engine.register(EVENT_LOG, self._on_log_event)
    
    def _on_tick_event(self, event: Event):
        """处理行情数据事件"""
        tick_data: TickData = event.data
        if self.tick_callback:
            try:
                self.tick_callback(tick_data)
            except Exception as e:
                self.write_log(f"行情回调处理错误: {e}")
    
    def _on_log_event(self, event: Event):
        """处理日志事件"""
        log_data: LogData = event.data
        if self.log_callback:
            try:
                self.log_callback(log_data)
            except Exception as e:
                print(f"日志回调处理错误: {e}")
        else:
            # 默认打印日志
            print(f"[{datetime.now().strftime('%H:%M:%S')}] {log_data.msg}")
    
    def set_tick_callback(self, callback: Callable[[TickData], None]):
        """设置行情数据回调函数"""
        self.tick_callback = callback
    
    def set_log_callback(self, callback: Callable[[LogData], None]):
        """设置日志回调函数"""
        self.log_callback = callback
    
    def set_connection_callback(self, callback: Callable[[bool], None]):
        """设置连接状态回调函数"""
        self.connection_callback = callback
    
    def write_log(self, msg: str) -> None:
        """
        写入日志事件
        """
        log: LogData = LogData(msg=msg, gateway_name="CTP")
        event = Event(EVENT_LOG, log)
        self.engine.put(event)
    
    def write_error(self, msg: str, error: dict) -> None:
        """
        写入错误日志事件
        """
        error_msg: str = f"{msg}，错误信息：{error['ErrorMsg']}"
        log: LogData = LogData(msg=error_msg, level=logging.ERROR, gateway_name="CTP")
        event = Event(EVENT_LOG, log)
        self.engine.put(event)
    
    def connect(self, addresses: List[str], user_id: str, password: str, broker_id: str) -> bool:
        """
        连接到CTP服务器
        
        Args:
            addresses: 行情服务器地址列表
            user_id: 用户ID
            password: 密码
            broker_id: 期货公司ID
            
        Returns:
            bool: 连接是否成功启动
        """
        try:
            # 保存连接参数
            self.addresses = addresses
            self.userid = user_id
            self.password = password
            self.brokerid = broker_id

            # 启动事件引擎（如果还没启动）
            if not self.engine._active:
                try:
                    self.engine.start()
                except Exception as e:
                    # 事件引擎可能已经启动，忽略错误
                    self.write_log("启动事件引擎失败")
                    pass

            
            # 禁止重复发起连接，会导致异常崩溃
            if not self.connect_status:
                # 创建API
                path: Path = get_folder_path("md")
                self.createFtdcMdApi((str(path) + "\\Md").encode("GBK"))
                
                # 注册服务器地址
                def format_address(address: str) -> str:
                    if (
                            (not address.startswith("tcp://"))
                            and (not address.startswith("ssl://"))
                            and (not address.startswith("socks"))
                    ):
                        address = "tcp://" + address
                    return address
                
                for addr in addresses:
                    self.registerFront(format_address(addr))
                
                # 初始化
                self.init()
                self.connect_status = True
                
                self.write_log(f"开始连接CTP服务器: {addresses}")
                self.write_log(f"期货公司: {broker_id}, 用户: {user_id}")
                
                return True
            else:
                self.write_log("已经处于连接状态，无需重复连接")
                return False
                
        except Exception as e:
            self.write_log(f"连接CTP服务器失败: {e}")
            return False
    
    def login(self) -> None:
        """用户登录"""
        ctp_req: dict = {
            "UserID": self.userid,
            "Password": self.password,
            "BrokerID": self.brokerid
        }
        
        self.reqid += 1
        self.reqUserLogin(ctp_req, self.reqid)
    
    def subscribe(self, symbol: str, exchange: Exchange) -> bool:
        """
        订阅行情
        
        Args:
            symbol: 合约代码
            exchange: 交易所
            
        Returns:
            bool: 订阅是否成功
        """
        try:
            if not self.connect_status:
                self.write_log(f"无法订阅{symbol}: 未连接到服务器")
                return False
            
            # 保存合约与交易所的映射关系
            self.symbol_exchange_map[symbol] = exchange
            
            # 如果已登录，直接发送订阅请求
            if self.login_status:
                self.write_log(f"订阅合约: {symbol}")
                self.subscribeMarketData(symbol)
            
            # 无论是否已登录，都记录该合约已被订阅
            self.subscribed.add(symbol)
            return True
            
        except Exception as e:
            self.write_log(f"订阅合约{symbol}失败: {e}")
            return False
    
    def subscribe_batch(self, symbols: List[tuple]) -> int:
        """
        批量订阅行情
        
        Args:
            symbols: 合约列表，格式为[(symbol, exchange_str), ...]
            
        Returns:
            int: 成功订阅的合约数量
        """
        success_count = 0
        
        for symbol, exchange_str in symbols:
            try:
                exchange = Exchange(exchange_str)
                if self.subscribe(symbol, exchange):
                    success_count += 1
            except Exception as e:
                self.write_log(f"订阅合约{symbol}失败: {e}")
        
        self.write_log(f"批量订阅完成: {success_count}/{len(symbols)}")
        return success_count
    
    def unsubscribe(self, symbol: str):
        """取消订阅"""
        if symbol in self.subscribed:
            self.subscribed.remove(symbol)
            if symbol in self.symbol_exchange_map:
                del self.symbol_exchange_map[symbol]
            self.write_log(f"取消订阅: {symbol}")
    
    def close(self) -> None:
        """关闭连接"""
        try:
            if self.connect_status:
                self.exit()
                self.connect_status = False
                self.login_status = False
                
                if self.connection_callback:
                    self.connection_callback(False)
                
                self.write_log("已断开CTP连接")
            
            if self.engine and self.engine._active:
                try:
                    self.engine.stop()
                except Exception as e:
                    self.write_log(f"断开连接时发生错误: {e}")
                
        except Exception as e:
            self.write_log(f"断开连接时发生错误: {e}")
    
    def get_connection_status(self) -> Dict[str, Any]:
        """获取连接状态信息"""
        return {
            "is_connected": self.connect_status,
            "is_logged_in": self.login_status,
            "subscribed_count": len(self.subscribed),
            "broker_id": self.brokerid,
            "user_id": self.userid
        }
    
    def get_subscribed_symbols(self) -> List[str]:
        """获取已订阅的合约列表"""
        return list(self.subscribed)
    

    
    def start_engine(self):
        """启动事件引擎"""
        if not self.engine._active:
            self.engine.start()
            self.write_log("事件引擎已启动")
    
    def stop_engine(self):
        """停止事件引擎"""
        if self.engine and self.engine._active:
            self.engine.stop()
            self.write_log("事件引擎已停止")
    
    # --------- CTP回调函数 ---------
    
    def onFrontConnected(self) -> None:
        """服务器连接成功回报"""
        self.write_log("行情服务器连接成功")
        self.login()
        
        if self.connection_callback:
            self.connection_callback(True)
    
    def onFrontDisconnected(self, reason: int) -> None:
        """服务器连接断开回报"""
        self.login_status = False
        self.connect_status = False
        self.write_log(f"行情服务器连接断开，原因{reason}")
        
        if self.connection_callback:
            self.connection_callback(False)
        
    
    def onRspUserLogin(self, data: dict, error: dict, reqid: int, last: bool) -> None:
        """用户登录请求回报"""
        if not error or not error["ErrorID"]:
            self.login_status = True
            self.write_log("行情服务器登录成功")
            
            # 登录成功后，重新订阅之前的合约
            for symbol in self.subscribed:
                self.write_log(f"重新订阅合约{symbol}")
                self.subscribeMarketData(symbol)
        else:
            self.write_error("行情服务器登录失败", error)
    
    def onRspError(self, error: dict, reqid: int, last: bool) -> None:
        """请求报错回报"""
        self.write_error("行情接口报错", error)
    
    def onRspSubMarketData(self, data: dict, error: dict, reqid: int, last: bool) -> None:
        """订阅行情回报"""
        if not error or not error["ErrorID"]:
            self.write_log(f"行情订阅成功: {data}")
            return
        
        self.write_error("行情订阅失败", error)
    
    def onRtnDepthMarketData(self, data: dict) -> None:
        """行情数据推送"""
        # 过滤没有时间戳的异常行情数据
        if not data["UpdateTime"]:
            return
        
        # 过滤还没有收到合约数据前的行情推送
        symbol: str = data["InstrumentID"]
        if symbol not in self.symbol_exchange_map:
            return
        
        date_str: str = data["ActionDay"]
        
        timestamp: str = f"{date_str} {data['UpdateTime']}.{data['UpdateMillisec']}"
        dt: datetime = datetime.strptime(timestamp, "%Y%m%d %H:%M:%S.%f")
        dt: datetime = dt.replace(tzinfo=CHINA_TZ)
        
        tick: TickData = TickData(
            symbol=symbol,
            exchange=self.symbol_exchange_map[symbol],
            datetime=dt,
            volume=data["Volume"],
            turnover=data["Turnover"],
            open_interest=data["OpenInterest"],
            last_price=self._adjust_price(data["LastPrice"]),
            limit_up=data["UpperLimitPrice"],
            limit_down=data["LowerLimitPrice"],
            open_price=self._adjust_price(data["OpenPrice"]),
            high_price=self._adjust_price(data["HighestPrice"]),
            low_price=self._adjust_price(data["LowestPrice"]),
            pre_close=self._adjust_price(data["PreClosePrice"]),
            bid_price_1=self._adjust_price(data["BidPrice1"]),
            ask_price_1=self._adjust_price(data["AskPrice1"]),
            bid_volume_1=data["BidVolume1"],
            ask_volume_1=data["AskVolume1"],
            gateway_name="CTP"
        )
        
        if data.get("BidVolume2", 0) or data.get("AskVolume2", 0):
            tick.bid_price_2 = self._adjust_price(data["BidPrice2"])
            tick.bid_price_3 = self._adjust_price(data["BidPrice3"])
            tick.bid_price_4 = self._adjust_price(data["BidPrice4"])
            tick.bid_price_5 = self._adjust_price(data["BidPrice5"])
            
            tick.ask_price_2 = self._adjust_price(data["AskPrice2"])
            tick.ask_price_3 = self._adjust_price(data["AskPrice3"])
            tick.ask_price_4 = self._adjust_price(data["AskPrice4"])
            tick.ask_price_5 = self._adjust_price(data["AskPrice5"])
            
            tick.bid_volume_2 = data["BidVolume2"]
            tick.bid_volume_3 = data["BidVolume3"]
            tick.bid_volume_4 = data["BidVolume4"]
            tick.bid_volume_5 = data["BidVolume5"]
            
            tick.ask_volume_2 = data["AskVolume2"]
            tick.ask_volume_3 = data["AskVolume3"]
            tick.ask_volume_4 = data["AskVolume4"]
            tick.ask_volume_5 = data["AskVolume5"]
        
        self.engine.put(Event(EVENT_TICK, tick))
    
    def _adjust_price(self, price: float) -> float:
        """将异常的浮点数最大值（MAX_FLOAT）数据调整为0"""
        if price == MAX_FLOAT:
            price = 0
        return price
    
    def __del__(self):
        """析构函数"""
        try:
            self.close()
        except:
            pass


if __name__ == "__main__":
    """测试代码"""
    import time
    
    def on_tick(tick: TickData):
        print(f"收到行情: {tick.symbol} {tick.last_price} {tick.datetime}")
    
    def on_log(log: LogData):
        print(f"日志: {log.msg}")
    
    def on_connection(status: bool):
        print(f"连接状态变更: {'已连接' if status else '已断开'}")
    
    # 创建适配器
    adapter = VnpyAdapter()
    adapter.set_tick_callback(on_tick)
    adapter.set_log_callback(on_log)
    adapter.set_connection_callback(on_connection)
    
    # 连接测试
    test_addresses = ["**************:43213"]
    success = adapter.connect(test_addresses, "000000", "000000", "4080")
    if not success:
        print("连接失败, ", success)
    
    if success:
        time.sleep(2)  # 等待连接
        
        # 订阅测试
        adapter.subscribe("IF2508", Exchange.CFFEX)
        
        # 保持运行
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("停止测试")
            adapter.close()
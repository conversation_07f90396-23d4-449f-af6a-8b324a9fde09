#!/usr/bin/env python3
import sys
import os
from utils.broker_parser import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def test_broker_parser():
    """测试BrokerParser的功能"""
    print("开始测试BrokerParser...")
    
    # 获取broker.xml的路径
    script_dir = os.path.dirname(os.path.abspath(__file__))
    xml_path = os.path.join(script_dir, "broker.xml")
    
    try:
        # 初始化解析器
        print(f"正在解析 {xml_path}...")
        parser = BrokerParser(xml_path)
        print("解析成功!\n")
        
        # 测试获取所有券商
        brokers = parser.get_all_brokers()
        broker_count = len(brokers)
        print(f"共解析到 {broker_count} 个券商信息")
        
        if broker_count > 0:
            print("\n===== 测试根据ID获取券商 =====")
            # 选择第一个券商进行展示
            first_broker = brokers[0]
            broker_id = first_broker.broker_id
            print(f"获取券商ID: {broker_id}")
            
            broker = parser.get_broker_by_id(broker_id)
            if broker:
                print(f"  名称: {broker.broker_name}")
                print(f"  英文名称: {broker.broker_ename}")
                print(f"  服务器数量: {len(broker.servers)}")
                
                if broker.servers:
                    print("\n===== 测试获取服务器信息 =====")
                    server_name = next(iter(broker.servers.keys()))
                    print(f"获取券商 {broker_id} 的服务器 '{server_name}' 信息:")
                    
                    server = broker.servers[server_name]
                    print(f"  交易地址数量: {len(server.trading_items)}")
                    if server.trading_items:
                        print(f"  交易地址示例: {server.trading_items[0]}")
                        
                    print(f"  行情数据地址数量: {len(server.market_data_items)}")
                    if server.market_data_items:
                        print(f"  行情数据地址示例: {server.market_data_items[0]}")
                    
                    print("\n===== 测试筛选功能 =====")
                    # 获取该券商的所有交易地址
                    trading_addresses = parser.get_trading_addresses(broker_id)
                    print(f"券商 {broker_id} 共有 {len(trading_addresses)} 个服务器的交易地址")
                    
                    # 获取该券商的所有行情数据地址
                    market_data_addresses = parser.get_market_data_addresses(broker_id)
                    print(f"券商 {broker_id} 共有 {len(market_data_addresses)} 个服务器的行情数据地址")
                    
                    # 测试筛选服务器
                    print("\n===== 测试筛选服务器 =====")
                    # 确保服务器名称不为None
                    test_name = ""
                    if server_name is not None:
                        test_name = "电" if "电" in server_name else server_name[:1]
                    else:
                        # 如果server_name是None，则使用默认的"电"作为测试名称
                        test_name = "电"
                        
                    print(f"筛选名称包含 '{test_name}' 的服务器:")
                    
                    filtered_servers = parser.filter_servers_by_name(broker_id, test_name)
                    for name, server in filtered_servers.items():
                        print(f"  {name}: {len(server.trading_items)} 个交易地址, {len(server.market_data_items)} 个行情数据地址")

            print("\n===== 测试根据名称搜索券商 =====")
            # 选择一个常见的名称进行搜索
            search_term = "期货"
            print(f"搜索名称包含 '{search_term}' 的券商:")
            
            found_brokers = parser.get_brokers_by_name(search_term)
            print(f"找到 {len(found_brokers)} 个匹配的券商")
            
            # 显示前5个匹配结果
            for i, broker in enumerate(found_brokers[:5]):
                print(f"  {i+1}. {broker.broker_id}: {broker.broker_name} ({broker.broker_ename})")
            
            if len(found_brokers) > 5:
                print(f"  ... 还有 {len(found_brokers) - 5} 个匹配结果")
        
        print("\n所有测试完成!")
        return True
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_broker_parser()
    sys.exit(0 if success else 1) 
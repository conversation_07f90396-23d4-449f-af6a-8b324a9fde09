package dianjia

import (
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/gin-gonic/gin"
)

type InstrumentRouter struct{}

// InitInstrumentRouter 初始化 期货合约 路由信息
func (s *InstrumentRouter) InitInstrumentRouter(Router *gin.RouterGroup) {
	instrumentRouter := Router.Group("instrument")
	instrumentApi := v1.ApiGroupApp.DianjiaApiGroup.InstrumentApi
	{
		instrumentRouter.POST("createInstrument", instrumentApi.CreateInstrument)         // 新建期货合约
		instrumentRouter.DELETE("deleteInstrument", instrumentApi.DeleteInstrument)       // 删除期货合约
		instrumentRouter.DELETE("deleteInstrumentByIds", instrumentApi.DeleteInstrumentByIds) // 批量删除期货合约
		instrumentRouter.PUT("updateInstrument", instrumentApi.UpdateInstrument)          // 更新期货合约
		instrumentRouter.GET("findInstrument", instrumentApi.FindInstrument)              // 根据ID获取期货合约
		instrumentRouter.GET("getInstrumentList", instrumentApi.GetInstrumentList)        // 获取期货合约列表
		instrumentRouter.GET("getInstrumentSelectList", instrumentApi.GetInstrumentSelectList) // 获取期货合约选择器列表
		instrumentRouter.GET("getInstrumentsByExchange", instrumentApi.GetInstrumentsByExchange) // 获取按交易所分组的期货合约列表
	}
}
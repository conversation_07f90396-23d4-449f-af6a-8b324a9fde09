<template>
  <view class="cancel-records-page">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">取消记录</text>
    </view>

    <!-- 合同信息 -->
    <view v-if="contractInfo" class="contract-info-card">
      <view class="info-row">
        <text class="label">合同编码:</text>
        <text class="value">{{ contractInfo.contractCode }}</text>
      </view>
      <view class="info-row">
        <text class="label">总数量:</text>
        <text class="value">{{ contractInfo.totalQuantity }} 手</text>
      </view>
      <view class="info-row">
        <text class="label">剩余数量:</text>
        <text class="value">{{ contractInfo.remainingQuantity }} 手</text>
      </view>
      <view class="info-row">
        <text class="label">已取消数量:</text>
        <text class="value">{{ contractInfo.totalQuantity - contractInfo.remainingQuantity }} 手</text>
      </view>
    </view>

    <!-- 取消记录列表 -->
    <view class="records-list">
      <view 
        v-for="record in cancelRecords" 
        :key="record.ID" 
        class="record-card"
      >
        <!-- 记录头部 -->
        <view class="record-header">
          <text class="record-time">{{ formatDateTime(record.CreatedAt) }}</text>
          <wd-tag :type="getStatusType(record.contractStatusAfterCancel)">
            {{ getStatusText(record.contractStatusAfterCancel) }}
          </wd-tag>
        </view>

        <!-- 记录详情 -->
        <view class="record-details">
          <view class="detail-row">
            <text class="label">取消数量:</text>
            <text class="value highlight">{{ record.cancelQuantity }} 手</text>
          </view>
          <view class="detail-row">
            <text class="label">取消前剩余:</text>
            <text class="value">{{ record.beforeCancelRemainingQuantity }} 手</text>
          </view>
          <view class="detail-row">
            <text class="label">取消后剩余:</text>
            <text class="value">{{ record.afterCancelRemainingQuantity }} 手</text>
          </view>
          <view v-if="record.reason" class="detail-row">
            <text class="label">取消原因:</text>
            <text class="value">{{ record.reason }}</text>
          </view>
          <view class="detail-row">
            <text class="label">操作人:</text>
            <text class="value">{{ record.user?.nickName || record.user?.userName || `用户${record.userID}` }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-if="!cancelRecords.length && !loading" class="empty-state">
      <text>暂无取消记录</text>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-state">
      <wd-loading />
      <text>加载中...</text>
    </view>
  </view>
</template>

<route lang="jsonc" type="page">
{
  "style": {
    "navigationBarTitleText": "取消记录"
  }
}
</route>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getContractCancelRecords, getContractDetail } from '@/api/contract'
import type { IContractCancelRecord, IContract, ContractStatus } from '@/types/contract'
import type { TagType } from 'wot-design-uni/components/wd-tag/types'

const router = useRouter()

// 响应式数据
const cancelRecords = ref<IContractCancelRecord[]>([])
const contractInfo = ref<IContract | null>(null)
const loading = ref(false)

// 获取路由参数
const contractId = ref<number>(0)

// 加载取消记录
async function loadCancelRecords() {
  if (loading.value || !contractId.value) return

  loading.value = true

  try {
    // 并行加载合同信息和取消记录
    const [contractResponse, recordsResponse] = await Promise.all([
      getContractDetail(contractId.value),
      getContractCancelRecords(contractId.value)
    ])
    
    if (contractResponse.code === 0) {
      contractInfo.value = contractResponse.data
    }
    
    if (recordsResponse.code === 0) {
      cancelRecords.value = recordsResponse.data || []
    } else {
      uni.showToast({
        title: recordsResponse.msg || '获取取消记录失败',
        icon: 'error',
      })
    }
  } catch (error) {
    console.error('获取取消记录失败:', error)
    uni.showToast({
      title: '网络错误',
      icon: 'error',
    })
  } finally {
    loading.value = false
  }
}

// 获取状态类型 - 更新为 V3 状态
function getStatusType(status: ContractStatus): TagType {
  const statusMap: Record<ContractStatus, TagType> = {
    Unexecuted: 'warning',
    Executing: 'success',
    Pending: 'warning',
    Completed: 'primary',
    Cancelled: 'danger',
  }
  return statusMap[status] || ('warning' as TagType)
}

// 获取状态文本 - 更新为 V3 状态
function getStatusText(status: ContractStatus): string {
  const statusMap: Record<ContractStatus, string> = {
    Unexecuted: '未执行',
    Executing: '执行中',
    Pending: '待处理',
    Completed: '已完成',
    Cancelled: '已取消',
  }
  return statusMap[status] || status
}

// 格式化日期时间
function formatDateTime(dateStr: string) {
  const date = new Date(dateStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 生命周期
onMounted(() => {
  // 获取路由参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options as any
  
  if (options.contractId) {
    contractId.value = parseInt(options.contractId)
    loadCancelRecords()
  } else {
    uni.showToast({
      title: '参数错误',
      icon: 'error',
    })
    setTimeout(() => {
      router.back()
    }, 1500)
  }
})
</script>

<style lang="scss" scoped>
.cancel-records-page {
  padding: 20rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.page-header {
  margin-bottom: 30rpx;

  .page-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.contract-info-card {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

  .info-row {
    display: flex;
    margin-bottom: 12rpx;

    .label {
      font-size: 26rpx;
      color: #666;
      width: 140rpx;
      flex-shrink: 0;
    }

    .value {
      font-size: 26rpx;
      color: #333;
      flex: 1;
    }
  }
}

.records-list {
  .record-card {
    background: white;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

    .record-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;

      .record-time {
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
      }
    }

    .record-details {
      .detail-row {
        display: flex;
        margin-bottom: 12rpx;

        .label {
          font-size: 26rpx;
          color: #666;
          width: 140rpx;
          flex-shrink: 0;
        }

        .value {
          font-size: 26rpx;
          color: #333;
          flex: 1;

          &.highlight {
            color: #f56c6c;
            font-weight: bold;
          }
        }
      }
    }
  }
}

.empty-state, .loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx;
  color: #999;
  font-size: 28rpx;
  gap: 30rpx;
}
</style>

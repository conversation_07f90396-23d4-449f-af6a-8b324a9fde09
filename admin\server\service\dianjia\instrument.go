package dianjia

import (
	"errors"
	"fmt"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/dianjia"
	"gorm.io/gorm"
)

type InstrumentService struct{}

// CreateInstrument 创建期货合约
func (instrumentService *InstrumentService) CreateInstrument(req dianjia.InstrumentRequest) (instrument dianjia.Instrument, err error) {
	// 检查InstrumentID是否已存在
	var existInstrument dianjia.Instrument
	if err = global.GVA_DB.Where("instrument_id = ?", req.InstrumentID).First(&existInstrument).Error; err == nil {
		return instrument, errors.New("合约ID已存在")
	}
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return instrument, err
	}

	instrument = dianjia.Instrument{
		ExchangeID:        req.ExchangeID,
		ProductID:         req.ProductID,
		ProductName:       req.ProductName,
		InstrumentID:      req.InstrumentID,
		InstrumentName:    req.InstrumentName,
		ProductClass:      req.ProductClass,
		VolumeMultiple:    req.VolumeMultiple,
		PriceTick:         req.PriceTick,
		LongMarginRatio:   req.LongMarginRatio,
		ShortMarginRatio:  req.ShortMarginRatio,
		OpenRatioByMoney:  req.OpenRatioByMoney,
		CloseRatioByMoney: req.CloseRatioByMoney,
		CloseTodayRatio:   req.CloseTodayRatio,
		DeliveryYear:      req.DeliveryYear,
		DeliveryMonth:     req.DeliveryMonth,
		OpenDate:          req.OpenDate,
		ExpireDate:        req.ExpireDate,
		InstLifePhase:     req.InstLifePhase,
	}

	err = global.GVA_DB.Create(&instrument).Error
	return instrument, err
}

// DeleteInstrument 删除期货合约
func (instrumentService *InstrumentService) DeleteInstrument(id uint) (err error) {
	err = global.GVA_DB.Delete(&dianjia.Instrument{}, id).Error
	return err
}

// DeleteInstrumentByIds 批量删除期货合约
func (instrumentService *InstrumentService) DeleteInstrumentByIds(ids []uint) (err error) {
	err = global.GVA_DB.Delete(&[]dianjia.Instrument{}, "id IN ?", ids).Error
	return err
}

// UpdateInstrument 更新期货合约
func (instrumentService *InstrumentService) UpdateInstrument(req dianjia.InstrumentRequest) (err error) {
	// 检查InstrumentID是否已被其他记录使用
	var existInstrument dianjia.Instrument
	if err = global.GVA_DB.Where("instrument_id = ? AND id != ?", req.InstrumentID, req.ID).First(&existInstrument).Error; err == nil {
		return errors.New("合约ID已被其他记录使用")
	}
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	instrument := dianjia.Instrument{
		ExchangeID:        req.ExchangeID,
		ProductID:         req.ProductID,
		ProductName:       req.ProductName,
		InstrumentID:      req.InstrumentID,
		InstrumentName:    req.InstrumentName,
		ProductClass:      req.ProductClass,
		VolumeMultiple:    req.VolumeMultiple,
		PriceTick:         req.PriceTick,
		LongMarginRatio:   req.LongMarginRatio,
		ShortMarginRatio:  req.ShortMarginRatio,
		OpenRatioByMoney:  req.OpenRatioByMoney,
		CloseRatioByMoney: req.CloseRatioByMoney,
		CloseTodayRatio:   req.CloseTodayRatio,
		DeliveryYear:      req.DeliveryYear,
		DeliveryMonth:     req.DeliveryMonth,
		OpenDate:          req.OpenDate,
		ExpireDate:        req.ExpireDate,
		InstLifePhase:     req.InstLifePhase,
	}
	instrument.ID = req.ID

	err = global.GVA_DB.Model(&instrument).Updates(instrument).Error
	return err
}

// GetInstrument 根据id获取期货合约记录
func (instrumentService *InstrumentService) GetInstrument(id uint) (instrument dianjia.Instrument, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&instrument).Error
	return
}

// GetInstrumentInfoList 分页获取期货合约记录
func (instrumentService *InstrumentService) GetInstrumentInfoList(req dianjia.InstrumentListRequest) (list []dianjia.InstrumentResponse, total int64, err error) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)

	// 构建查询条件
	db := global.GVA_DB.Model(&dianjia.Instrument{})

	if req.InstrumentID != "" {
		db = db.Where("instrument_id LIKE ?", "%"+req.InstrumentID+"%")
	}
	if req.InstrumentName != "" {
		db = db.Where("instrument_name LIKE ?", "%"+req.InstrumentName+"%")
	}
	if req.ExchangeID != "" {
		db = db.Where("exchange_id = ?", req.ExchangeID)
	}
	if req.ProductID != "" {
		db = db.Where("product_id = ?", req.ProductID)
	}
	if req.ProductClass != "" {
		db = db.Where("product_class = ?", req.ProductClass)
	}
	if req.InstLifePhase != "" {
		db = db.Where("inst_life_phase = ?", req.InstLifePhase)
	}

	// 获取总数
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	// 获取分页数据
	var instruments []dianjia.Instrument
	err = db.Limit(limit).Offset(offset).Order("created_at desc").Find(&instruments).Error
	if err != nil {
		return
	}

	// 转换为响应格式
	list = make([]dianjia.InstrumentResponse, len(instruments))
	for i, instrument := range instruments {
		list[i] = dianjia.InstrumentResponse{
			ID:                instrument.ID,
			ExchangeID:        instrument.ExchangeID,
			ProductID:         instrument.ProductID,
			ProductName:       instrument.ProductName,
			InstrumentID:      instrument.InstrumentID,
			InstrumentName:    instrument.InstrumentName,
			ProductClass:      instrument.ProductClass,
			ProductClassName:  dianjia.ProductClassMap[instrument.ProductClass],
			VolumeMultiple:    instrument.VolumeMultiple,
			PriceTick:         instrument.PriceTick,
			LongMarginRatio:   instrument.LongMarginRatio,
			ShortMarginRatio:  instrument.ShortMarginRatio,
			OpenRatioByMoney:  instrument.OpenRatioByMoney,
			CloseRatioByMoney: instrument.CloseRatioByMoney,
			CloseTodayRatio:   instrument.CloseTodayRatio,
			DeliveryYear:      instrument.DeliveryYear,
			DeliveryMonth:     instrument.DeliveryMonth,
			OpenDate:          instrument.OpenDate,
			ExpireDate:        instrument.ExpireDate,
			InstLifePhase:     instrument.InstLifePhase,
			InstLifePhaseName: dianjia.InstLifePhaseMap[instrument.InstLifePhase],
			CreatedAt:         instrument.CreatedAt,
			UpdatedAt:         instrument.UpdatedAt,
		}
	}

	return list, total, err
}

// GetInstrumentSelectList 获取期货合约选择器列表
func (instrumentService *InstrumentService) GetInstrumentSelectList(req dianjia.InstrumentSelectRequest) (list []dianjia.InstrumentSelectResponse, err error) {
	db := global.GVA_DB.Model(&dianjia.Instrument{}).
		Select("id, instrument_id, instrument_name, product_name, exchange_id")

	// 构建查询条件
	if req.ExchangeID != "" {
		db = db.Where("exchange_id = ?", req.ExchangeID)
	}
	if req.ProductID != "" {
		db = db.Where("product_id = ?", req.ProductID)
	}
	if req.LifePhase != "" {
		db = db.Where("inst_life_phase = ?", req.LifePhase)
	} else {
		// 默认只查询上市状态的合约
		db = db.Where("inst_life_phase = ?", "1")
	}
	if req.Keyword != "" {
		db = db.Where("(instrument_id LIKE ? OR instrument_name LIKE ?)",
			"%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}

	var results []struct {
		ID             uint   `json:"id"`
		InstrumentID   string `json:"instrument_id"`
		InstrumentName string `json:"instrument_name"`
		ProductName    string `json:"product_name"`
		ExchangeID     string `json:"exchange_id"`
	}

	err = db.Order("instrument_id").Find(&results).Error
	if err != nil {
		return
	}

	list = make([]dianjia.InstrumentSelectResponse, len(results))
	for i, result := range results {
		list[i] = dianjia.InstrumentSelectResponse{
			ID:             result.ID,
			InstrumentID:   result.InstrumentID,
			InstrumentName: result.InstrumentName,
			ProductName:    result.ProductName,
			ExchangeID:     result.ExchangeID,
		}
	}

	return list, err
}

// GetInstrumentByInstrumentID 根据合约ID获取期货合约
func (instrumentService *InstrumentService) GetInstrumentByInstrumentID(instrumentID string) (instrument dianjia.Instrument, err error) {
	err = global.GVA_DB.Where("instrument_id = ?", instrumentID).First(&instrument).Error
	return
}

// GetInstrumentsByExchange 根据交易所ID获取期货合约列表（分组）
func (instrumentService *InstrumentService) GetInstrumentsByExchange() (result map[string]map[string][]dianjia.InstrumentSelectResponse, err error) {
	var instruments []struct {
		ID             uint   `json:"id"`
		InstrumentID   string `json:"instrument_id"`
		InstrumentName string `json:"instrument_name"`
		ProductName    string `json:"product_name"`
		ExchangeID     string `json:"exchange_id"`
		ProductID      string `json:"product_id"`
	}

	err = global.GVA_DB.Model(&dianjia.Instrument{}).
		Select("id, instrument_id, instrument_name, product_name, exchange_id, product_id").
		Where("inst_life_phase = ?", "1"). // 只查询上市状态的合约
		Order("exchange_id, product_id, instrument_id").
		Find(&instruments).Error

	if err != nil {
		return nil, err
	}

	// 按交易所 -> 产品 -> 合约的层级组织数据
	result = make(map[string]map[string][]dianjia.InstrumentSelectResponse)

	for _, inst := range instruments {
		exchangeID := inst.ExchangeID
		productKey := fmt.Sprintf("%s(%s)", inst.ProductName, inst.ProductID)

		if _, exists := result[exchangeID]; !exists {
			result[exchangeID] = make(map[string][]dianjia.InstrumentSelectResponse)
		}

		if _, exists := result[exchangeID][productKey]; !exists {
			result[exchangeID][productKey] = []dianjia.InstrumentSelectResponse{}
		}

		result[exchangeID][productKey] = append(result[exchangeID][productKey], dianjia.InstrumentSelectResponse{
			ID:             inst.ID,
			InstrumentID:   inst.InstrumentID,
			InstrumentName: inst.InstrumentName,
			ProductName:    inst.ProductName,
			ExchangeID:     inst.ExchangeID,
		})
	}

	return result, nil
}

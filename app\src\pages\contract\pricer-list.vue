<template>
  <view class="pricer-contract-page">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">合同中心</text>
      <view class="view-switch">
        <wd-button
          :type="currentView === 'summary' ? 'primary' : 'info'"
          size="small"
          @click="switchView('summary')"
        >
          汇总视图
        </wd-button>
        <wd-button
          :type="currentView === 'detail' ? 'primary' : 'info'"
          size="small"
          @click="switchView('detail')"
        >
          明细视图
        </wd-button>
      </view>
    </view>

    <!-- 筛选条件 - Tabs格式 -->
    <view v-if="currentView === 'detail'" class="filter-tabs">
      <wd-tabs v-model="activeTab" @change="handleTabChange">
        <wd-tab
          v-for="tab in statusTabs"
          :key="tab.value"
          :title="tab.label"
          :name="tab.value"
        >
        </wd-tab>
      </wd-tabs>
    </view>

    <!-- 汇总视图 -->
    <ContractSummary
      v-if="currentView === 'summary'"
      :contracts="summaryContractList"
      user-role="pricer"
      :enable-click="true"
      @instrument-click="handleInstrumentClick"
    />

    <!-- 明细视图 -->
    <view v-if="currentView === 'detail'" class="detail-view">
      <scroll-view
        class="scroll-container"
        scroll-y
        refresher-enabled
        :refresher-triggered="isRefreshing"
        @refresherrefresh="onRefresh"
        @scrolltolower="loadMore"
      >
        <view class="contract-list">
          <ContractCard
            v-for="contract in contractList"
            :key="contract.ID"
            :contract="contract"
            user-role="pricer"
            @click="goToDetail(contract.ID)"
          />
        </view>
      </scroll-view>
    </view>

    <!-- 空状态 -->
    <view v-if="summaryContractList.length === 0 && !loading" class="empty-state">
      <text>暂无可用合同</text>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-state">
      <wd-loading />
      <text>加载中...</text>
    </view>
  </view>
</template>

<route lang="jsonc" type="page">
{
  "style": {
    "navigationBarTitleText": "合同中心"
  }
}
</route>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getContractsAsPricer } from '@/api/contract'
import type { IContract, IContractAsPricerRequest } from '@/types/contract'
import ContractCard from '@/components/ContractCard.vue'
import ContractSummary from '@/components/ContractSummary.vue'

const router = useRouter()

// 视图类型
type ViewType = 'summary' | 'detail'

// 响应式数据
const contractList = ref<IContract[]>([])
const currentView = ref<ViewType>('summary')
const loading = ref(false)
const isLoading = ref(false)
const isRefreshing = ref(false)

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const hasMore = ref(true)

// 请求控制
let currentRequest: Promise<any> | null = null

// Tabs筛选相关
const activeTab = ref('all')
const statusTabs = ref([
  { label: '全部', value: 'all' },
  { label: '未执行', value: 'Unexecuted' },
  { label: '执行中', value: 'Executing' },
  { label: '已完成', value: 'Completed' },
  { label: '已取消', value: 'Cancelled' },
])

// 数据结构已移至ContractSummary组件中

// 汇总视图的合同数据
const summaryContractList = ref<IContract[]>([])

// 加载汇总视图数据
async function loadSummaryData() {
  try {
    // 获取所有执行中的合同进行汇总
    const response = await getContractsAsPricer({
      status: 'Executing',
      pageSize: 1000 // 获取足够多的数据进行汇总
    })

    if (response.code === 0) {
      summaryContractList.value = response.data.list || []
    }
  } catch (error) {
    console.error('获取汇总数据失败:', error)
  }
}

// 计算逻辑已移至ContractSummary组件中

// 加载合同列表
async function loadContractList(refresh = false) {
  // 如果是刷新操作，立即清理数据和状态
  if (refresh) {
    currentPage.value = 1
    contractList.value = []
    hasMore.value = true
    isRefreshing.value = true

    // 取消之前的请求
    if (currentRequest) {
      currentRequest = null
    }
  } else {
    // 如果正在加载或没有更多数据，直接返回
    if (isLoading.value || !hasMore.value) return
    isLoading.value = true
  }

  try {
    const params: IContractAsPricerRequest = {
      page: currentPage.value,
      pageSize: pageSize.value,
      status: activeTab.value === 'all' ? undefined : activeTab.value,
    }

    // 创建新的请求
    const requestPromise = getContractsAsPricer(params)
    currentRequest = requestPromise

    const response = await requestPromise

    // 检查这个请求是否还是当前请求（防止过期请求覆盖新数据）
    if (currentRequest !== requestPromise) {
      return // 请求已过期，忽略结果
    }

    if (response.code === 0) {
      // 处理分页格式的响应
      const list = response.data.list || []
      const totalCount = response.data.total || 0

      if (refresh) {
        contractList.value = list
      } else {
        contractList.value.push(...list)
      }

      total.value = totalCount
      hasMore.value = contractList.value.length < totalCount
    } else {
      uni.showToast({
        title: response.msg || '获取合同列表失败',
        icon: 'error',
      })
    }
  } catch (error) {
    console.error('获取合同列表失败:', error)
    uni.showToast({
      title: '网络错误',
      icon: 'error',
    })
  } finally {
    loading.value = false
    isLoading.value = false
    isRefreshing.value = false
  }
}

// 加载更多
async function loadMore() {
  if (!hasMore.value || isLoading.value) return

  currentPage.value++
  await loadContractList()
}

// 下拉刷新
async function onRefresh() {
  await loadContractList(true)
}

// Tab切换
async function handleTabChange() {
  // 立即清理数据，避免显示旧数据
  contractList.value = []
  hasMore.value = true
  currentPage.value = 1

  // 取消之前的请求
  if (currentRequest) {
    currentRequest = null
  }

  // 加载新数据
  await loadContractList(true)
}

// 切换视图
function switchView(view: ViewType) {
  currentView.value = view

  // 根据视图类型加载对应数据
  if (view === 'detail') {
    loadContractList(true)
  } else if (view === 'summary') {
    loadSummaryData()
  }
}

// 这些函数已移至ContractSummary组件中

// 查看合同详情
function goToDetail(contractId: number) {
  router.push(`/pages/contract/detail?id=${contractId}&role=pricer`)
}

// 处理期货合约点击事件
function handleInstrumentClick(data: { instrumentInfo: any, userInfo: any }) {
  const { instrumentInfo, userInfo } = data
  uni.navigateTo({
    url: `/pages/trade/execute?instrumentId=${instrumentInfo.instrumentId}&setterID=${userInfo.userID}`
  })
}

// 这些函数已移至ContractCard组件中

// 生命周期
onMounted(() => {
  // 默认显示汇总视图，加载汇总数据
  loadSummaryData()
})
</script>

<style lang="scss" scoped>
.pricer-contract-page {
  padding: 20rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;

  .page-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }

  .view-switch {
    display: flex;
    gap: 10rpx;
  }
}

.filter-bar {
  margin-bottom: 30rpx;
}

.filter-tabs {
  margin-bottom: 20rpx;
}

.scroll-container {
  height: calc(100vh - 200rpx);
}

.contract-list {
  padding: 0 20rpx;
}

// 汇总视图样式已移至ContractSummary组件中

// 明细视图样式
.detail-view {
  .setter-group {
    margin-bottom: 40rpx;

    .setter-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 12rpx 12rpx 0 0;
      padding: 24rpx 30rpx;

      .setter-name {
        font-size: 32rpx;
        font-weight: bold;
        color: white;
      }
    }

    .instrument-group {
      background: white;
      border-radius: 0 0 12rpx 12rpx;
      margin-bottom: 20rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

      &:not(:last-child) {
        border-radius: 0;
      }

      &:last-child {
        border-radius: 0 0 12rpx 12rpx;
      }

      .instrument-header {
        background: #f8f9fa;
        padding: 20rpx 30rpx;
        border-bottom: 1rpx solid #e4e7ed;

        .instrument-name {
          font-size: 28rpx;
          font-weight: 500;
          color: #333;
        }
      }

      .contract-details {
        padding: 20rpx 30rpx;

        .contract-detail-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 20rpx;
          margin-bottom: 16rpx;
          background: #f8f9fa;
          border-radius: 8rpx;
          transition: all 0.3s ease;

          &:last-child {
            margin-bottom: 0;
          }

          &:active {
            background: #e9ecef;
            transform: scale(0.98);
          }

          .contract-basic {
            display: flex;
            align-items: center;
            gap: 16rpx;

            .contract-code {
              font-size: 26rpx;
              font-weight: 500;
              color: #333;
            }
          }

          .contract-meta {
            display: flex;
            align-items: center;
            gap: 16rpx;
            font-size: 24rpx;

            .quantity {
              color: #333;
              font-weight: 500;
            }

            .price {
              color: #666;
            }

            .date {
              color: #999;
            }
          }
        }
      }
    }
  }
}

.empty-state, .loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx;
  color: #999;
  font-size: 28rpx;
  gap: 30rpx;
}
</style>
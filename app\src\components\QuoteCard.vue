<template>
  <view class="quote-card bg-white rounded-lg shadow-sm p-3">
    <view v-if="marketData" class="space-y-2">
      <!-- 标题行：合约名称、交易所、时间 -->
      <view class="flex justify-between items-center">
        <view class="flex items-center space-x-2">
          <text class="text-sm font-medium text-gray-800">{{ marketData.symbol }}</text>
          <text class="text-xs text-gray-500">{{ marketData.exchange }}</text>
        </view>
        <text class="text-xs text-gray-400">{{ formatTime(marketData.timestamp) }}</text>
      </view>

      <!-- 主价格行：最新价、涨跌比率/数量、卖一买一 -->
      <view class="flex items-stretch justify-between">
        <view class="flex items-center space-x-3">
          <text
            class="text-4xl font-bold cursor-pointer leading-tight"
            :class="priceChangeClass"
            @click="onPriceClick('last', marketData.last_price)"
          >
            {{ formatPrice(marketData.last_price) }}
          </text>
          <view class="flex flex-col justify-center">
            <text class="text-xs" :class="priceChangeClass">
              {{ priceChangePercent >= 0 ? '+' : '' }}{{ priceChangePercent.toFixed(2) }}%
            </text>
            <text class="text-xs text-gray-500">
              {{ marketData.volume }}
            </text>
          </view>
        </view>
        <view class="text-right flex flex-col justify-center space-y-1">
          <view class="flex items-center space-x-1">
            <text class="text-xs text-gray-500">卖一</text>
            <text
              class="text-sm font-medium text-red-600 cursor-pointer"
              @click="onPriceClick('ask', marketData.ask_price_1)"
            >
              {{ formatPrice(marketData.ask_price_1) }}
            </text>
            <text class="text-xs text-gray-500">{{ marketData.ask_volume_1 }}</text>
          </view>
          <view class="flex items-center space-x-1">
            <text class="text-xs text-gray-500">买一</text>
            <text
              class="text-sm font-medium text-green-600 cursor-pointer"
              @click="onPriceClick('bid', marketData.bid_price_1)"
            >
              {{ formatPrice(marketData.bid_price_1) }}
            </text>
            <text class="text-xs text-gray-500">{{ marketData.bid_volume_1 }}</text>
          </view>
        </view>
      </view>



      <!-- 涨跌停行 -->
      <view class="flex justify-between items-center">
        <view class="flex items-center space-x-1">
          <text class="text-xs text-gray-500">涨停</text>
          <text
            class="text-sm font-medium text-red-500 cursor-pointer"
            @click="onPriceClick('limit_up', limitUpPrice)"
          >
            {{ formatPrice(limitUpPrice) }}
          </text>
        </view>
        <view class="flex items-center space-x-1">
          <text class="text-xs text-gray-500">跌停</text>
          <text
            class="text-sm font-medium text-green-500 cursor-pointer"
            @click="onPriceClick('limit_down', limitDownPrice)"
          >
            {{ formatPrice(limitDownPrice) }}
          </text>
        </view>
      </view>
    </view>

    <!-- 无数据状态 -->
    <view v-else class="flex items-center justify-center py-6">
      <view class="text-center">
        <wd-icon name="chart-line" size="24px" custom-class="text-gray-300 mb-1"></wd-icon>
        <text class="text-xs text-gray-400">{{ isSubscribed ? '没有获取到行情数据' : '正在获取行情数据...' }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useMarketStore } from '@/store/market'

// Props 定义
interface Props {
  instrumentId?: string  // 合约ID，如 IF2508
  symbol?: string       // 合约代码，如 i2510
  exchange?: string     // 交易所，如 DCE
}

const props = withDefaults(defineProps<Props>(), {
  instrumentId: '',
  symbol: '',
  exchange: 'DCE'
})

// Emits 定义
interface PriceClickEvent {
  type: 'last' | 'bid' | 'ask' | 'limit_up' | 'limit_down'
  price: number
  numericPrice: number
}

const emit = defineEmits<{
  priceClick: [event: PriceClickEvent]
}>()

// 引入 market store
const marketStore = useMarketStore()

// 响应式数据
const previousPrice = ref<number>(0)

// 获取当前合约的行情数据
const marketData = computed(() => {
  const symbol = props.instrumentId || props.symbol
  if (!symbol) return null
  return marketStore.getMarketData(symbol)
})

// 检查是否已订阅
const isSubscribed = computed(() => {
  const symbol = props.instrumentId || props.symbol
  if (!symbol) return false
  return marketStore.isSubscribed(symbol)
})

// 计算属性
const currentPrice = computed(() => {
  return marketData.value ? marketData.value.last_price : 0
})

const priceChange = computed(() => {
  if (!marketData.value || !previousPrice.value) return 0
  return currentPrice.value - previousPrice.value
})

const priceChangePercent = computed(() => {
  if (!marketData.value || !previousPrice.value || previousPrice.value === 0) return 0
  return ((currentPrice.value - previousPrice.value) / previousPrice.value * 100)
})

const priceChangeClass = computed(() => {
  if (priceChange.value > 0) return 'text-red-500'
  if (priceChange.value < 0) return 'text-green-500'
  return 'text-gray-700'
})

// 计算涨跌停价格
const limitUpPrice = computed(() => {
  if (!marketData.value) return 0
  return marketData.value.last_price * 1.1
})

const limitDownPrice = computed(() => {
  if (!marketData.value) return 0
  return marketData.value.last_price * 0.9
})

// 方法
const formatTime = (timestamp: number) => {
  if (!timestamp) return '--'
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const formatPrice = (price: number) => {
  return price ? price.toFixed(1) : '0.0'
}

const onPriceClick = (type: PriceClickEvent['type'], price: number) => {
  emit('priceClick', { type, price, numericPrice: price })
}

// 订阅行情数据
const subscribeToMarket = () => {
  const symbol = props.instrumentId || props.symbol
  if (symbol && !marketStore.isSubscribed(symbol)) {
    marketStore.subscribe(symbol, props.exchange)
  }
}

// 监听价格变化，更新 previousPrice
watch(() => currentPrice.value, (newPrice, oldPrice) => {
  if (oldPrice && oldPrice !== newPrice) {
    previousPrice.value = oldPrice
  }
}, { immediate: false })

// 监听 props 变化，重新订阅
watch(() => [props.instrumentId, props.symbol, props.exchange], () => {
  subscribeToMarket()
}, { immediate: false })

// 生命周期
onMounted(() => {
  subscribeToMarket()
})
</script>

<style lang="scss" scoped>
.quote-card {
  .cursor-pointer {
    cursor: pointer;
    transition: opacity 0.2s;
    
    &:hover {
      opacity: 0.8;
    }
    
    &:active {
      opacity: 0.6;
    }
  }
}

// 响应式调整
@media (max-width: 375px) {
  .quote-card {
    .text-2xl {
      font-size: 1.75rem;
    }
  }
}
</style>

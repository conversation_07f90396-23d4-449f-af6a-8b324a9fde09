# asyncio 异步重构方案 (修订版)

## 1. 概述

本文档旨在指导将整个 HQ 行情系统代码库从基于 `asyncio` 的异步模型重构为**事件驱动的同步模型**。主要目标是移除对 `asyncio` 库的依赖，并使用 `EventEngine` 结合 `threading` 和 `concurrent.futures` 来处理并发和后台任务，以实现与 vnpy 框架一致的架构风格。

**核心重构策略:**

- **异步到同步**: 所有 `async def` 函数将转换为同步 `def` 函数，并移除 `await` 关键字。
- **后台任务管理**: 放弃直接使用 `threading.Thread`，全面转向 `EventEngine`。后台任务的触发将通过向 `EventEngine` 推送事件来完成，由预先注册的处理器函数执行具体逻辑。
- **高并发网络I/O**: 对于需要大量并发执行的网络 `ping` 测试，我们将采用 `concurrent.futures.ThreadPoolExecutor`。这在同步代码库中是最高效且符合架构一致性的选择。

---

## 2. 需要修改的文件及具体方案

### 2.1. `adapters/vnpy_adapter.py`

**核心修改**: 将自动重连逻辑从 `asyncio` 迁移到 `EventEngine` 驱动。

- **具体修改点**:
    1.  移除 `import asyncio`，确保已导入 `from vnpy.event import Event`。
    2.  定义一个新的事件类型，例如 `EVENT_RECONNECT_CTP = "eReconnectCtp"`。
    3.  在 `__init__` 方法中，注册重连处理器：`self.engine.register(EVENT_RECONNECT_CTP, self._process_reconnect_event)`。
    4.  在 `onFrontDisconnected` 回调中，当需要重连时，不再直接调用重连逻辑，而是向事件引擎推送一个事件：`event = Event(EVENT_RECONNECT_CTP); self.engine.put(event)`。
    5.  创建一个新的处理器方法 `_process_reconnect_event(self, event: Event)`，将原 `_auto_reconnect` 的逻辑（不含 `sleep` 和循环）移入此方法。此方法将负责执行一次重连尝试。
    6.  如果重连失败，`onFrontDisconnected` 会再次被触发，从而形成一个自然的、事件驱动的重试循环，无需手动 `sleep` 和递归调用。

- **注意事项**:
    -   这种模式将重连的“决策”（在 `onFrontDisconnected` 中）与“执行”（在 `_process_reconnect_event` 中）分离开，更加清晰。

### 2.2. `core/instrument_manager.py`

**核心修改**: 移除异步方法，并将后台缓存更新的职责转移给调用者（`core.Engine`）。

- **具体修改点**:
    1.  移除 `import asyncio` 和 `import threading`。
    2.  删除 `load_instruments_async` 和 `_update_cache_async` 方法。
    3.  修改 `load_instruments_sync` 方法，让它在发现缓存陈旧时，可以返回一个标志，建议调用者执行后台更新。例如，返回 `(success: bool, needs_update: bool)`。
    4.  创建一个新的同步方法 `update_cache_sync(self)`，封装从 API 获取数据并保存到缓存的逻辑。

### 2.3. `core/engine.py`

**核心修改**: 作为业务流程的编排者，`Engine` 将负责处理由其他组件发起的事件，如缓存更新。

- **具体修改点**:
    1.  定义一个新的事件类型 `EVENT_UPDATE_CACHE = "eUpdateCache"`。
    2.  在 `__init__` 中注册缓存更新处理器：`self.vnpy_adapter.engine.register(EVENT_UPDATE_CACHE, self._process_cache_update_event)`。
    3.  在 `initialize` 方法中，当调用 `self.instrument_manager.load_instruments_sync()` 后，检查返回的 `needs_update` 标志。如果为 `True`，则推送缓存更新事件：`event = Event(EVENT_UPDATE_CACHE); self.vnpy_adapter.engine.put(event)`。
    4.  创建处理器方法 `_process_cache_update_event(self, event: Event)`，该方法在新线程中调用 `self.instrument_manager.update_cache_sync()`，以避免阻塞主引擎。示例：`thread = threading.Thread(target=self.instrument_manager.update_cache_sync, daemon=True); thread.start()`。

### 2.4. `utils/network_utils.py`

**核心修改**: 确认并统一使用基于 `concurrent.futures.ThreadPoolExecutor` 的同步并发方案。

- **方案**: 
    -   保留并使用 `batch_ping_addresses_sync` 函数作为唯一的批量测速实现。此函数是最高效且符合项目架构的选择。
    -   删除所有 `asyncio` 相关的 ping 函数（如 `async_ping_ip_port`），以保持代码库的纯净。

### 2.5. `core/server_manager.py`

**核心修改**: 将服务器选择逻辑完全同步化。

- **具体修改点**:
    1.  移除 `async` 关键字，将 `select_best_broker` 和 `health_check` 等函数改为同步 `def`。
    2.  在这些函数内部，确保所有对网络测速的调用都指向 `utils.network_utils.batch_ping_addresses_sync`。

### 2.6. 所有测试文件 (`tests/*.py`, `test_*.py`)

**核心修改**: 将所有测试用例改为同步执行。

- **具体修改点**:
    1.  移除所有 `import asyncio`。
    2.  将所有 `async def test_...` 函数改为 `def test_...`。
    3.  移除所有 `await` 调用。
    4.  在 `if __name__ == "__main__":` 块中，将 `asyncio.run(...)` 直接改为函数调用。

---

## 3. 风险与注意事项

1.  **线程安全**: 虽然 `EventEngine` 通常按顺序处理事件，但如果处理器函数内部启动了新线程（如我们的缓存更新处理器），则必须确保被调用的函数（`instrument_manager.update_cache_sync`）是线程安全的。如果它修改了共享数据，需要使用 `threading.Lock` 进行保护。
2.  **事件风暴**: 在某些异常情况下（如网络快速断开和重连），可能会在短时间内产生大量事件。需要确保事件处理逻辑是高效的，不会因此造成事件队列积压。
3.  **依赖链**: 确保所有函数的调用链都已从异步模型完全转换为同步模型。

---

## 4. 验证步骤

1.  **静态代码检查**: 完成代码修改后，全局搜索 `async` 和 `await` 关键字，确保没有遗漏。
2.  **单元测试与集成测试**: 运行所有修改后的测试文件，确保核心功能（服务器选择、合约加载、事件处理）正常。
3.  **手动运行与场景测试**:
    -   启动 `main.py`，验证系统能否正常连接并接收行情。
    -   **手动断开网络**，观察日志中是否正确触发了 `onFrontDisconnected`，是否发出了 `EVENT_RECONNECT_CTP` 事件，以及重连处理器是否被调用。
    -   **删除缓存文件**，启动程序，验证系统是否能从 API 获取数据，并触发 `EVENT_UPDATE_CACHE` 事件进行缓存重建。

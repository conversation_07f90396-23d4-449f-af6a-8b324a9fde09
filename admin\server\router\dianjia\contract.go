package dianjia

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type ContractRouter struct{}

// InitContractRouter 初始化合同路由信息 (V3)
func (s *ContractRouter) InitContractRouter(Router *gin.RouterGroup) {
	// V3: 统一使用新版API路径，移除所有向后兼容路由
	apiV1ContractsRouterWithoutRecord := Router.Group("dianjia/contracts")
	apiV1ContractRouter := Router.Group("dianjia/contract").Use(middleware.OperationRecord())
	apiV1ContractRouterWithoutRecord := Router.Group("dianjia/contract")

	contractApi := v1.ApiGroupApp.DianjiaApiGroup.ContractApi

	// 新的API路由 - 符合文档要求
	{
		// 获取合同列表 - 按角色分离
		apiV1ContractsRouterWithoutRecord.GET("as-setter", contractApi.GetContractsAsSetter) // 被点价方合同列表
		apiV1ContractsRouterWithoutRecord.GET("as-pricer", contractApi.GetContractsAsPricer) // 点价方合同列表

		// 单个合同操作
		apiV1ContractRouterWithoutRecord.GET(":contractId", contractApi.GetContractDetail) // 获取合同详情

		// 新增：被点价方操作
		apiV1ContractRouterWithoutRecord.GET(":contractId/cancel-records", contractApi.GetContractCancelRecords) // 获取取消记录

		// 基础CRUD操作
		apiV1ContractRouter.POST("", contractApi.CreateContract)              // 创建合同
		apiV1ContractRouter.PUT(":contractId", contractApi.UpdateContract)    // 更新合同
		apiV1ContractRouter.DELETE(":contractId", contractApi.DeleteContract) // 删除合同

		// V3 新增：合同状态管理
		apiV1ContractRouter.POST(":contractId/activate", contractApi.ActivateContract)     // 激活合同
		apiV1ContractRouter.POST(":contractId/deactivate", contractApi.DeactivateContract) // 挂起合同
		apiV1ContractRouter.POST(":contractId/cancel", contractApi.CancelContract)         // 取消合同（V3新版）
	}
}

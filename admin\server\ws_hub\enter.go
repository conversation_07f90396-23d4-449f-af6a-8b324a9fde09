package ws_hub

import "github.com/flipped-aurora/gin-vue-admin/server/global"

var GLOBAL_APP_HUB *AppHub
var GLOBAL_ORDER_HUB *OrderHub

// InitializeWebSocketHubs 初始化WebSocket Hub
// 这个函数应该在logger初始化之后调用
func InitializeWebSocketHubs() {
	global.GVA_LOG.Info("Initializing WebSocket Hubs...")
	GLOBAL_APP_HUB = NewAppHub()
	go GLOBAL_APP_HUB.Run()
	global.GVA_LOG.Info("App Hub initialized")
	GLOBAL_ORDER_HUB = NewOrderHub()
	go GLOBAL_ORDER_HUB.Run()
	global.GVA_LOG.Info("Order Hub initialized")
	global.GVA_LOG.Info("WebSocket Hubs initialized")
}

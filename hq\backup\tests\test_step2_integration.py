#!/usr/bin/env python
"""
步骤2集成测试脚本
测试Instrument信息获取API化与缓存功能，验证无数据库依赖的合约获取功能
"""
import asyncio
import os
import time
from pathlib import Path

# 测试各个组件
from utils.instrument_fetcher import InstrumentFetcher, InstrumentInfo
from utils.cache_manager import CacheManager
from core.instrument_manager import InstrumentManager, get_instrument


async def test_step2_integration():
    """测试步骤2的完整集成功能"""
    print("=== 步骤2：Instrument信息获取API化与缓存 - 集成测试 ===\n")
    
    # 1. 测试InstrumentFetcher
    print("1. 测试InstrumentFetcher...")
    try:
        fetcher = InstrumentFetcher()
        print("[OK] InstrumentFetcher初始化成功")
        
        # 测试同步获取
        print("  测试同步API获取...")
        instruments, error = fetcher.fetch_instruments_sync()
        if error:
            print(f"[WARN] 同步API获取失败: {error}")
        else:
            print(f"[OK] 同步API获取成功，获取 {len(instruments)} 个合约")
            if instruments:
                sample = instruments[0]
                print(f"  示例合约: {sample.InstrumentID} ({sample.ExchangeID})")
        
        # 测试异步获取
        print("  测试异步API获取...")
        instruments_async, error_async = await fetcher.fetch_instruments_async()
        if error_async:
            print(f"[WARN] 异步API获取失败: {error_async}")
        else:
            print(f"[OK] 异步API获取成功，获取 {len(instruments_async)} 个合约")
        
        print()
        
    except Exception as e:
        print(f"[FAIL] InstrumentFetcher测试失败: {e}")
        return False
    
    # 2. 测试CacheManager
    print("2. 测试CacheManager...")
    try:
        cache_manager = CacheManager()
        print("[OK] CacheManager初始化成功")
        print(f"[OK] 缓存目录: {cache_manager.cache_dir}")
        
        # 清空缓存以确保测试环境干净
        cache_manager.clear_cache()
        print("[OK] 清空旧缓存")
        
        # 如果有合约数据，测试缓存功能
        if 'instruments' in locals() and instruments:
            print("  测试缓存保存...")
            success = cache_manager.save_instruments(instruments)
            if success:
                print("[OK] 缓存保存成功")
                
                # 测试缓存加载
                print("  测试缓存加载...")
                cached_instruments, cache_error = cache_manager.load_instruments()
                if cache_error:
                    print(f"[FAIL] 缓存加载失败: {cache_error}")
                else:
                    print(f"[OK] 缓存加载成功，加载 {len(cached_instruments)} 个合约")
                    
                    # 验证数据一致性
                    if len(cached_instruments) == len(instruments):
                        print("[OK] 缓存数据数量一致")
                    else:
                        print(f"[WARN] 缓存数据数量不一致: 原始{len(instruments)}, 缓存{len(cached_instruments)}")
            else:
                print("[FAIL] 缓存保存失败")
        else:
            print("[WARN] 跳过缓存测试（没有可用的合约数据）")
        
        print()
        
    except Exception as e:
        print(f"[FAIL] CacheManager测试失败: {e}")
        return False
    
    # 3. 测试InstrumentManager集成功能
    print("3. 测试InstrumentManager集成功能...")
    try:
        # 清空缓存以测试完整流程
        cache_manager.clear_cache()
        
        manager = InstrumentManager()
        print("[OK] InstrumentManager初始化成功")
        
        # 测试同步加载（应该从API获取并创建缓存）
        print("  测试同步加载...")
        success = manager.load_instruments_sync()
        if success:
            print("[OK] 同步加载成功")
            instruments_loaded = manager.get_instruments()
            print(f"[OK] 获取到 {len(instruments_loaded)} 个合约")
            
            # 测试兼容接口
            instrument_list = manager.get_instrument_list()
            print(f"[OK] 兼容格式转换成功，{len(instrument_list)} 个合约")
            if instrument_list:
                print(f"  示例: {instrument_list[0]}")
        else:
            print("[FAIL] 同步加载失败")
        
        # 测试缓存优先加载
        print("  测试缓存优先加载...")
        manager2 = InstrumentManager()
        success2 = manager2.load_instruments_sync()
        if success2:
            print("[OK] 缓存优先加载成功")
            instruments_cached = manager2.get_instruments()
            print(f"[OK] 从缓存获取到 {len(instruments_cached)} 个合约")
        else:
            print("[WARN] 缓存优先加载失败")
        
        print()
        
    except Exception as e:
        print(f"[FAIL] InstrumentManager测试失败: {e}")
        return False
    
    # 4. 测试旧接口兼容性
    print("4. 测试旧接口兼容性...")
    try:
        # 测试get_instrument函数
        old_format_instruments = get_instrument()
        if old_format_instruments:
            print(f"[OK] 旧接口兼容测试成功，返回 {len(old_format_instruments)} 个合约")
            print(f"  示例格式: {old_format_instruments[0]} (应为tuple)")
            
            # 验证格式
            if isinstance(old_format_instruments[0], tuple) and len(old_format_instruments[0]) == 2:
                print("[OK] 旧接口返回格式正确 (symbol, exchange)")
            else:
                print("[FAIL] 旧接口返回格式不正确")
        else:
            print("[WARN] 旧接口返回空列表")
        
        print()
        
    except Exception as e:
        print(f"[FAIL] 旧接口兼容性测试失败: {e}")
        return False
    
    # 5. 测试缓存文件验证
    print("5. 验证缓存文件...")
    try:
        cache_info = cache_manager.get_cache_info()
        
        if cache_info.get("instruments_cache_exists"):
            print("[OK] 合约缓存文件存在")
            print(f"  文件大小: {cache_info.get('instruments_cache_size', 0) / 1024:.1f} KB")
            print(f"  修改时间: {cache_info.get('instruments_cache_mtime_readable', 'Unknown')}")
        else:
            print("[WARN] 合约缓存文件不存在")
        
        if cache_info.get("metadata_cache_exists"):
            print("[OK] 元数据缓存文件存在")
            metadata = cache_info.get("metadata", {})
            if metadata:
                print(f"  合约数量: {metadata.get('instruments_count', 0)}")
                print(f"  最后更新: {metadata.get('last_update_readable', 'Unknown')}")
        else:
            print("[WARN] 元数据缓存文件不存在")
        
        print()
        
    except Exception as e:
        print(f"[FAIL] 缓存文件验证失败: {e}")
        return False
    
    print("=== 步骤2集成测试完成 ===")
    print("[OK] 所有核心功能测试通过")
    print("[OK] InstrumentManager可以替代原有的数据库依赖功能")
    print("[OK] 系统已成功实现合约信息API化与缓存")
    
    return True


async def test_old_function_removal():
    """测试旧函数的移除"""
    print("\n=== 测试旧函数移除 ===")
    try:
        from engine.instrument import get_instrument
        print("[FAIL] 错误：旧模块仍然存在")
        return False
    except ImportError:
        print("[OK] 旧模块已正确移除")
        return True
    except Exception as e:
        print(f"[OK] 旧模块已移除: {e}")
        return True


async def test_performance():
    """测试性能"""
    print("\n=== 性能测试 ===")
    try:
        manager = InstrumentManager()
        
        # 测试首次加载时间
        start_time = time.time()
        success = manager.load_instruments_sync()
        first_load_time = time.time() - start_time
        
        if success:
            print(f"[OK] 首次加载耗时: {first_load_time:.2f} 秒")
            
            # 测试缓存加载时间
            manager2 = InstrumentManager()
            start_time = time.time()
            success2 = manager2.load_instruments_sync()
            cache_load_time = time.time() - start_time
            
            if success2:
                print(f"[OK] 缓存加载耗时: {cache_load_time:.2f} 秒")
                
                if cache_load_time < first_load_time:
                    print("[OK] 缓存加载比首次加载更快")
                else:
                    print("[WARN] 缓存加载未显著提升性能")
            else:
                print("[FAIL] 缓存加载失败")
        else:
            print("[FAIL] 首次加载失败")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] 性能测试失败: {e}")
        return False


if __name__ == "__main__":
    # 运行集成测试
    success = asyncio.run(test_step2_integration())
    
    # 测试旧函数移除
    removal_success = asyncio.run(test_old_function_removal())
    
    # 性能测试
    performance_success = asyncio.run(test_performance())
    
    if success and removal_success and performance_success:
        print("\n[SUCCESS] 步骤2重构成功！API化与缓存功能正常工作。")
        exit(0)
    else:
        print("\n[ERROR] 步骤2重构存在问题，请检查并修复。")
        exit(1)
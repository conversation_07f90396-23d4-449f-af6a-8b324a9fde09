<template>
  <wd-popup 
    v-model="visible" 
    position="center" 
    :close-on-click-modal="false"
    custom-style="border-radius: 16rpx; overflow: hidden;"
  >
    <view class="cancel-dialog">
      <!-- 标题 -->
      <view class="dialog-header">
        <text class="dialog-title">取消合同</text>
        <wd-icon name="close" size="20px" @click="handleClose" />
      </view>

      <!-- 合同信息 -->
      <view class="contract-info">
        <view class="info-item">
          <text class="label">合同编码:</text>
          <text class="value">{{ contractData?.contractCode }}</text>
        </view>
        <view class="info-item">
          <text class="label">剩余数量:</text>
          <text class="value">{{ contractData?.remainingQuantity }} 手</text>
        </view>
      </view>

      <!-- 取消数量输入 -->
      <view class="quantity-section">
        <view class="section-title">取消数量</view>
        <view class="quantity-input-row">
          <wd-input
            v-model="cancelQuantity"
            type="number"
            placeholder="请输入取消数量"
            :max="contractData?.remainingQuantity"
            :min="1"
            @input="handleQuantityInput"
          />
          <wd-button 
            type="info" 
            size="small" 
            @click="fillAllQuantity"
            custom-style="margin-left: 20rpx;"
          >
            全部
          </wd-button>
        </view>
        <view class="quantity-tip">
          <text>最大可取消: {{ contractData?.remainingQuantity }} 手</text>
        </view>
      </view>

      <!-- 取消原因输入 -->
      <view class="reason-section">
        <view class="section-title">取消原因（可选）</view>
        <wd-textarea
          v-model="cancelReason"
          placeholder="请输入取消原因，例如：线下提货"
          :maxlength="200"
          :show-word-limit="true"
          :auto-height="true"
        />
      </view>

      <!-- 操作按钮 -->
      <view class="dialog-actions">
        <wd-button 
          type="info" 
          size="large" 
          @click="handleClose"
          custom-style="margin-right: 20rpx;"
        >
          取消
        </wd-button>
        <wd-button 
          type="primary" 
          size="large" 
          :loading="loading"
          @click="handleConfirm"
        >
          确定
        </wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import type { IContract } from '@/types/contract'

// Props
interface Props {
  modelValue: boolean
  contractData?: IContract | null
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  contractData: null
})

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', data: { cancelQuantity: number; reason: string }): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const visible = ref(false)
const cancelQuantity = ref('')
const cancelReason = ref('')
const loading = ref(false)

// 计算属性
const maxQuantity = computed(() => props.contractData?.remainingQuantity || 0)

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    // 重置表单
    cancelQuantity.value = ''
    cancelReason.value = ''
    loading.value = false
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 方法
const handleClose = () => {
  visible.value = false
}

const fillAllQuantity = () => {
  cancelQuantity.value = String(maxQuantity.value)
}

const handleQuantityInput = (value: string) => {
  // 确保输入的数量不超过最大值
  const num = parseInt(value)
  if (num > maxQuantity.value) {
    cancelQuantity.value = String(maxQuantity.value)
  }
}

const handleConfirm = () => {
  const quantity = parseInt(cancelQuantity.value)
  
  // 验证输入
  if (!quantity || quantity <= 0) {
    uni.showToast({
      title: '请输入有效的取消数量',
      icon: 'error'
    })
    return
  }
  
  if (quantity > maxQuantity.value) {
    uni.showToast({
      title: '取消数量不能超过剩余数量',
      icon: 'error'
    })
    return
  }

  // 发送确认事件
  emit('confirm', {
    cancelQuantity: quantity,
    reason: cancelReason.value || '用户取消'
  })
}
</script>

<style lang="scss" scoped>
.cancel-dialog {
  width: 600rpx;
  background: white;
  padding: 40rpx;

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;

    .dialog-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .contract-info {
    background: #f8f9fa;
    border-radius: 12rpx;
    padding: 24rpx;
    margin-bottom: 30rpx;

    .info-item {
      display: flex;
      margin-bottom: 12rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        font-size: 26rpx;
        color: #666;
        width: 140rpx;
        flex-shrink: 0;
      }

      .value {
        font-size: 26rpx;
        color: #333;
        flex: 1;
      }
    }
  }

  .quantity-section, .reason-section {
    margin-bottom: 30rpx;

    .section-title {
      font-size: 28rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 20rpx;
    }

    .quantity-input-row {
      display: flex;
      align-items: center;
      margin-bottom: 12rpx;
    }

    .quantity-tip {
      font-size: 24rpx;
      color: #999;
    }
  }

  .dialog-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 40rpx;
  }
}
</style>

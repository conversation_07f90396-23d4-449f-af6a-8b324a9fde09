#!/usr/bin/env python3
"""
登录UI测试脚本

用于测试优化后的登录界面
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer

# 设置日志
logging.basicConfig(level=logging.INFO)

def test_login_ui():
    """测试登录UI"""
    try:
        # 创建应用
        app = QApplication(sys.argv)
        
        # 导入必要的模块
        from vnpy.event import EventEngine
        from ui.login.login_window import LoginWindow
        
        # 创建事件引擎
        event_engine = EventEngine()
        
        # 创建登录窗口
        login_window = LoginWindow(event_engine)
        
        # 显示窗口
        login_window.show_centered()
        
        # 设置窗口关闭时退出应用
        def on_window_closed():
            app.quit()
        
        login_window.finished.connect(on_window_closed)
        
        # 运行应用
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_login_ui()

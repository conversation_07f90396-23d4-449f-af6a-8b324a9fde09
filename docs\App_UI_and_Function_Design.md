# 移动端App功能与UI交互设计

**版本:** 2.0
**日期:** 2025年7月22日

---

## 1. 核心设计理念 (Core Design Philosophy)

本设计文档基于一个核心前提：**用户身份是流动的**。同一个用户在平台中可以同时拥有多种角色（既是A合同的卖方，也可能是B合同的买方）。因此，App的设计必须摒弃固化的角色界面，转而提供一个灵活、清晰、可切换的“工作台”模式。

1.  **统一App，身份融合 (Unified App, Fused Identities)**: 所有用户使用同一个App。系统不预设用户的单一角色，而是通过功能入口的选择来确定用户当前的操作意图。

2.  **情景化入口 (Contextual Entry Points)**: App的核心界面是一个“九宫格”或“田字格”布局的工作台。用户通过点击不同的功能入口（如“我是卖方”的合同管理 vs “我是买方”的合同中心），进入不同的业务情景。这使得角色切换变得直观自然。

3.  **交易为核心，数据为支撑 (Transaction-Centric, Data-Driven)**: 所有的功能设计最终都服务于高效、安全地完成交易。同时，为交易决策提供强大的数据看板支持。

---

## 2. 主界面设计：九宫格工作台 (Main Interface: The Grid Workspace)

用户登录后，将直接进入一个功能启动板，该界面采用九宫格布局，清晰地划分了不同角色和功能的入口。

**页面路径**: `app/src/pages/workspace/index.vue`

### 工作台布局

| | |
| :--- | :--- |
| **我是卖方 (Seller Context)** | **我是买方 (Pricer Context)** |
| **📄 合同管理**<br>创建和管理我作为卖方的所有合同。<br>*-> `pages/contract/setter-list.vue`* | **🛒 合同中心**<br>查看所有指派给我、可进行点价的合同。<br>*-> `pages/contract/pricer-list.vue`* |
| **✅ 交易审核**<br>审核买方提交的点价请求。<br>*-> `pages/trade/review-list.vue`* | **👆 发起点价**<br>快速入口，直接进入合同选择和点价操作页面。<br>*-> `pages/trade/execute.vue`* |
| **📈 卖方报表**<br>从卖方视角分析交易数据和合同执行情况。<br>*-> `pages/reports/setter-view.vue`* | **📊 买方报表**<br>从买方视角分析点价成本和交易历史。<br>*-> `pages/reports/pricer-view.vue`* |
| **通用工具 (Common Tools)** | |
| **💹 行情看板**<br>查看实时市场行情与分析图表。<br>*-> `pages/dashboard/index.vue`* | **🔔 消息通知**<br>聚合所有与我相关的业务通知（审核、成交等）。<br>*-> `pages/notifications/index.vue`* |
| **👤 账户中心**<br>管理个人与企业资料、账户安全。<br>*-> `pages/profile/index.vue`* | |


---

## 3. 核心功能流程重述 (Core Functional Flow Revisited)

基于新的工作台设计，用户的操作路径变得更加清晰。

### 3.1. 被点价方 (Setter) 流程
1.  **登录App**，进入九宫格工作台。
2.  点击 **[合同管理]**，查看、修改或新建一份点价合同。
3.  当收到点价请求时，App会推送通知。用户可点击 **[交易审核]** 入口，对 `PendingApproval` 状态的交易进行“接受”或“拒绝”操作。
4.  随时点击 **[卖方报表]** 查看名下所有合同的整体点价情况和平均成本。

### 3.2. 点价方 (Pricer) 流程
1.  **登录App**，进入九宫格工作台。
2.  点击 **[合同中心]**，浏览所有等待他点价的合同。
3.  选择一份合同，进入详情，点击“去点价”按钮，跳转至点价页面。
4.  在点价页面，系统实时显示指数价与计算后的一口价，用户输入数量后提交。
5.  提交后，可在 **[买方报表]** 或 **[消息通知]** 中跟踪这笔交易的后续状态（待审核、已通过、已成交等）。

---

## 4. 身份切换与数据隔离 (Identity Switching and Data Isolation)

- **前端视角**: 九宫格的UI设计完美解决了“身份切换”的问题。用户无需进行任何“切换账户”的操作，只需根据当前任务目标点击不同的功能块即可。

- **后端视角**: 后端API必须实现严格的数据隔离。当用户请求“合同管理”（卖方视角）列表时，API应查询 `contracts` 表中 `setter_id` 为当前登录用户ID的记录。当用户请求“合同中心”（买方视角）列表时，则查询 `pricer_id` 为当前用户ID的记录。API通过token解析出用户ID，并结合前端请求的“意图”（例如，通过不同的API路径 `/api/v1/contract/setter-list` vs `/api/v1/contract/pricer-list`）来返回正确的数据。

---

## 5. 交互流程示意图 (Updated Sequence Diagram)

```mermaid
sequenceDiagram
    participant User as 用户 (App)
    participant Workspace as 九宫格工作台
    participant System as 后端系统
    participant CTP as 期货柜台

    User->>Workspace: 登录后看到功能入口

    alt 卖方创建合同
        User->>Workspace: 点击 [合同管理]
        User->>System: 提交新合同数据
        System-->>User: 返回“创建成功”
    end

    alt 买方发起点价
        User->>Workspace: 点击 [合同中心]
        User->>System: 查看可点价合同
        System-->>User: 返回合同列表
        User->>System: 提交点价交易 (Trade)
        System-->>User: 返回“提交成功，待审核”
    end

    alt 卖方审核
        User->>Workspace: 点击 [交易审核]
        User->>System: 接受/拒绝交易
        System->>CTP: (若接受) 后台下单
        CTP-->>System: 返回执行结果
        System->>User: 推送最终成交/失败状态
    end
```

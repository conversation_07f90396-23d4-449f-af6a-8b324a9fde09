"""
基础对话框类

提供通用的对话框功能和样式。
"""

from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QIcon


class BaseDialog(QDialog):
    """基础对话框类
    
    提供通用的对话框功能，包括：
    - 标准布局
    - 通用按钮
    - 样式设置
    - 事件处理
    """
    
    # 信号定义
    accepted = Signal()
    rejected = Signal()
    
    def __init__(self, title: str = "对话框", parent=None):
        """初始化基础对话框
        
        Args:
            title: 窗口标题
            parent: 父窗口
        """
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setModal(True)
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
        
        # 设置默认大小
        self.resize(400, 300)
        
        # 初始化UI
        self.setup_ui()
        self.setup_connections()
        self.apply_styles()
    
    def setup_ui(self):
        """设置UI界面"""
        # 主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setSpacing(15)
        self.main_layout.setContentsMargins(20, 20, 20, 20)
        
        # 内容区域（子类重写）
        self.content_widget = self.create_content_widget()
        if self.content_widget:
            self.main_layout.addWidget(self.content_widget)
        
        # 按钮区域
        self.button_widget = self.create_button_widget()
        self.main_layout.addWidget(self.button_widget)
    
    def create_content_widget(self):
        """创建内容组件（子类重写）
        
        Returns:
            QWidget: 内容组件
        """
        return None
    
    def create_button_widget(self):
        """创建按钮区域
        
        Returns:
            QWidget: 按钮组件
        """
        widget = QLabel("基础对话框")
        widget.setAlignment(Qt.AlignCenter)
        return widget
    
    def setup_connections(self):
        """设置信号连接"""
        pass
    
    def apply_styles(self):
        """应用样式（子类可以重写）"""
        # 只有在子类没有重写此方法时才应用默认样式
        if self.__class__ == BaseDialog:
            self.setStyleSheet("""
                QDialog {
                    background-color: #f8f9fa;
                    border-radius: 8px;
                    font-family: "Microsoft YaHei", "Segoe UI", Arial, sans-serif;
                }

                QLabel {
                    color: #2c3e50;
                    font-size: 14px;
                }

                QPushButton {
                    padding: 8px 16px;
                    border: none;
                    border-radius: 4px;
                    font-size: 14px;
                    font-weight: 500;
                    min-width: 80px;
                    min-height: 32px;
                }

                QPushButton:hover {
                    opacity: 0.8;
                }

                QPushButton:pressed {
                    opacity: 0.6;
                }
            """)
    
    def center_on_screen(self):
        """居中显示"""
        from PySide6.QtWidgets import QApplication
        screen = QApplication.primaryScreen().geometry()
        size = self.geometry()
        x = (screen.width() - size.width()) // 2
        y = (screen.height() - size.height()) // 2
        self.move(x, y)
    
    def set_fixed_size(self, width: int, height: int):
        """设置固定大小"""
        self.setFixedSize(width, height)
    
    def show_centered(self):
        """居中显示对话框"""
        self.center_on_screen()
        self.show()
    
    def exec_centered(self):
        """居中显示模态对话框"""
        self.center_on_screen()
        return self.exec()
    
    def accept(self):
        """接受对话框"""
        self.accepted.emit()
        super().accept()
    
    def reject(self):
        """拒绝对话框"""
        self.rejected.emit()
        super().reject()

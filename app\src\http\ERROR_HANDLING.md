# HTTP错误处理规范

本文档规范了前端HTTP请求的错误处理机制，确保用户能够看到明确、友好的错误信息。

## 一、错误处理架构

### 1.1 错误类型分类

```typescript
interface HttpError {
  statusCode: number  // HTTP状态码
  data?: any         // 响应数据
  errMsg: string     // 错误信息
  code?: number      // 业务错误码
}
```

### 1.2 错误处理优先级

1. **业务错误** (code !== 0)：最高优先级
2. **HTTP状态错误** (401, 403, 404, 500等)：中等优先级  
3. **网络错误** (连接失败、超时)：基础优先级

## 二、错误处理流程

### 2.1 业务错误处理

```javascript
// 后端返回格式：{ code: 1001, msg: "用户名已存在", data: null }
if (responseData.code && responseData.code !== 0) {
  // 直接显示后端返回的具体错误信息
  title = responseData.msg || '操作失败'
}
```

**特点：**
- 优先显示后端返回的`msg`字段
- 这是最准确的业务错误信息
- 开发环境会打印详细的业务错误日志

### 2.2 HTTP状态错误处理

```javascript
switch (statusCode) {
  case 401: title = '登录已过期，请重新登录'; break
  case 403: title = '没有权限访问该资源'; break  
  case 404: title = '请求的资源不存在'; break
  case 500: title = '服务器内部错误，请稍后重试'; break
  // ...更多状态码处理
}
```

**特点：**
- 为常见HTTP状态码提供友好的中文提示
- 401错误会自动清理用户信息并跳转登录页
- 其他4xx/5xx错误会尝试使用服务器返回的错误信息

### 2.3 网络错误处理

```javascript
// 网络连接失败、请求超时等
fail(err) {
  const error: HttpError = {
    statusCode: 0,
    errMsg: '网络连接失败，请检查网络设置'
  }
}
```

## 三、开发规范

### 3.1 API调用规范

```typescript
// ✅ 正确：让HTTP模块处理错误
try {
  const res = await createContract(contractData)
  // 处理成功逻辑
} catch (error) {
  // HTTP模块已经显示了错误提示
  // 这里只需处理特定的业务逻辑（如表单重置等）
  console.log('合同创建失败')
}

// ❌ 错误：重复显示错误信息
try {
  const res = await createContract(contractData)
} catch (error) {
  uni.showToast({ // 不要这样做，HTTP模块已经处理了
    title: '创建失败',
    icon: 'none'
  })
}
```

### 3.2 后端接口规范要求

后端接口必须返回标准格式：

```json
{
  "code": 0,     // 0表示成功，非0表示业务错误
  "msg": "操作成功", // 错误或成功信息
  "data": {}     // 实际数据
}
```

### 3.3 自定义错误处理

如果需要自定义错误处理，使用`hideErrorToast`参数：

```typescript
// 隐藏默认错误提示，自行处理
try {
  const res = await http.post('/api/upload', formData, {}, {}, {
    hideErrorToast: true
  })
} catch (error) {
  // 自定义错误处理逻辑
  handleCustomError(error)
}
```

## 四、调试和测试

### 4.1 开发环境调试

开发环境下会在控制台打印详细错误信息：

```javascript
// 业务错误日志
console.warn('业务错误:', { code, msg, data, statusCode })

// HTTP错误日志  
console.error('HTTP错误:', { statusCode, msg, data, errMsg })
```

### 4.2 错误测试用例

建议测试以下场景：

1. **业务错误**：后端返回`code !== 0`的情况
2. **认证错误**：401状态码，验证是否自动跳转登录
3. **权限错误**：403状态码
4. **服务器错误**：500状态码
5. **网络错误**：断网、超时等情况

## 五、常见问题解决

### 5.1 为什么只显示"网络错误"？

**原因分析：**
1. 后端接口没有返回标准的`{ code, msg, data }`格式
2. 前端获取错误信息的逻辑有问题
3. `hideErrorToast`被错误设置为true

**解决方案：**
1. 检查后端接口返回格式
2. 查看浏览器控制台的错误日志
3. 确认API调用时没有设置`hideErrorToast: true`

### 5.2 如何显示更具体的错误信息？

确保后端返回的错误信息在`msg`字段中：

```json
// ✅ 正确格式
{
  "code": 1001,
  "msg": "用户名不能为空",  // 前端会显示这个信息
  "data": null
}

// ❌ 错误格式  
{
  "error": "用户名不能为空"  // 前端无法获取这个信息
}
```

### 5.3 特殊场景处理

某些特殊接口需要不同的错误处理方式：

```typescript
// 文件上传等特殊接口
const uploadFile = (file) => {
  return http.post('/upload', formData, {}, {}, {
    hideErrorToast: true,  // 隐藏默认提示
    timeout: 30000         // 延长超时时间
  }).catch(error => {
    // 自定义上传失败处理
    if (error.code === 1001) {
      uni.showToast({ title: '文件格式不支持', icon: 'none' })
    } else {
      uni.showToast({ title: '上传失败，请重试', icon: 'none' })
    }
    throw error
  })
}
```

## 六、最佳实践总结

1. **信任HTTP模块**：让HTTP模块处理所有通用错误
2. **标准化后端接口**：确保返回`{ code, msg, data }`格式
3. **避免重复提示**：不要在业务代码中重复显示错误提示
4. **合理使用hideErrorToast**：只在需要自定义处理时使用
5. **开发环境调试**：充分利用控制台错误日志
6. **用户体验优先**：确保错误提示清晰、友好、可操作

---

通过遵循这些规范，可以确保：
- 用户总能看到明确的错误信息
- 开发者可以快速定位问题
- 错误处理逻辑统一且易于维护
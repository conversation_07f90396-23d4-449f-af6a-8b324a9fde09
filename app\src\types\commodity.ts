/**
 * 商品相关类型定义
 */

// 商品基础信息
export interface ICommodity {
  id: number
  name: string
  product_id: string
  exchange_id: string
  created_at: string
  updated_at: string
  instrument_count?: number
}

// 商品请求参数
export interface ICommodityRequest {
  id?: number
  name: string
  product_id: string
  exchange_id: string
}

// 商品列表查询参数
export interface ICommodityListRequest {
  page?: number
  pageSize?: number
  name?: string
  product_id?: string
  exchange_id?: string
}

// 商品列表响应
export interface ICommodityListResponse {
  list: ICommodity[]
  total: number
  page: number
  pageSize: number
}
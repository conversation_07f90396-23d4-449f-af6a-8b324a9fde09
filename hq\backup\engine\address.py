import json
import socket
import time
from typing import Optional, Dict, List
# from config import get_db_config  # 已移除数据库依赖
# import pymysql  # 已移除数据库依赖
import asyncio
import concurrent.futures


def ping(address: str, port: int) -> float:
    """测试行情服务器延迟情况，返回延迟时间
    
    Args:
        address: 服务器地址
        port: 端口号
        
    Returns:
        float: 延迟时间(ms)，如果连接失败返回-1
    """
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    s.settimeout(0.5)
    try:
        start_time = time.perf_counter()  # 使用更精确的计时器
        s.connect((address, port))
        s.send(b'ping')
        s.recv(1024)
        delay = (time.perf_counter() - start_time) * 1000
        return delay
    except Exception as e:
        return -1
    finally:
        s.close()


# 封装ping_ip_port函数，方便调用
def ping_ip_port(ip_port):
    split_result = ip_port.split(':')
    if len(split_result) < 2:
        return -1
    ip = split_result[-2]
    port = split_result[-1]
    return ping(ip, int(port))


async def async_ping_ip_port(ip_port: str) -> tuple[str, float]:
    """异步版本的ping测试"""
    split_result = ip_port.split(':')
    if len(split_result) < 2:
        return ip_port, -1
    ip = split_result[-2]
    port = int(split_result[-1])
    
    loop = asyncio.get_event_loop()
    with concurrent.futures.ThreadPoolExecutor() as pool:
        result = await loop.run_in_executor(pool, ping, ip, port)
    return ip_port, result


async def batch_ping_addresses(addresses: List[str]) -> Dict[str, float]:
    """并发测试多个地址"""
    tasks = [async_ping_ip_port(addr) for addr in addresses]
    results = await asyncio.gather(*tasks)
    return {addr: speed for addr, speed in results if speed > 0}


# 已弃用：从数据库中获取行情服务器地址的函数已迁移到 ServerManager
# def get_md_address(broker_id: Optional[str] = None, exclude_broker_id: Optional[List[str]] = None):
#     """
#     该函数已被弃用，请使用 core.server_manager.ServerManager 替代
#     """
#     raise DeprecationWarning(
#         "get_md_address 函数已被弃用，请使用 core.server_manager.ServerManager.select_best_broker() 替代"
#     )

if __name__ == '__main__':
    print("该模块中的数据库相关功能已迁移到 core.server_manager.ServerManager")
    print("请使用 ServerManager 进行期货公司服务器的选择和管理")

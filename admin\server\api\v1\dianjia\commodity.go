package dianjia

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/dianjia"
	dianjiaService "github.com/flipped-aurora/gin-vue-admin/server/service/dianjia"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type CommodityApi struct{}

var commodityService = dianjiaService.CommodityService{}

// CreateCommodity 创建商品
// @Tags Commodity
// @Summary 创建商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body dianjia.CommodityRequest true "商品信息"
// @Success 200 {object} response.Response{data=dianjia.Commodity,msg=string} "创建成功"
// @Router /commodity/createCommodity [post]
func (commodityApi *CommodityApi) CreateCommodity(c *gin.Context) {
	var req dianjia.CommodityRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = utils.Verify(req, utils.CommodityVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	commodity, err := commodityService.CreateCommodity(req)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
		return
	}
	response.OkWithDetailed(commodity, "创建成功", c)
}

// DeleteCommodity 删除商品
// @Tags Commodity
// @Summary 删除商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "ID"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /commodity/deleteCommodity [delete]
func (commodityApi *CommodityApi) DeleteCommodity(c *gin.Context) {
	ID := c.Query("ID")
	id, err := strconv.Atoi(ID)
	if err != nil {
		response.FailWithMessage("ID格式错误", c)
		return
	}
	err = commodityService.DeleteCommodity(uint(id))
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteCommodityByIds 批量删除商品
// @Tags Commodity
// @Summary 批量删除商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除商品"
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /commodity/deleteCommodityByIds [delete]
func (commodityApi *CommodityApi) DeleteCommodityByIds(c *gin.Context) {
	IDs := c.QueryArray("IDs[]")
	var ids []uint
	for _, id := range IDs {
		ID, err := strconv.Atoi(id)
		if err != nil {
			response.FailWithMessage("ID格式错误", c)
			return
		}
		ids = append(ids, uint(ID))
	}
	err := commodityService.DeleteCommodityByIds(ids)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateCommodity 更新商品
// @Tags Commodity
// @Summary 更新商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body dianjia.CommodityRequest true "商品信息"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /commodity/updateCommodity [put]
func (commodityApi *CommodityApi) UpdateCommodity(c *gin.Context) {
	var req dianjia.CommodityRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = utils.Verify(req, utils.CommodityVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = commodityService.UpdateCommodity(req)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindCommodity 用id查询商品
// @Tags Commodity
// @Summary 用id查询商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query dianjia.Commodity true "用id查询商品"
// @Success 200 {object} response.Response{data=dianjia.Commodity,msg=string} "查询成功"
// @Router /commodity/findCommodity [get]
func (commodityApi *CommodityApi) FindCommodity(c *gin.Context) {
	ID := c.Query("ID")
	id, err := strconv.Atoi(ID)
	if err != nil {
		response.FailWithMessage("ID格式错误", c)
		return
	}
	commodity, err := commodityService.GetCommodity(uint(id))
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
		return
	}
	response.OkWithDetailed(commodity, "查询成功", c)
}

// GetCommodityList 分页获取商品列表
// @Tags Commodity
// @Summary 分页获取商品列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query dianjia.CommodityListRequest true "分页获取商品列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /commodity/getCommodityList [get]
func (commodityApi *CommodityApi) GetCommodityList(c *gin.Context) {
	var pageInfo dianjia.CommodityListRequest
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if pageInfo.PageSize == 0 {
		pageInfo.PageSize = 10
	}
	if pageInfo.Page == 0 {
		pageInfo.Page = 1
	}

	list, total, err := commodityService.GetCommodityInfoList(pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetAllCommodityList 获取所有商品列表
// @Tags Commodity
// @Summary 获取所有商品列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=[]dianjia.CommodityResponse,msg=string} "获取成功"
// @Router /commodity/getAllCommodityList [get]
func (commodityApi *CommodityApi) GetAllCommodityList(c *gin.Context) {
	list, err := commodityService.GetCommodityList()
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	response.OkWithDetailed(list, "获取成功", c)
}
package dianjia

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type TradeRequestRouter struct{}

// InitTradeRequestRouter 初始化交易请求路由
func (s *TradeRequestRouter) InitTradeRequestRouter(Router *gin.RouterGroup) {
	tradeRequestRouter := Router.Group("dianjia/traderequests").Use(middleware.OperationRecord())
	tradeRequestRouterWithoutRecord := Router.Group("dianjia/traderequests")
	tradeRequestApi := v1.ApiGroupApp.DianjiaApiGroup.TradeRequestApi

	{
		tradeRequestRouter.POST("", tradeRequestApi.CreateTradeRequest)           // 创建交易请求
		tradeRequestRouter.POST(":id/cancel", tradeRequestApi.CancelTradeRequest) // 取消交易请求
		tradeRequestRouter.POST(":id/reject", tradeRequestApi.RejectTradeRequest) // 拒绝交易请求 (V4新增)
		tradeRequestRouter.POST(":id/feedback", tradeRequestApi.ManualFeedback)   // 人工反馈交易结果
	}
	{
		tradeRequestRouterWithoutRecord.GET("as-pricer", tradeRequestApi.GetTradeRequestsForPricer) // 获取点价方交易请求列表 (V4路径调整)
		tradeRequestRouterWithoutRecord.GET("as-setter", tradeRequestApi.GetTradeRequestsForSetter) // 获取被点价方交易请求列表 (V4路径调整)
		tradeRequestRouterWithoutRecord.GET(":id", tradeRequestApi.GetTradeRequest)                 // 获取单个交易请求
	}
}

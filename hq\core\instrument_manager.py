"""
合约管理器模块
负责合约信息的获取、缓存和管理，整合API获取和本地缓存功能
"""
from typing import List, Tuple, Optional
from utils.instrument_fetcher import InstrumentFetcher, InstrumentInfo
from utils.cache_manager import CacheManager


class InstrumentManager:
    """合约管理器，负责合约信息的智能获取和缓存管理"""
    
    def __init__(self, api_url: str = "http://dict.openctp.cn/prices?types=futures",
                 cache_dir: str = None, max_cache_age_hours: float = 24.0):
        """
        初始化合约管理器
        
        Args:
            api_url: OpenCTP API地址
            cache_dir: 缓存目录路径
            max_cache_age_hours: 最大缓存年龄（小时）
        """
        self.fetcher = InstrumentFetcher(api_url)
        self.cache_manager = CacheManager(cache_dir)
        self.max_cache_age_hours = max_cache_age_hours
        self.instruments: List[InstrumentInfo] = []
        
        print(f"合约管理器初始化完成")
        print(f"API地址: {api_url}")
        print(f"缓存目录: {self.cache_manager.cache_dir}")
        print(f"最大缓存年龄: {max_cache_age_hours} 小时")
    
    def load_instruments_sync(self) -> Tuple[bool, bool]:
        """
        同步方式加载合约信息
        优先从缓存加载，如果缓存无效则从API获取
        
        Returns:
            Tuple[bool, bool]: (success, needs_update) - 成功状态和是否需要更新
        """
        try:
            print("开始加载合约信息...")
            
            # 1. 优先尝试从缓存加载
            if self.cache_manager.is_cache_valid(self.max_cache_age_hours):
                print("缓存有效，从缓存加载合约信息")
                instruments, error = self.cache_manager.load_instruments()
                
                if not error and instruments:
                    self.instruments = instruments
                    print(f"成功从缓存加载 {len(instruments)} 个合约")
                    
                    # 返回成功且建议更新
                    print("建议后台更新缓存...")
                    return True, True
                else:
                    print(f"缓存加载失败: {error}，尝试从API获取")
            else:
                print("缓存无效或不存在，从API获取合约信息")
            
            # 2. 从API获取
            instruments, error = self.fetcher.fetch_instruments_sync()
            
            if error:
                print(f"API获取失败: {error}")
                
                # 3. 如果API失败，尝试加载过期缓存作为备用
                print("尝试加载过期缓存作为备用...")
                instruments, cache_error = self.cache_manager.load_instruments()
                
                if not cache_error and instruments:
                    self.instruments = instruments
                    print(f"使用过期缓存，加载了 {len(instruments)} 个合约")
                    return True, False
                else:
                    print(f"过期缓存也无法加载: {cache_error}")
                    return False, False
            else:
                # 4. API获取成功，更新缓存
                self.instruments = instruments
                print(f"API获取成功，共 {len(instruments)} 个合约")
                
                # 保存到缓存
                if self.cache_manager.save_instruments(instruments):
                    print("成功更新缓存")
                else:
                    print("更新缓存失败")
                
                return True, False
                
        except Exception as e:
            print(f"加载合约信息异常: {str(e)}")
            
            # 异常情况下尝试加载任何可用的缓存
            try:
                instruments, error = self.cache_manager.load_instruments()
                if not error and instruments:
                    self.instruments = instruments
                    print(f"异常恢复：使用缓存数据，加载了 {len(instruments)} 个合约")
                    return True, False
            except Exception:
                pass
            
            return False, False
    
    def update_cache_sync(self):
        """同步更新缓存"""
        try:
            print("同步更新缓存中...")
            instruments, error = self.fetcher.fetch_instruments_sync()
            
            if not error and instruments:
                # 只有获取到更多或不同的数据时才更新
                if len(instruments) >= len(self.instruments):
                    self.instruments = instruments
                    self.cache_manager.save_instruments(instruments)
                    print(f"缓存更新完成 ({len(instruments)} 个合约)")
                else:
                    print("获取的数据较少，保持当前数据")
            else:
                print(f"缓存更新失败: {error}")
                
        except Exception as e:
            print(f"缓存更新异常: {str(e)}")
    
    def get_instruments(self) -> List[InstrumentInfo]:
        """
        获取当前加载的合约信息列表
        
        Returns:
            list: 合约信息列表
        """
        return self.instruments.copy()
    
    def get_instrument_list(self) -> List[Tuple[str, str]]:
        """
        获取兼容旧系统格式的合约列表
        
        Returns:
            list: [(symbol, exchange), ...] 格式的合约列表
        """
        return [(inst.InstrumentID, inst.ExchangeID) for inst in self.instruments]
    
    def get_instrument_by_id(self, instrument_id: str) -> Optional[InstrumentInfo]:
        """
        根据合约ID获取合约信息
        
        Args:
            instrument_id: 合约ID
            
        Returns:
            InstrumentInfo: 合约信息，如果未找到返回None
        """
        for inst in self.instruments:
            if inst.InstrumentID == instrument_id:
                return inst
        return None
    
    def get_instruments_by_exchange(self, exchange_id: str) -> List[InstrumentInfo]:
        """
        根据交易所获取合约列表
        
        Args:
            exchange_id: 交易所ID
            
        Returns:
            list: 该交易所的合约列表
        """
        return [inst for inst in self.instruments if inst.ExchangeID == exchange_id]
    
    def get_cache_info(self):
        """获取缓存信息"""
        return self.cache_manager.get_cache_info()
    
    def force_refresh(self) -> bool:
        """
        强制刷新合约信息（忽略缓存）
        
        Returns:
            bool: 刷新是否成功
        """
        print("强制刷新合约信息...")
        instruments, error = self.fetcher.fetch_instruments_sync()
        
        if error:
            print(f"强制刷新失败: {error}")
            return False
        
        self.instruments = instruments
        self.cache_manager.save_instruments(instruments)
        print(f"强制刷新成功，获取 {len(instruments)} 个合约")
        return True


# 全局实例（单例模式）
_instrument_manager_instance = None

def get_instrument_manager(**kwargs) -> InstrumentManager:
    """
    获取全局合约管理器实例（单例模式）
    
    Args:
        **kwargs: 传递给InstrumentManager的参数
        
    Returns:
        InstrumentManager: 合约管理器实例
    """
    global _instrument_manager_instance
    if _instrument_manager_instance is None:
        _instrument_manager_instance = InstrumentManager(**kwargs)
    return _instrument_manager_instance

def get_instrument() -> List[Tuple[str, str]]:
    """
    获取合约列表（兼容旧系统接口）
    
    Returns:
        list: [(symbol, exchange), ...] 格式的合约列表
    """
    manager = get_instrument_manager()
    
    # 如果还没有加载过合约，先加载
    if not manager.instruments:
        success, _ = manager.load_instruments_sync()
        if not success:
            print("警告: 获取合约信息失败，返回空列表")
            return []
    
    return manager.get_instrument_list()


# 示例用法
def main():
    """示例用法"""
    print("=== 合约管理器测试 ===")
    
    # 创建合约管理器
    manager = InstrumentManager()
    
    # 同步加载
    print("\n1. 同步加载测试:")
    success, needs_update = manager.load_instruments_sync()
    print(f"同步加载结果: {success}, 需要更新: {needs_update}")
    
    if success:
        instruments = manager.get_instruments()
        print(f"当前有 {len(instruments)} 个合约")
        
        if instruments:
            print(f"示例合约: {instruments[0].InstrumentID} ({instruments[0].ExchangeID})")
        
        # 获取兼容格式
        instrument_list = manager.get_instrument_list()
        print(f"兼容格式示例: {instrument_list[:3]}")
    
    # 测试旧接口兼容性
    print("\n2. 旧接口兼容性测试:")
    old_format_instruments = get_instrument()
    print(f"旧接口返回 {len(old_format_instruments)} 个合约")
    if old_format_instruments:
        print(f"示例: {old_format_instruments[:3]}")


if __name__ == "__main__":
    main()
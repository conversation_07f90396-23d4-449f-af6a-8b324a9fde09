<template>
  <view class="contract-entrance-page">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">合同管理</text>
    </view>

    <!-- 角色选择 -->
    <view class="role-selection">
      <view class="role-card setter" @click="goToSetterList">
        <view class="role-icon">
          <text class="icon">📝</text>
        </view>
        <view class="role-info">
          <text class="role-name">我的合同</text>
          <text class="role-desc">创建和管理合同资源池</text>
        </view>
        <view class="role-arrow">
          <text class="arrow">›</text>
        </view>
      </view>

      <view class="role-card pricer" @click="goToPricerList">
        <view class="role-icon">
          <text class="icon">💼</text>
        </view>
        <view class="role-info">
          <text class="role-name">合同中心</text>
          <text class="role-desc">查看可执行合同</text>
        </view>
        <view class="role-arrow">
          <text class="arrow">›</text>
        </view>
      </view>
    </view>

    <!-- 快捷操作 -->
    <view class="quick-actions">
      <text class="section-title">快捷操作</text>
      
      <view class="action-list">
        <view class="action-item" @click="goToCreate">
          <view class="action-icon">
            <text class="icon">➕</text>
          </view>
          <text class="action-name">新建合同</text>
        </view>
        
        <view class="action-item" @click="goToSetterList">
          <view class="action-icon">
            <text class="icon">📋</text>
          </view>
          <text class="action-name">管理合同</text>
        </view>
        
        <view class="action-item" @click="goToPricerList">
          <view class="action-icon">
            <text class="icon">🎯</text>
          </view>
          <text class="action-name">查看合同</text>
        </view>

        <view class="action-item" @click="goToSetterManagement">
          <view class="action-icon">
            <text class="icon">⚖️</text>
          </view>
          <text class="action-name">处理请求</text>
        </view>
      </view>
    </view>
  </view>
</template>

<route lang="jsonc" type="page">
{
  "style": {
    "navigationBarTitleText": "合同管理"
  }
}
</route>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

// 跳转到被点价方页面
function goToSetterList() {
  router.push('/pages/contract/setter-list')
}

// 跳转到点价方页面
function goToPricerList() {
  router.push('/pages/contract/pricer-list')
}

// 创建新合同
function goToCreate() {
  router.push('/pages/contract/form')
}

// 跳转到被点价方管理页面
function goToSetterManagement() {
  router.push('/pages/trade/setter-management')
}
</script>

<style lang="scss" scoped>
.contract-entrance-page {
  padding: 20rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.page-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 40rpx;

  .page-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.role-selection {
  margin-bottom: 60rpx;

  .role-card {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 12rpx;
    padding: 40rpx 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
    }

    &.setter {
      border-left: 8rpx solid #667eea;
    }

    &.pricer {
      border-left: 8rpx solid #764ba2;
    }

    .role-icon {
      margin-right: 30rpx;

      .icon {
        font-size: 48rpx;
      }
    }

    .role-info {
      flex: 1;

      .role-name {
        display: block;
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 8rpx;
      }

      .role-desc {
        font-size: 26rpx;
        color: #666;
      }
    }

    .role-arrow {
      .arrow {
        font-size: 40rpx;
        color: #ccc;
      }
    }
  }
}

.quick-actions {
  .section-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
    display: block;
  }

  .action-list {
    display: flex;
    justify-content: space-around;

    .action-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      background: white;
      border-radius: 12rpx;
      padding: 30rpx 20rpx;
      min-width: 160rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
      }

      .action-icon {
        margin-bottom: 16rpx;

        .icon {
          font-size: 40rpx;
        }
      }

      .action-name {
        font-size: 24rpx;
        color: #333;
        font-weight: 500;
      }
    }
  }
}
</style>
# flake8: noqa
"""
Order客户端主程序

集成登录功能和WebSocket连接，管理应用程序生命周期。
"""

import sys
import logging
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

from vnpy.event import EventEngine
from vnpy.trader.engine import MainEngine
from vnpy.trader.event import EVENT_LOG
from vnpy.trader.ui import MainWindow as VnpyMainWindow, create_qapp

# 导入认证模块
from auth import LoginManager, EVENT_USER_LOGIN_SUCCESS, EVENT_USER_LOGIN_FAILED

# 导入UI模块
from ui import LoginWindow

# 导入WebSocket模块
from websock.client import WebSocketClient
from websock.models import WSConfig
from websock.events import EVENT_WS_ERROR, EVENT_WS_AUTHENTICATED


def print_log(event):
    """打印日志事件"""
    log = event.data
    print(f"{log.time}\t{log.msg}")


class OrderMainWindow(VnpyMainWindow):
    """扩展的主窗口类，集成登录功能"""

    def __init__(self, main_engine: MainEngine, event_engine: EventEngine):
        super().__init__(main_engine, event_engine)
        self.logger = logging.getLogger(__name__)

        # 设置窗口标题
        self.setWindowTitle("Order交易客户端")

    def check_login_and_show(self):
        """检查登录状态并显示窗口"""
        self.logger.info("检查登录状态")

        # 这个方法现在由OrderApplication来处理登录逻辑
        self.showMaximized()
        return True


class OrderApplication:
    """Order应用程序主类"""

    def __init__(self):
        self.qapp = None
        self.event_engine = None
        self.login_manager = None
        self.ws_client = None
        self.main_engine = None
        self.main_window = None
        self.logger = logging.getLogger(__name__)

    def initialize(self):
        """初始化应用程序"""
        # 创建QApplication
        self.qapp = create_qapp()
        self.qapp.setApplicationName("Order交易客户端")
        self.qapp.setApplicationVersion("1.0.0")

        # 创建事件引擎
        self.event_engine = EventEngine()
        self.event_engine.register(EVENT_LOG, print_log)

        # 创建登录管理器
        self.login_manager = LoginManager(self.event_engine)

        # 创建WebSocket客户端
        self.ws_client = WebSocketClient(self.event_engine)

        # 加载WebSocket配置
        self._load_websocket_config()

        # 创建vnpy主引擎
        self.main_engine = MainEngine(self.event_engine)

        # 创建主窗口
        self.main_window = OrderMainWindow(self.main_engine, self.event_engine)

        # 设置事件监听
        self._setup_event_handlers()

        self.logger.info("应用程序初始化完成")

    def _load_websocket_config(self):
        """加载WebSocket配置"""
        try:
            config_path = Path("config/websocket_config.yaml")
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    import yaml
                    config_data = yaml.safe_load(f)

                # 获取WebSocket配置部分
                ws_config_data = config_data.get('websocket', {})
                server_config = ws_config_data.get('server', {})
                heartbeat_config = ws_config_data.get('heartbeat', {})
                reconnect_config = ws_config_data.get('reconnect', {})

                # 创建WSConfig实例
                ws_config = WSConfig(
                    server_url=server_config.get('url', 'ws://localhost:8888/ws/order'),
                    heartbeat_interval=heartbeat_config.get('interval', 30),
                    heartbeat_timeout=heartbeat_config.get('timeout', 45),
                    reconnect_enabled=reconnect_config.get('enabled', True),
                    max_reconnect_attempts=reconnect_config.get('max_attempts', 10)
                )
                self.ws_client.config = ws_config
                self.logger.info(f"WebSocket配置已加载: {ws_config.server_url}")
            else:
                self.logger.warning("WebSocket配置文件不存在，使用默认配置")
                # 创建默认配置
                ws_config = WSConfig(
                    server_url='ws://localhost:8888/ws/order',
                    heartbeat_interval=30,
                    heartbeat_timeout=45,
                    reconnect_enabled=True,
                    max_reconnect_attempts=10
                )
                self.ws_client.config = ws_config
        except Exception as e:
            self.logger.error(f"加载WebSocket配置失败: {str(e)}")
            # 使用默认配置作为后备
            ws_config = WSConfig(
                server_url='ws://localhost:8888/ws/order',
                heartbeat_interval=30,
                heartbeat_timeout=45,
                reconnect_enabled=True,
                max_reconnect_attempts=10
            )
            self.ws_client.config = ws_config

    def _setup_event_handlers(self):
        """设置事件处理器"""
        # 登录相关事件
        self.event_engine.register(EVENT_USER_LOGIN_SUCCESS, self._on_login_success)
        self.event_engine.register(EVENT_USER_LOGIN_FAILED, self._on_login_failed)

        # WebSocket相关事件
        self.event_engine.register(EVENT_WS_ERROR, self._on_ws_error)
        self.event_engine.register(EVENT_WS_AUTHENTICATED, self._on_ws_authenticated)

    def _on_login_success(self, event):
        """处理登录成功事件"""
        token_data = event.data
        username = token_data.user_info.get('username', '用户')
        self.logger.info(f"用户 {username} 登录成功")

    def _on_login_failed(self, event):
        """处理登录失败事件"""
        login_result = event.data
        self.logger.error(f"登录失败：{login_result.message}")

    def _on_ws_error(self, event):
        """处理WebSocket错误"""
        error_data = event.data
        self.logger.error(f"WebSocket连接错误: {error_data}")

    def _on_ws_authenticated(self, event):
        """处理WebSocket认证成功"""
        self.logger.info("WebSocket认证成功")

    def check_login_and_show(self):
        """检查登录状态并显示窗口"""
        self.logger.info("检查登录状态")

        # 检查是否已登录
        if self.login_manager.is_logged_in():
            self.logger.info("用户已登录，直接显示主窗口")
            self.main_window.showMaximized()
            return True

        # 需要登录，显示登录窗口
        self.logger.info("用户未登录，显示登录窗口")
        return self._show_login_window()

    def _show_login_window(self):
        """显示登录窗口"""
        # 创建登录窗口
        login_window = LoginWindow(self.event_engine, self.login_manager, None)

        # 显示登录窗口
        result = login_window.exec_centered()

        if result == LoginWindow.Accepted:
            self.logger.info("登录成功，显示主窗口")
            self.main_window.showMaximized()
            return True
        else:
            self.logger.info("用户取消登录")
            return False


    def run(self):
        """运行应用程序"""
        try:
            self.initialize()

            if self.check_login_and_show():
                self.logger.info("应用程序开始运行")
                return self.qapp.exec()
            else:
                self.logger.info("用户取消登录，退出应用")
                return 0
        except Exception as e:
            self.logger.error(f"应用程序异常：{str(e)}")
            return 1
        finally:
            self.cleanup()

    def cleanup(self):
        """清理资源"""
        try:
            # 1. 完全关闭WebSocket客户端
            if self.ws_client:
                try:
                    self.ws_client.close()
                    self.logger.info("WebSocket客户端已关闭")
                except Exception as e:
                    self.logger.warning(f"WebSocket客户端关闭失败: {str(e)}")

            # 2. 关闭主引擎
            if self.main_engine:
                try:
                    self.main_engine.close()
                    self.logger.info("主引擎已关闭")
                except Exception as e:
                    self.logger.warning(f"主引擎关闭失败: {str(e)}")

            # 3. 停止事件引擎
            if self.event_engine:
                try:
                    self.event_engine.stop()
                    self.logger.info("事件引擎已停止")
                except Exception as e:
                    self.logger.warning(f"事件引擎停止失败: {str(e)}")

            # 4. 确保QApplication正确退出
            if self.qapp:
                try:
                    self.qapp.processEvents()  # 处理剩余事件
                    self.logger.info("QApplication事件已处理")
                except Exception as e:
                    self.logger.warning(f"QApplication事件处理失败: {str(e)}")

            self.logger.info("应用程序资源清理完成")
        except Exception as e:
            self.logger.error(f"资源清理异常: {str(e)}")


def setup_logging():
    """设置日志"""
    # 创建logs目录
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(logs_dir / 'order_main.log', encoding='utf-8')
        ]
    )


def main():
    """主函数"""
    setup_logging()
    app = OrderApplication()
    return app.run()


if __name__ == "__main__":
    sys.exit(main())

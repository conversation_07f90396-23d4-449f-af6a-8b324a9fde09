"""
登录窗口主类

管理整个登录界面，协调用户交互。
"""

import asyncio
import logging
from typing import Optional

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QMessageBox, QApplication, QWidget
)
from PySide6.QtCore import Qt, QTimer, Signal, QThread, QObject, QUrl
from PySide6.QtGui import QFont, QIcon, QPixmap
from PySide6.QtNetwork import QNetworkAccessManager, QNetworkRequest, QNetworkReply

from vnpy.event import EventEngine, Event

from auth.managers.login_manager import LoginManager
from auth.models import LoginCredentials, LoginResult
from auth.events import (
    EVENT_USER_LOGIN_SUCCESS, EVENT_USER_LOGIN_FAILED,
    EVENT_SMS_CODE_SENT, EVENT_SMS_CODE_SEND_FAILED
)
from ..base.base_dialog import BaseDialog
# 使用Qt默认样式
from .login_widget import LoginWidget
from .status_indicator import StatusIndicator


class AsyncLoginWorker(QObject):
    """异步登录工作器"""

    # 信号定义
    login_finished = Signal(object)  # LoginResult
    sms_sent = Signal(bool)
    
    def __init__(self, login_manager: LoginManager):
        super().__init__()
        self.login_manager = login_manager
    
    def login_with_password(self, username: str, password: str, captcha: str, captcha_id: str, remember_me: bool):
        """执行密码登录"""
        async def _login():
            result = await self.login_manager.login_with_password(
                username, password, remember_me, captcha, captcha_id
            )
            self.login_finished.emit(result)

        asyncio.run(_login())
    
    def login_with_phone(self, phone: str, sms_code: str, remember_me: bool):
        """执行手机登录"""
        async def _login():
            result = await self.login_manager.login_with_phone(phone, sms_code, remember_me)
            self.login_finished.emit(result)
        
        asyncio.run(_login())
    
    def auto_login(self):
        """执行自动登录"""
        async def _login():
            result = await self.login_manager.auto_login()
            self.login_finished.emit(result)
        
        asyncio.run(_login())
    
    def send_sms_code(self, phone: str):
        """发送短信验证码"""
        async def _send():
            success = await self.login_manager.auth_service.send_sms_code(phone)
            self.sms_sent.emit(success)
        
        asyncio.run(_send())


class LoginWindow(BaseDialog):
    """登录窗口主类
    
    管理整个登录界面，协调用户交互，包括：
    - 登录方式切换
    - 用户输入验证
    - 登录状态显示
    - 事件处理和响应
    """
    
    # 信号定义
    login_success = Signal(object)  # 登录成功信号
    login_cancelled = Signal()      # 登录取消信号
    
    def __init__(self, event_engine: EventEngine, login_manager: Optional[LoginManager] = None, parent=None):
        """初始化登录窗口
        
        Args:
            event_engine: 事件引擎
            login_manager: 登录管理器（可选）
            parent: 父窗口
        """
        self.event_engine = event_engine
        self.login_manager = login_manager or LoginManager(event_engine)
        self.logger = logging.getLogger(__name__)
        
        # 倒计时相关
        self.countdown_timer = None
        self.countdown_seconds = 0
        
        # 异步工作器
        self.worker_thread = None
        self.worker = None
        
        # 使用Qt默认样式

        # 当前验证码
        self.current_captcha = ""
        self.current_captcha_id = ""

        super().__init__("Order交易客户端 - 登录", parent)

        # 网络管理器（在super().__init__()之后初始化）
        self.network_manager = QNetworkAccessManager(self)
        
        # 设置窗口属性
        self.setFixedWidth(400)  # 只固定宽度，高度自适应
        self.setMinimumHeight(500)  # 设置最小高度
        self.setMaximumHeight(800)  # 设置最大高度，防止过高
        self.setModal(True)
        
        # 设置事件监听
        self.setup_events()

        # 使用Qt默认样式

        # 延迟调整窗口大小，确保所有组件都已初始化
        QTimer.singleShot(100, self.adjust_size_to_content)

        # 尝试自动登录
        QTimer.singleShot(500, self.try_auto_login)

        # 初始化验证码
        QTimer.singleShot(100, self.get_captcha_from_server)
    
    def create_content_widget(self):
        """创建内容组件"""
        widget = QLabel()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)  # 减少spacing以缩小组件间距
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 标题
        title_label = QLabel("Order交易客户端")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("titleLabel")
        
        # 登录表单
        self.login_widget = LoginWidget()
        self.login_widget.send_code_requested.connect(self.on_send_code_requested)
        self.login_widget.refresh_captcha_requested.connect(self.on_refresh_captcha_requested)
        
        # 状态指示器
        self.status_indicator = StatusIndicator()
        
        # 添加到布局
        layout.addWidget(title_label)
        layout.addWidget(self.login_widget)
        # layout.addWidget(self.status_indicator)
        # 移除addStretch()以减少按钮与上方控件的距离
        
        return widget
    
    def create_button_widget(self):
        """创建按钮区域"""
        widget = QWidget()  # 修复：使用QWidget而不是QLabel
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(20, 5, 20, 20)  # 减少上边距以缩小与上方控件的距离
        layout.setSpacing(10)
        
        self.login_button = QPushButton("登录")
        self.login_button.setObjectName("primaryButton")
        self.login_button.clicked.connect(self.on_login_clicked)
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.on_cancel_clicked)
        self.cancel_button.setObjectName("secondaryButton")
        
        layout.addWidget(self.login_button)
        layout.addStretch()
        layout.addWidget(self.cancel_button)

        
        return widget

    def adjust_size_to_content(self):
        """调整窗口大小以适应内容"""
        # 强制所有子组件更新布局
        self.updateGeometry()
        self.layout().activate()

        # 确保Tab组件也更新了布局
        if hasattr(self, 'login_widget') and self.login_widget:
            self.login_widget.updateGeometry()
            if hasattr(self.login_widget, 'tab_widget'):
                self.login_widget.tab_widget.updateGeometry()

        # 多次尝试获取准确的大小
        for _ in range(3):  # 尝试3次确保布局稳定
            self.layout().activate()
            QApplication.processEvents()  # 处理待处理的事件

        # 获取内容的推荐大小
        content_size = self.layout().sizeHint()

        # 如果sizeHint太小，使用widget的minimumSizeHint
        if content_size.height() < 300:
            content_size = self.minimumSizeHint()

        # 设置窗口高度为内容高度，但在最小和最大值范围内
        preferred_height = max(content_size.height() + 40, 400)  # 至少400px高度
        actual_height = max(self.minimumHeight(), min(preferred_height, self.maximumHeight()))

        print(f"调整窗口大小: content_size={content_size.height()}, preferred={preferred_height}, actual={actual_height}")

        # 调整窗口大小
        self.resize(400, actual_height)

    def show_centered(self):
        """显示窗口并居中"""
        # 先显示窗口，这样布局才能正确计算
        self.show()

        # 延迟调整大小，确保所有组件都已经渲染
        QTimer.singleShot(50, self._delayed_adjust_and_center)

    def _delayed_adjust_and_center(self):
        """延迟调整大小并居中"""
        # 调整大小以适应内容
        self.adjust_size_to_content()

        # 居中显示
        if self.parent():
            # 如果有父窗口，相对于父窗口居中
            parent_geometry = self.parent().geometry()
            x = parent_geometry.x() + (parent_geometry.width() - self.width()) // 2
            y = parent_geometry.y() + (parent_geometry.height() - self.height()) // 2
        else:
            # 相对于屏幕居中
            screen = QApplication.primaryScreen().geometry()
            x = (screen.width() - self.width()) // 2
            y = (screen.height() - self.height()) // 2

        self.move(x, y)

    def exec_centered(self):
        """以模态方式显示窗口并居中"""
        self.show_centered()
        return self.exec()

    def setup_events(self):
        """设置事件监听"""
        self.event_engine.register(EVENT_USER_LOGIN_SUCCESS, self.on_login_success_event)
        self.event_engine.register(EVENT_USER_LOGIN_FAILED, self.on_login_failed_event)
        self.event_engine.register(EVENT_SMS_CODE_SENT, self.on_sms_code_sent_event)
        self.event_engine.register(EVENT_SMS_CODE_SEND_FAILED, self.on_sms_code_send_failed_event)
    
    def on_send_code_requested(self, phone: str):
        """处理发送验证码请求"""
        if not phone:
            self.show_error_message("请输入手机号")
            return

        # 验证手机号格式
        if not self.login_manager.phone_validator.validate(phone):
            self.show_error_message("手机号格式不正确")
            return

        # 异步发送验证码
        self.send_sms_code_async(phone)

    def on_refresh_captcha_requested(self):
        """处理刷新验证码请求"""
        # 从服务器获取新验证码
        self.get_captcha_from_server()
    
    def on_login_clicked(self):
        """处理登录按钮点击"""
        try:
            credentials = self.login_widget.get_login_credentials()

            # 验证凭据
            error = credentials.validate()
            if error:
                self.show_error_message(error)
                return

            # 设置登录状态
            self.set_login_state(True)
            self.status_indicator.set_status("authenticating", "登录中...")

            # 异步执行登录
            if credentials.login_type == "password":
                self.login_async(credentials.username, credentials.password,
                               credentials.captcha, self.current_captcha_id, credentials.remember_me)
            else:
                self.login_phone_async(credentials.phone, credentials.sms_code, credentials.remember_me)

        except Exception as e:
            # 如果出现异常，确保重置登录状态
            self.set_login_state(False)
            self.status_indicator.set_status("error", "登录失败")
            self.show_error_message(f"登录异常: {str(e)}")
            self.logger.error(f"登录点击异常: {str(e)}")
    
    def on_cancel_clicked(self):
        """处理取消按钮点击"""
        self.login_cancelled.emit()
        self.reject()
    
    def login_async(self, username: str, password: str, captcha: str, captcha_id: str, remember_me: bool):
        """异步执行密码登录"""
        # 清理之前的线程
        self.cleanup_worker_thread()

        self.worker_thread = QThread()
        self.worker = AsyncLoginWorker(self.login_manager)
        self.worker.moveToThread(self.worker_thread)

        # 连接信号
        self.worker.login_finished.connect(self.on_login_finished)

        # 创建局部引用避免时序问题
        worker = self.worker
        self.worker_thread.started.connect(
            lambda: worker.login_with_password(username, password, captcha, captcha_id, remember_me)
        )
        self.worker_thread.finished.connect(self.cleanup_worker_thread)

        self.worker_thread.start()
    
    def login_phone_async(self, phone: str, sms_code: str, remember_me: bool):
        """异步执行手机登录"""
        # 清理之前的线程
        self.cleanup_worker_thread()

        self.worker_thread = QThread()
        self.worker = AsyncLoginWorker(self.login_manager)
        self.worker.moveToThread(self.worker_thread)

        # 连接信号
        self.worker.login_finished.connect(self.on_login_finished)

        # 创建局部引用避免时序问题
        worker = self.worker
        self.worker_thread.started.connect(
            lambda: worker.login_with_phone(phone, sms_code, remember_me)
        )
        self.worker_thread.finished.connect(self.cleanup_worker_thread)

        self.worker_thread.start()
    
    def send_sms_code_async(self, phone: str):
        """异步发送短信验证码"""
        self.login_widget.set_send_code_button_state(False, "发送中...")

        # 清理之前的线程
        self.cleanup_worker_thread()

        self.worker_thread = QThread()
        self.worker = AsyncLoginWorker(self.login_manager)
        self.worker.moveToThread(self.worker_thread)

        # 连接信号
        self.worker.sms_sent.connect(self.on_sms_sent)

        # 创建局部引用避免时序问题
        worker = self.worker
        self.worker_thread.started.connect(lambda: worker.send_sms_code(phone))
        self.worker_thread.finished.connect(self.cleanup_worker_thread)

        self.worker_thread.start()

    def cleanup_worker_thread(self):
        """清理工作线程"""
        if self.worker_thread and self.worker_thread.isRunning():
            self.worker_thread.quit()
            if not self.worker_thread.wait(2000):  # 等待最多2秒
                self.logger.warning("工作线程未能正常退出，强制终止")
                self.worker_thread.terminate()
                self.worker_thread.wait(1000)  # 再等待1秒确保终止

        if self.worker_thread:
            self.worker_thread.deleteLater()
            self.worker_thread = None

        if self.worker:
            self.worker.deleteLater()
            self.worker = None

    def get_captcha_from_server(self):
        """从服务器获取验证码"""
        try:
            # 构建请求URL
            url = f"{self.login_manager.auth_service.base_url}/base/captcha"
            request = QNetworkRequest(QUrl(url))
            request.setHeader(QNetworkRequest.ContentTypeHeader, "application/json")

            # 发送POST请求
            reply = self.network_manager.post(request, b'{}')
            reply.finished.connect(lambda: self.on_captcha_reply_finished(reply))

            self.logger.info("正在获取验证码...")

        except Exception as e:
            self.logger.error(f"获取验证码请求失败: {str(e)}")

    def on_captcha_reply_finished(self, reply: QNetworkReply):
        """处理验证码请求响应"""
        try:
            if reply.error() == QNetworkReply.NoError:
                # 读取响应数据
                data = reply.readAll().data()
                import json
                response_data = json.loads(data.decode('utf-8'))

                # 检查业务状态码
                if response_data.get("code") == 0:
                    captcha_data = response_data.get("data", {})
                    self.on_captcha_received(captcha_data)
                else:
                    error_msg = response_data.get("msg", "获取验证码失败")
                    self.logger.error(f"获取验证码失败: {error_msg}")
            else:
                self.logger.error(f"网络请求失败: {reply.errorString()}")

        except Exception as e:
            self.logger.error(f"处理验证码响应失败: {str(e)}")
        finally:
            reply.deleteLater()

    def on_captcha_received(self, captcha_data):
        """处理验证码接收"""
        if captcha_data:
            try:
                # 保存验证码ID
                self.current_captcha_id = captcha_data.get("captchaId", "")

                # 获取验证码图片路径
                pic_path = captcha_data.get("picPath", "")

                if pic_path:
                    # 直接加载base64图片数据
                    self.load_captcha_image(pic_path)
                else:
                    self.logger.warning("验证码图片路径为空")
                    self.generate_mock_captcha()
            except Exception as e:
                self.logger.error(f"处理验证码数据失败: {str(e)}")
                self.generate_mock_captcha()
        else:
            self.logger.warning("获取验证码失败，使用模拟验证码")
            self.generate_mock_captcha()

    def load_captcha_image(self, image_data: str):
        """加载验证码图片"""
        try:
            import base64

            # 处理base64图片数据
            if image_data.startswith("data:image/"):
                # 提取base64数据部分
                base64_data = image_data.split(",")[1]
                image_bytes = base64.b64decode(base64_data)

                pixmap = QPixmap()
                if pixmap.loadFromData(image_bytes):
                    self.login_widget.captcha_label.setPixmap(
                        pixmap.scaled(100, 35, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    )
                    self.logger.info("验证码图片加载成功")
                else:
                    self.logger.error("验证码图片数据无效")
                    self.generate_mock_captcha()
            else:
                self.logger.error("验证码图片格式不支持")
                self.generate_mock_captcha()
        except Exception as e:
            self.logger.error(f"加载验证码图片异常: {str(e)}")
            self.generate_mock_captcha()

    def try_auto_login(self):
        """尝试自动登录"""
        if not self.login_widget.auto_login_checkbox.isChecked():
            return
        
        self.status_indicator.set_status("connecting", "自动登录中...")

        # 清理之前的线程
        self.cleanup_worker_thread()

        self.worker_thread = QThread()
        self.worker = AsyncLoginWorker(self.login_manager)
        self.worker.moveToThread(self.worker_thread)

        # 连接信号
        self.worker.login_finished.connect(self.on_auto_login_finished)

        # 创建局部引用避免时序问题
        worker = self.worker
        self.worker_thread.started.connect(worker.auto_login)
        self.worker_thread.finished.connect(self.cleanup_worker_thread)

        self.worker_thread.start()
    
    def on_login_finished(self, result: LoginResult):
        """处理登录完成"""
        self.set_login_state(False)
        
        if result.success:
            self.status_indicator.set_status("authenticated", "登录成功")
            
            # 保存凭据
            credentials = self.login_widget.get_login_credentials()
            self.login_widget.save_credentials(credentials)
            
            # 发送成功信号
            self.login_success.emit(result)
            
            # 延迟关闭窗口
            QTimer.singleShot(1000, self.accept)
        else:
            self.status_indicator.set_status("error", "登录失败")
            self.show_error_message(result.message)
    
    def on_auto_login_finished(self, result: LoginResult):
        """处理自动登录完成"""
        if result.success:
            self.status_indicator.set_status("authenticated", "自动登录成功")
            self.login_success.emit(result)
            QTimer.singleShot(1000, self.accept)
        else:
            self.status_indicator.set_status("disconnected", "需要登录")
    
    def on_sms_sent(self, success: bool):
        """处理短信发送结果"""
        if success:
            self.show_info_message("验证码已发送")
            self.start_countdown_timer()
        else:
            self.show_error_message("发送验证码失败")
            self.login_widget.set_send_code_button_state(True, "获取验证码")
    
    def set_login_state(self, is_logging_in: bool):
        """设置登录状态"""
        self.login_button.setEnabled(not is_logging_in)
        self.login_button.setText("登录中..." if is_logging_in else "登录")
        self.login_widget.setEnabled(not is_logging_in)
    
    def start_countdown_timer(self):
        """启动验证码倒计时"""
        self.countdown_seconds = 60
        self.countdown_timer = QTimer()
        self.countdown_timer.timeout.connect(self.update_countdown)
        self.countdown_timer.start(1000)
        self.update_countdown()
    
    def update_countdown(self):
        """更新倒计时显示"""
        if self.countdown_seconds > 0:
            self.login_widget.set_send_code_button_state(
                False, f"重新发送({self.countdown_seconds})"
            )
            self.countdown_seconds -= 1
        else:
            self.countdown_timer.stop()
            self.login_widget.set_send_code_button_state(True, "获取验证码")
    
    def show_error_message(self, message: str):
        """显示错误消息"""
        QMessageBox.warning(self, "登录错误", message)
    
    def show_info_message(self, message: str):
        """显示信息消息"""
        QMessageBox.information(self, "提示", message)
    
    def on_login_success_event(self, event: Event):
        """处理登录成功事件"""
        self.logger.info("收到登录成功事件")
    
    def on_login_failed_event(self, event: Event):
        """处理登录失败事件"""
        self.logger.warning("收到登录失败事件")
    
    def on_sms_code_sent_event(self, event: Event):
        """处理验证码发送成功事件"""
        self.logger.info("收到验证码发送成功事件")
    
    def on_sms_code_send_failed_event(self, event: Event):
        """处理验证码发送失败事件"""
        self.logger.warning("收到验证码发送失败事件")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        # 停止倒计时器
        if self.countdown_timer:
            self.countdown_timer.stop()

        # 清理工作线程
        self.cleanup_worker_thread()

        super().closeEvent(event)

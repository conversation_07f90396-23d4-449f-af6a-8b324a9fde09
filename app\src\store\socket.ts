import { defineStore } from 'pinia'
import { ref, readonly } from 'vue'
import { useUserStore } from './user'
import { toast } from '@/utils/toast'

// 从env获取WebSocket URL，使用App端专用路径
const VITE_WS_URL = import.meta.env.VITE_WS_URL || 'ws://localhost:8888/ws/app'

// 消息信封接口，与文档对齐
interface IMessageEnvelope {
  event: string
  payload?: any
  seq?: number
  timestamp: number
}

export const useSocketStore = defineStore('socket', () => {
  const socket = ref<WebSocket | null>(null)
  const isConnected = ref(false)
  const isAuthenticated = ref(false)

  let heartbeatInterval: ReturnType<typeof setInterval> | null = null
  let reconnectTimeout: ReturnType<typeof setTimeout> | null = null
  let reconnectAttempts = 0

  // 事件处理器映射表
  const eventHandlers = new Map<string, Function[]>()

  /**
   * 注册事件处理器
   * @param eventType 事件类型
   * @param handler 处理器函数
   */
  function registerHandler(eventType: string, handler: Function) {
    if (!eventHandlers.has(eventType)) {
      eventHandlers.set(eventType, [])
    }
    const handlers = eventHandlers.get(eventType)!
    if (!handlers.includes(handler)) {
      handlers.push(handler)
      console.log(`[WebSocket] 注册事件处理器: ${eventType}`)
    }
  }

  /**
   * 取消注册事件处理器
   * @param eventType 事件类型
   * @param handler 处理器函数
   */
  function unregisterHandler(eventType: string, handler: Function) {
    const handlers = eventHandlers.get(eventType)
    if (handlers) {
      const index = handlers.indexOf(handler)
      if (index > -1) {
        handlers.splice(index, 1)
        console.log(`[WebSocket] 取消注册事件处理器: ${eventType}`)
        // 如果没有处理器了，删除整个条目
        if (handlers.length === 0) {
          eventHandlers.delete(eventType)
        }
      }
    }
  }

  /**
   * 建立 WebSocket 连接
   */
  function connect() {
    if (socket.value && socket.value.readyState === WebSocket.OPEN) {
      console.log('[WebSocket] 已连接，无需重复连接。')
      return
    }

    console.log(`[WebSocket] 正在连接到 ${VITE_WS_URL}...`)
    const ws = new WebSocket(VITE_WS_URL)

    ws.onopen = handleOpen
    ws.onmessage = handleMessage
    ws.onclose = handleClose
    ws.onerror = handleError

    socket.value = ws
  }

  /**
   * 断开 WebSocket 连接
   */
  function disconnect() {
    if (socket.value) {
      console.log('[WebSocket] 主动断开连接。')
      socket.value.close()
    }
    stopHeartbeat()
    if (reconnectTimeout) clearTimeout(reconnectTimeout)
  }

  /**
   * 发送消息
   * @param event 事件名
   * @param payload 数据
   */
  function sendMessage(event: string, payload?: object) {
    if (!socket.value || socket.value.readyState !== WebSocket.OPEN) {
      toast.error('连接已断开，消息发送失败')
      return
    }

    const message: IMessageEnvelope = {
      event,
      payload,
      timestamp: Date.now(),
    }

    console.log('[WebSocket] 发送消息:', message)
    socket.value.send(JSON.stringify(message))
  }

  // --- 私有辅助函数 ---

  function handleOpen() {
    console.log('[WebSocket] 连接成功！')
    isConnected.value = true
    reconnectAttempts = 0
    if (reconnectTimeout) clearTimeout(reconnectTimeout)

    startHeartbeat()

    // 如果用户已登录，则自动进行认证
    const userStore = useUserStore()
    if (userStore.isLoggedIn) {
      authenticate(userStore.token)
    }
  }

  async function handleMessage(event: MessageEvent) {
    try {
      const message: IMessageEnvelope = JSON.parse(event.data)
      // Don't log every pong message to avoid spamming the console
      if (message.event !== 'pong') {
        console.log('[WebSocket] 收到消息:', message)
      }

      // 首先处理内置事件
      switch (message.event) {
        case 'pong':
          if (message.payload?.timestamp) {
            const rtt = Date.now() - message.payload.timestamp
            console.log(`[WebSocket] Pong received. RTT: ${rtt}ms`)
          }
          break
        case 'auth_response':
          if (message.payload?.success) {
            isAuthenticated.value = true
            console.log('[WebSocket] 认证成功。')
          } else {
            toast.error(`认证失败: ${message.payload?.message}`)
          }
          break
        case 'notification':
          toast.info(message.payload?.content || '收到一条新通知')
          break
        case 'trade_update':
          // TODO: 更新交易列表或详情
          console.log('[WebSocket] 交易更新:', message.payload)
          toast.success('您的交易状态已更新')
          break
      }

      // 然后调用已注册的事件处理器
      const handlers = eventHandlers.get(message.event)
      if (handlers && handlers.length > 0) {
        handlers.forEach(handler => {
          try {
            handler(message.payload)
          } catch (error) {
            console.error(`[WebSocket] 事件处理器执行失败 (${message.event}):`, error)
          }
        })
      }
    } catch (error) {
      console.error('[WebSocket] 解析消息失败:', error)
    }
  }

  function handleClose() {
    console.log('[WebSocket] 连接已关闭。')
    isConnected.value = false
    isAuthenticated.value = false
    stopHeartbeat()
    attemptReconnect()
  }

  function handleError(event: Event) {
    console.error('[WebSocket] 发生错误:', event)
  }

  function startHeartbeat() {
    stopHeartbeat()
    heartbeatInterval = setInterval(() => {
      // Send ping with a timestamp payload for RTT calculation
      sendMessage('ping', { timestamp: Date.now() })
    }, 25000)
  }

  function stopHeartbeat() {
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval)
      heartbeatInterval = null
    }
  }

  function attemptReconnect() {
    if (reconnectAttempts >= 5) {
      console.log('[WebSocket] 重连次数过多，已停止。')
      return
    }
    const delay = Math.pow(2, reconnectAttempts) * 1000
    reconnectAttempts++

    console.log(`[WebSocket] 将在 ${delay / 1000} 秒后尝试重新连接...`)
    reconnectTimeout = setTimeout(() => {
      connect()
    }, delay)
  }

  // --- 公开的动作 --- 

  /**
   * 使用Token进行身份认证
   * @param token JWT Token
   */
  function authenticate(token: string) {
    sendMessage('auth', { token })
  }

  /**
   * 订阅频道
   * @param channel 频道名称，例如 'contract:**********-001'
   */
  function subscribe(channel: string) {
    sendMessage('subscribe', { channel })
  }

  return {
    isConnected: readonly(isConnected),
    isAuthenticated: readonly(isAuthenticated),
    connect,
    disconnect,
    sendMessage,
    authenticate,
    subscribe,
    registerHandler,
    unregisterHandler,
  }
})

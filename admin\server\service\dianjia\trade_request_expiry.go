package dianjia

import (
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/dianjia"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type TradeRequestExpiryService struct{}

// ProcessExpiredTradeRequests 处理过期的交易请求 (V4)
// 这个函数应该被定时任务调用，例如每小时执行一次
func (s *TradeRequestExpiryService) ProcessExpiredTradeRequests() error {
	global.GVA_LOG.Info("开始处理过期的交易请求")

	// 开启数据库事务
	tx := global.GVA_DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := tx.Error; err != nil {
		return err
	}

	// 查询所有执行中且已过期的交易请求
	var expiredRequests []dianjia.TradeRequest
	now := time.Now()

	err := tx.Where("status = ? AND expires_at < ?",
		dianjia.TradeRequestStatusExecuting,
		now,
	).Find(&expiredRequests).Error

	if err != nil {
		tx.Rollback()
		return fmt.Errorf("查询过期交易请求失败: %v", err)
	}

	if len(expiredRequests) == 0 {
		tx.Commit()
		global.GVA_LOG.Info("没有找到过期的交易请求")
		return nil
	}

	global.GVA_LOG.Info(fmt.Sprintf("找到 %d 个过期的交易请求", len(expiredRequests)))

	// 处理每个过期的请求
	tradeRequestService := &TradeRequestService{}
	processedCount := 0

	for _, request := range expiredRequests {
		err := s.processExpiredRequest(tx, &request, tradeRequestService)
		if err != nil {
			global.GVA_LOG.Error(fmt.Sprintf("处理过期请求 %d 失败", request.ID), zap.Error(err))
			continue
		}
		processedCount++
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交事务失败: %v", err)
	}

	global.GVA_LOG.Info(fmt.Sprintf("成功处理了 %d 个过期的交易请求", processedCount))
	return nil
}

// processExpiredRequest 处理单个过期的交易请求
func (s *TradeRequestExpiryService) processExpiredRequest(tx *gorm.DB, request *dianjia.TradeRequest, tradeRequestService *TradeRequestService) error {
	// 解冻关联的合同数量
	err := tradeRequestService.unfreezeContractsForTradeRequestV4(tx, request)
	if err != nil {
		return fmt.Errorf("解冻合同失败: %v", err)
	}

	// 更新请求状态为过期
	request.Status = dianjia.TradeRequestStatusExpired
	if err := tx.Save(request).Error; err != nil {
		return fmt.Errorf("更新请求状态失败: %v", err)
	}

	global.GVA_LOG.Info(fmt.Sprintf("交易请求 %d 已标记为过期", request.ID))
	return nil
}

// GetExpiringTradeRequests 获取即将过期的交易请求 (用于提醒)
func (s *TradeRequestExpiryService) GetExpiringTradeRequests(hoursBeforeExpiry int) ([]dianjia.TradeRequest, error) {
	var expiringRequests []dianjia.TradeRequest

	// 计算提醒时间点
	reminderTime := time.Now().Add(time.Duration(hoursBeforeExpiry) * time.Hour)

	err := global.GVA_DB.Where("status = ? AND expires_at <= ? AND expires_at > ?",
		dianjia.TradeRequestStatusExecuting,
		reminderTime,
		time.Now(),
	).Preload("Pricer").Preload("Setter").Preload("Instrument").Find(&expiringRequests).Error

	if err != nil {
		return nil, fmt.Errorf("查询即将过期的交易请求失败: %v", err)
	}

	return expiringRequests, nil
}

// StartExpiryScheduler 启动过期处理调度器
// 这个函数应该在应用启动时调用
func (s *TradeRequestExpiryService) StartExpiryScheduler() {
	// 使用 Go 协程定期执行过期处理
	go func() {
		ticker := time.NewTicker(1 * time.Hour) // 每小时执行一次
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				err := s.ProcessExpiredTradeRequests()
				if err != nil {
					global.GVA_LOG.Error("处理过期交易请求失败", zap.Error(err))
				}
			}
		}
	}()

	global.GVA_LOG.Info("交易请求过期处理调度器已启动")
}

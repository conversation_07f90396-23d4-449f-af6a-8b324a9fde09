<template>
  <view class="contract-card" @click="handleClick">
    <!-- 合同头部 -->
    <view class="contract-header">
      <view class="header-main">
        <text class="contract-code">{{ contract.contractCode }}</text>
        <view class="header-tags">
          <wd-tag :type="getStatusType(contract.status)" size="small">
            {{ getStatusText(contract.status) }}
          </wd-tag>
          <wd-tag :type="getContractTypeTagType(contract.priceType)" size="small">
            {{ getContractTypeLabel(contract.priceType) }}
          </wd-tag>
          <wd-tag v-if="contract.frozenQuantity > 0" type="warning" size="small">
            冻结{{ contract.frozenQuantity }}
          </wd-tag>
        </view>
      </view>
    </view>

    <!-- 合同信息 -->
    <view class="contract-info">
      <!-- 用户和合约信息 -->
      <view class="user-contract-info">
        <text class="user-name">{{ getUserDisplayName() }}</text>
        <text class="instrument-name">{{ contract.instrument?.instrument_name || `合约${contract.instrumentRefID}` }}</text>
        <text class="create-time">{{ formatDate(contract.CreatedAt) }}</text>
      </view>

      <!-- 价格和数量信息 -->
      <view class="price-quantity-section">
        <view class="price-section">
          <text class="price-value">{{ getPriceDisplay() }}</text>
        </view>
        <view class="quantity-section">
          <view class="quantity-row">
            <view class="quantity-item">
              <text class="quantity-number">{{ contract.totalQuantity }}</text>
              <text class="quantity-label">总量</text>
            </view>
            <view class="quantity-item">
              <text class="quantity-number">{{ contract.remainingQuantity }}</text>
              <text class="quantity-label">剩余</text>
            </view>
            <view class="quantity-item">
              <text class="quantity-number" :class="{ 'warning': contract.frozenQuantity > 0 }">{{ contract.frozenQuantity }}</text>
              <text class="quantity-label" :class="{ 'warning': contract.frozenQuantity > 0 }">冻结</text>
            </view>
            <view class="quantity-item">
              <text class="quantity-number">{{ contract.remainingQuantity - contract.frozenQuantity }}</text>
              <text class="quantity-label">可用</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 备注信息 -->
      <view v-if="contract.remarks" class="remarks-section">
        <text class="remarks-text">{{ contract.remarks }}</text>
      </view>
    </view>

    <!-- 插槽：用于放置操作按钮等自定义内容 -->
    <slot name="actions" :contract="contract"></slot>
  </view>
</template>

<script setup lang="ts">
import type { IContract, ContractPriceType, ContractStatus } from '@/types/contract'
import type { TagType } from 'wot-design-uni/components/wd-tag/types'

interface Props {
  contract: IContract
  userRole: 'setter' | 'pricer' // 用户角色，决定显示哪个用户名
}

interface Emits {
  (e: 'click', contract: IContract): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 处理点击事件
function handleClick() {
  emit('click', props.contract)
}

// 获取要显示的用户名
function getUserDisplayName(): string {
  if (props.userRole === 'setter') {
    // setter角色查看时显示pricer名称
    return props.contract.pricer?.nickName || props.contract.pricer?.userName || `用户${props.contract.pricerID}`
  } else {
    // pricer角色查看时显示setter名称
    return props.contract.setter?.nickName || props.contract.setter?.userName || `用户${props.contract.setterID}`
  }
}

// 获取价格显示
function getPriceDisplay(): string {
  if (props.contract.priceType === 'basis') {
    return `基差 ${props.contract.priceValue > 0 ? '+' : ''}${props.contract.priceValue}`
  } else {
    return `固定价 ${props.contract.priceValue}`
  }
}

// 获取状态类型
function getStatusType(status: ContractStatus): TagType {
  const typeMap: Record<ContractStatus, TagType> = {
    'Unexecuted': 'warning',
    'Executing': 'success',
    'Pending': 'warning',
    'Completed': 'primary',
    'Cancelled': 'danger'
  }
  return typeMap[status] || 'primary'
}

// 获取状态文本
function getStatusText(status: ContractStatus): string {
  const textMap: Record<ContractStatus, string> = {
    'Unexecuted': '未执行',
    'Executing': '执行中',
    'Pending': '待处理',
    'Completed': '已完成',
    'Cancelled': '已取消'
  }
  return textMap[status] || status
}

// 获取合同类型标签类型
function getContractTypeTagType(priceType: ContractPriceType): TagType {
  return priceType === 'basis' ? 'primary' : 'success'
}

// 获取合同类型标签文本
function getContractTypeLabel(priceType: ContractPriceType): string {
  return priceType === 'basis' ? '基差' : '固定价'
}

// 格式化日期
function formatDate(dateStr: string): string {
  const date = new Date(dateStr)
  return `${date.getMonth() + 1}-${date.getDate()}`
}
</script>

<style lang="scss" scoped>
.contract-card {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4rpx;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  }

  &:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  }
}

.contract-header {
  margin-bottom: 20rpx;

  .header-main {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 12rpx;

    .contract-code {
      font-size: 32rpx;
      font-weight: 700;
      color: #1a1a1a;
      line-height: 1.2;
      flex-shrink: 0;
    }

    .header-tags {
      display: flex;
      gap: 8rpx;
      flex-wrap: wrap;
      align-items: center;
      justify-content: flex-end;
    }
  }
}

.contract-info {
  margin-bottom: 20rpx;

  .user-contract-info {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
    gap: 16rpx;

    .user-name {
      font-size: 28rpx;
      color: #1a1a1a;
      font-weight: 600;
      flex: 1;
      min-width: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .instrument-name {
      font-size: 24rpx;
      color: #666;
      flex-shrink: 0;
      max-width: 200rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .create-time {
      font-size: 24rpx;
      color: #999;
      flex-shrink: 0;
    }
  }

  .price-quantity-section {
    display: flex;
    align-items: center;
    gap: 16rpx;
    margin-bottom: 16rpx;

    .price-section {
      flex: 1;
      .price-value {
        font-size: 32rpx;
        color: #e6a23c;
        font-weight: 700;
        background: linear-gradient(135deg, rgba(230, 162, 60, 0.1) 0%, rgba(230, 162, 60, 0.05) 100%);
        padding: 12rpx 16rpx;
        border-radius: 8rpx;
        display: block;
        box-shadow: 0 2rpx 6rpx rgba(230, 162, 60, 0.15);
        text-align: center;
      }
    }

    .quantity-section {
      flex: 1;
      .quantity-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12rpx 16rpx;
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border-radius: 8rpx;
        border: 1rpx solid rgba(0, 0, 0, 0.05);

        .quantity-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          flex: 1;
          padding: 6rpx 2rpx;
          border-radius: 4rpx;
          transition: all 0.2s ease;

          &:hover {
            background: rgba(0, 0, 0, 0.02);
          }

          .quantity-number {
            font-size: 28rpx;
            color: #1a1a1a;
            font-weight: 700;
            margin-bottom: 2rpx;
            line-height: 1.2;

            &.warning {
              color: #f56c6c;
            }
          }

          .quantity-label {
            font-size: 20rpx;
            color: #666;
            font-weight: 500;
            line-height: 1.2;

            &.warning {
              color: #f56c6c;
            }
          }
        }
      }
    }
  }

  .remarks-section {
    margin-top: 8rpx;
    padding: 8rpx 12rpx;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 6rpx;
    border-left: 3rpx solid #667eea;

    .remarks-text {
      font-size: 22rpx;
      color: #666;
      line-height: 1.3;
      font-style: italic;
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

// 操作按钮样式（通过插槽使用）
:deep(.contract-actions) {
  display: flex;
  gap: 12rpx;
  justify-content: flex-end;
  margin-top: 20rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);

  .wd-button {
    border-radius: 8rpx;
    font-weight: 500;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1rpx);
    }

    &:active {
      transform: translateY(0);
    }
  }
}
</style>

#!/usr/bin/env python3
"""
全局样式系统测试脚本

测试全局样式管理器是否正确覆盖所有窗口样式
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, 
    QPushButton, QLabel, QLineEdit, QMenuBar, QStatusBar, QToolBar,
    QTableWidget, QTableWidgetItem, QTabWidget, QTextEdit, QComboBox,
    QCheckBox, QRadioButton, QSlider, QProgressBar, QGroupBox,
    QListWidget, QTreeWidget, QTreeWidgetItem, QSplitter, QScrollArea
)
from PySide6.QtCore import Qt, QTimer

# 设置日志
logging.basicConfig(level=logging.INFO)

def test_global_styles():
    """测试全局样式系统"""
    try:
        # 创建应用
        app = QApplication(sys.argv)
        
        # 导入全局样式管理器
        from ui.styles.global_styles import GlobalStyleManager
        
        # 应用全局样式
        global_style_manager = GlobalStyleManager()
        global_styles = global_style_manager.get_complete_global_style()
        app.setStyleSheet(global_styles)
        
        print("✅ 全局样式已应用到整个应用程序")
        
        # 创建主窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("全局样式测试 - 主窗口")
        main_window.resize(1000, 700)
        
        # 创建中央widget
        central_widget = QWidget()
        main_layout = QVBoxLayout(central_widget)
        
        # 创建标题
        title_label = QLabel("全局样式系统测试")
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; margin: 10px;")
        main_layout.addWidget(title_label)
        
        # 创建Tab组件测试各种控件
        tab_widget = QTabWidget()
        
        # Tab 1: 基础控件
        basic_tab = QWidget()
        basic_layout = QVBoxLayout(basic_tab)
        
        # 按钮测试
        button_group = QGroupBox("按钮测试")
        button_layout = QHBoxLayout(button_group)
        button_layout.addWidget(QPushButton("普通按钮"))
        button_layout.addWidget(QPushButton("悬停测试"))
        disabled_btn = QPushButton("禁用按钮")
        disabled_btn.setEnabled(False)
        button_layout.addWidget(disabled_btn)
        basic_layout.addWidget(button_group)
        
        # 输入框测试
        input_group = QGroupBox("输入框测试")
        input_layout = QVBoxLayout(input_group)
        input_layout.addWidget(QLineEdit("普通输入框"))
        input_layout.addWidget(QTextEdit("多行文本编辑器\n支持多行输入"))
        combo = QComboBox()
        combo.addItems(["选项1", "选项2", "选项3"])
        input_layout.addWidget(combo)
        basic_layout.addWidget(input_group)
        
        # 选择控件测试
        choice_group = QGroupBox("选择控件测试")
        choice_layout = QVBoxLayout(choice_group)
        choice_layout.addWidget(QCheckBox("复选框选项"))
        choice_layout.addWidget(QRadioButton("单选框选项"))
        slider = QSlider(Qt.Horizontal)
        slider.setValue(50)
        choice_layout.addWidget(slider)
        progress = QProgressBar()
        progress.setValue(75)
        choice_layout.addWidget(progress)
        basic_layout.addWidget(choice_group)
        
        tab_widget.addTab(basic_tab, "基础控件")
        
        # Tab 2: 表格和列表
        table_tab = QWidget()
        table_layout = QVBoxLayout(table_tab)
        
        # 表格测试
        table = QTableWidget(5, 3)
        table.setHorizontalHeaderLabels(["列1", "列2", "列3"])
        for i in range(5):
            for j in range(3):
                table.setItem(i, j, QTableWidgetItem(f"数据 {i+1}-{j+1}"))
        table_layout.addWidget(table)
        
        # 列表测试
        list_widget = QListWidget()
        for i in range(10):
            list_widget.addItem(f"列表项 {i+1}")
        table_layout.addWidget(list_widget)
        
        tab_widget.addTab(table_tab, "表格列表")
        
        # Tab 3: 树形和分割器
        tree_tab = QWidget()
        tree_layout = QVBoxLayout(tree_tab)
        
        # 分割器测试
        splitter = QSplitter(Qt.Horizontal)
        
        # 树形控件
        tree = QTreeWidget()
        tree.setHeaderLabels(["项目", "值"])
        root = QTreeWidgetItem(tree, ["根节点", "根值"])
        for i in range(3):
            child = QTreeWidgetItem(root, [f"子节点 {i+1}", f"子值 {i+1}"])
            for j in range(2):
                QTreeWidgetItem(child, [f"孙节点 {i+1}-{j+1}", f"孙值 {i+1}-{j+1}"])
        tree.expandAll()
        splitter.addWidget(tree)
        
        # 滚动区域测试
        scroll_area = QScrollArea()
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        for i in range(20):
            scroll_layout.addWidget(QLabel(f"滚动内容 {i+1}"))
        scroll_area.setWidget(scroll_content)
        splitter.addWidget(scroll_area)
        
        tree_layout.addWidget(splitter)
        tab_widget.addTab(tree_tab, "树形分割")
        
        main_layout.addWidget(tab_widget)
        
        # 设置中央widget
        main_window.setCentralWidget(central_widget)
        
        # 添加菜单栏
        menu_bar = main_window.menuBar()
        file_menu = menu_bar.addMenu("文件")
        file_menu.addAction("新建")
        file_menu.addAction("打开")
        file_menu.addSeparator()
        file_menu.addAction("退出")
        
        edit_menu = menu_bar.addMenu("编辑")
        edit_menu.addAction("复制")
        edit_menu.addAction("粘贴")
        
        view_menu = menu_bar.addMenu("视图")
        view_menu.addAction("全屏")
        view_menu.addAction("缩放")
        
        # 添加工具栏
        tool_bar = main_window.addToolBar("主工具栏")
        tool_bar.addAction("新建")
        tool_bar.addAction("保存")
        tool_bar.addSeparator()
        tool_bar.addAction("设置")
        
        # 添加状态栏
        status_bar = main_window.statusBar()
        status_bar.showMessage("全局样式测试就绪 - 所有控件使用统一样式")
        
        # 显示主窗口
        main_window.show()
        
        # 创建登录窗口测试
        def show_login_test():
            try:
                from vnpy.event import EventEngine
                from ui.login.login_window import LoginWindow
                
                event_engine = EventEngine()
                login_window = LoginWindow(event_engine, None, None)
                login_window.show_centered()
                print("✅ 登录窗口已显示，使用全局样式")
            except Exception as e:
                print(f"❌ 登录窗口测试失败：{str(e)}")
        
        # 添加测试按钮
        test_login_btn = QPushButton("测试登录窗口样式")
        test_login_btn.clicked.connect(show_login_test)
        main_layout.addWidget(test_login_btn)
        
        print("=" * 60)
        print("全局样式系统测试启动")
        print("=" * 60)
        print("测试内容:")
        print("1. 主窗口使用全局样式")
        print("2. 所有基础控件使用统一样式")
        print("3. 表格、列表、树形控件样式")
        print("4. 菜单栏、工具栏、状态栏样式")
        print("5. 登录窗口样式一致性")
        print()
        print("观察要点:")
        print("- 所有控件使用统一的颜色方案")
        print("- 现代化的圆角设计")
        print("- 一致的字体和间距")
        print("- 悬停和焦点效果")
        print("- 与vnpy默认样式的对比")
        print("=" * 60)
        
        # 运行应用
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_global_styles()

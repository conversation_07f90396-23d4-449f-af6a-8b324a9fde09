<script lang="ts" setup>
import { ref, computed, onMounted, watch } from 'vue'
import type { 
  IInstrumentSelectItem, 
  IInstrumentSelectorProps, 
  IInstrumentSelectorEmits 
} from '@/types'
import { getInstrumentSelectList } from '@/api/instrument'
import { ExchangeMap } from '@/types/instrument'

// 组件定义
defineOptions({
  name: 'InstrumentSelector'
})

const props = defineProps({
  modelValue: {
    type: Number,
    default: 0
  },
  label: {
    type: String,
    default: '期货合约'
  },
  placeholder: {
    type: String,
    default: '请选择期货合约'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: true
  },
  customClass: {
    type: String,
    default: ''
  },
  customLabelClass: {
    type: String,
    default: ''
  },
  customValueClass: {
    type: String,
    default: ''
  }
})

// Emits 定义
const emit = defineEmits<{
  'update:modelValue': [value: number]
  'change': [value: number, instrumentData?: IInstrumentSelectItem | null]
}>()

// 状态管理
const showModal = ref(false)
const isLoading = ref(false)
const searchKeyword = ref('')
const selectedInstrument = ref<IInstrumentSelectItem | null>(null)

// 选择步骤状态
const currentStep = ref(0) // 0: 交易所, 1: 商品, 2: 合约
const selectedExchange = ref('')
const selectedProduct = ref('')

// 缓存数据结构
interface GroupedData {
  exchanges: {
    value: string
    label: string
  }[]
  productsByExchange: Record<string, {
    value: string
    label: string
  }[]>
  contractsByProduct: Record<string, {
    value: string
    label: string
    instrumentData: IInstrumentSelectItem
  }[]>
  instrumentsById: Record<number, IInstrumentSelectItem>
}

// 缓存数据
const cachedData = ref<GroupedData>({
  exchanges: [],
  productsByExchange: {},
  contractsByProduct: {},
  instrumentsById: {}
})

// 当前显示的列表数据
const currentList = computed(() => {
  if (currentStep.value === 0) {
    // 显示交易所列表
    return cachedData.value.exchanges.filter(exchange => 
      !searchKeyword.value || 
      exchange.label.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  } else if (currentStep.value === 1) {
    // 显示商品列表
    const products = cachedData.value.productsByExchange[selectedExchange.value] || []
    return products.filter(product => 
      !searchKeyword.value || 
      product.label.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  } else {
    // 显示合约列表
    const key = `${selectedExchange.value}_${selectedProduct.value}`
    const contracts = cachedData.value.contractsByProduct[key] || []
    return contracts.filter(contract => 
      !searchKeyword.value || 
      contract.label.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }
})

// 标题文本
const modalTitle = computed(() => {
  if (currentStep.value === 0) return '选择交易所'
  if (currentStep.value === 1) return '选择商品'
  return '选择合约'
})

// 面包屑导航
const breadcrumb = computed(() => {
  const items = []
  if (selectedExchange.value) {
    items.push(ExchangeMap[selectedExchange.value] || selectedExchange.value)
  }
  if (selectedProduct.value) {
    items.push(selectedProduct.value)
  }
  return items.join(' > ')
})

// 监听modelValue变化，查找对应的instrument并更新显示
watch(() => props.modelValue, (newId) => {
  if (newId && cachedData.value.instrumentsById[newId]) {
    selectedInstrument.value = cachedData.value.instrumentsById[newId]
  } else if (!newId) {
    selectedInstrument.value = null
  }
}, { immediate: true })

// 初始化加载全部数据
async function initializeData() {
  try {
    isLoading.value = true

    // 1. 获取交易所列表
    cachedData.value.exchanges = Object.entries(ExchangeMap).map(([value, label]) => ({
      value,
      label
    }))

    // 2. 获取所有合约数据
    const instrumentResponse = await getInstrumentSelectList()
    const instruments = instrumentResponse.data || []

    if (instruments.length === 0) {
      uni.showToast({ title: '未获取到合约数据', icon: 'none' })
      return
    }

    // 3. 从合约数据中提取商品信息，按交易所分组
    const productsByExchange: Record<string, any[]> = {}
    const productSet = new Map<string, string>()
    const instrumentsById: Record<number, IInstrumentSelectItem> = {}

    for (const instrument of instruments) {
      // 建立ID到instrument的映射
      instrumentsById[instrument.id] = instrument
      
      const productKey = `${instrument.exchange_id}_${instrument.product_name}`
      if (!productSet.has(productKey)) {
        productSet.set(productKey, instrument.product_name)

        if (!productsByExchange[instrument.exchange_id]) {
          productsByExchange[instrument.exchange_id] = []
        }

        productsByExchange[instrument.exchange_id].push({
          value: instrument.product_name,
          label: instrument.product_name
        })
      }
    }
    cachedData.value.productsByExchange = productsByExchange
    cachedData.value.instrumentsById = instrumentsById

    // 4. 按商品分组合约
    const contractsByProduct: Record<string, any[]> = {}
    for (const instrument of instruments) {
      const key = `${instrument.exchange_id}_${instrument.product_name}`
      if (!contractsByProduct[key]) {
        contractsByProduct[key] = []
      }

      contractsByProduct[key].push({
        value: instrument.instrument_id,
        label: instrument.instrument_id,
        instrumentData: instrument
      })
    }
    cachedData.value.contractsByProduct = contractsByProduct
    
    // 如果有初始值，查找对应的instrument并设置显示值
    if (props.modelValue && cachedData.value.instrumentsById[props.modelValue]) {
      selectedInstrument.value = cachedData.value.instrumentsById[props.modelValue]
    }
  } catch (error) {
    uni.showToast({ title: '数据加载失败，请重试', icon: 'error' })
    throw error
  } finally {
    isLoading.value = false
  }
}

// 显示选择器
function showInstrumentPicker() {
  resetSelection()
  showModal.value = true
}

// 重置选择状态
function resetSelection() {
  currentStep.value = 0
  selectedExchange.value = ''
  selectedProduct.value = ''
  searchKeyword.value = ''
}

// 搜索
function searchInstruments() {
  // 搜索逻辑已经在 currentList 计算属性中处理
}

// 处理列表项点击
function handleItemClick(item: any) {
  if (currentStep.value === 0) {
    // 选择交易所
    selectedExchange.value = item.value
    currentStep.value = 1
    searchKeyword.value = ''
  } else if (currentStep.value === 1) {
    // 选择商品
    selectedProduct.value = item.value
    currentStep.value = 2
    searchKeyword.value = ''
  } else {
    // 选择合约
    confirmInstrumentSelection(item.instrumentData)
  }
}

// 确认选择
function confirmInstrumentSelection(instrument: IInstrumentSelectItem) {
  selectedInstrument.value = instrument
  showModal.value = false
  emit('update:modelValue', instrument.id)
  emit('change', instrument.id, instrument)
  resetSelection()
}

// 返回上一步
function goBack() {
  if (currentStep.value > 0) {
    currentStep.value--
    searchKeyword.value = ''
    if (currentStep.value === 0) {
      selectedExchange.value = ''
      selectedProduct.value = ''
    } else if (currentStep.value === 1) {
      selectedProduct.value = ''
    }
  }
}

// 移除选择
function removeInstrument() {
  selectedInstrument.value = null
  emit('update:modelValue', 0)
  emit('change', 0, null)
}

// 显示文本
const displayValue = computed(() => {
  if (selectedInstrument.value) {
    return `${ExchangeMap[selectedInstrument.value.exchange_id] || selectedInstrument.value.exchange_id}.${selectedInstrument.value.instrument_id}`
  }
  return ''
})

// 组件挂载时初始化数据
onMounted(async () => {
  try {
    await initializeData()
  } catch (error) {
    console.error('数据初始化失败:', error)
    uni.showModal({
      title: '加载失败',
      content: '数据加载失败，请检查网络连接后重试',
      showCancel: true,
      confirmText: '重试',
      success: (res) => {
        if (res.confirm) {
          initializeData()
        }
      }
    })
  }
})
</script>

<template>
  <view :class="['instrument-selector-compact', customClass]">
    <view class="instrument-content">
      <view v-if="!selectedInstrument" class="empty-instrument-compact">
        {{ placeholder }}
      </view>
      <view v-else class="selected-instrument-compact">
        <text class="instrument-name">
          {{ displayValue }}
        </text>
        <!-- <text class="instrument-product">
          {{ selectedInstrument.product_name }}
        </text> -->
      </view>
    </view>
    <wd-button 
      v-if="!selectedInstrument" 
      type="primary" 
      size="small" 
      custom-class="dj-btn-primary" 
      :disabled="disabled"
      @click="showInstrumentPicker"
    >
      选择
    </wd-button>
    <wd-button 
      v-else 
      type="error" 
      size="small" 
      custom-class="dj-btn-danger"
      :disabled="disabled"
      @click="removeInstrument"
    >
      移除
    </wd-button>

    <!-- 期货合约选择弹窗 -->
    <wd-popup v-model="showModal" position="bottom" custom-style="height: 80%" custom-class="dj-popup">
      <view class="instrument-picker">
        <view class="picker-header">
          <view class="header-content">
            <wd-button 
              v-if="currentStep > 0" 
              type="info" 
              size="small" 
              custom-class="back-btn"
              @click="goBack"
            >
              返回
            </wd-button>
            <view class="title-section">
              <text class="picker-title">
                {{ modalTitle }}
              </text>
              <text v-if="breadcrumb" class="breadcrumb">
                {{ breadcrumb }}
              </text>
            </view>
          </view>
        </view>

        <view class="picker-content">
          <wd-search 
            v-model="searchKeyword" 
            :placeholder="`搜索${modalTitle.replace('选择', '')}`" 
            custom-class="dj-search" 
            @search="searchInstruments" 
          />

          <view class="instrument-list">
            <view 
              v-for="item in currentList" 
              :key="item.value" 
              class="instrument-item" 
              @click="handleItemClick(item)"
            >
              <view class="item-info">
                <text class="item-name">
                  {{ item.label }}
                </text>
                <text v-if="currentStep === 2" class="item-product">
                  {{ item.instrumentData?.product_name }}
                </text>
              </view>
              <view class="item-arrow">
                <text v-if="currentStep < 2">></text>
                <wd-radio v-else :value="selectedInstrument?.id === item.instrumentData?.id" custom-class="dj-radio" />
              </view>
            </view>
          </view>

          <view v-if="currentList.length === 0 && !isLoading" class="empty-list">
            <text>暂无数据</text>
          </view>

          <view v-if="isLoading" class="loading-indicator">
            <text>加载中...</text>
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<style lang="scss" scoped>
// 基础变量
$primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$primary-color: #667eea;
$secondary-color: #764ba2;
$text-primary: #303133;
$text-secondary: #606266;
$text-light: #909399;
$font-size-large: 32rpx;
$font-size-medium: 28rpx;
$font-size-title: 36rpx;
$box-shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
$transition-base: all 0.3s ease;

// 期货合约选择器
.instrument-selector-compact {
  display: flex;
  align-items: center;
  gap: 20rpx;
  background-color: #f9fafc;
  padding: 24rpx;
  border-radius: 8rpx;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4rpx;
    background: linear-gradient(180deg, $secondary-color, $primary-color);
  }

  .instrument-content {
    flex: 1;

    .empty-instrument-compact {
      color: $text-light;
      font-size: $font-size-medium;
      font-style: italic;
    }

    .selected-instrument-compact {
      display: flex;
      flex-direction: column;
      gap: 8rpx;
      background-color: rgba(102, 126, 234, 0.05);
      padding: 12rpx 16rpx;
      border-radius: 8rpx;

      .instrument-name {
        font-size: $font-size-medium;
        color: $text-primary;
        font-weight: 500;
      }

      .instrument-product {
        font-size: $font-size-medium;
        color: $text-secondary;
      }
    }
  }
}

// 弹窗选择器
.instrument-picker {
  height: 100%;
  display: flex;
  flex-direction: column;

  .picker-header {
    background: $primary-gradient;
    padding: 30rpx 20rpx;

    .header-content {
      display: flex;
      align-items: center;
      gap: 20rpx;

      .title-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8rpx;

        .picker-title {
          font-size: $font-size-title;
          font-weight: bold;
          color: #ffffff;
          text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
        }

        .breadcrumb {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }

  .picker-content {
    flex: 1;
    padding: 20rpx;
    overflow-y: auto;
    background-color: #f9fafc;

    .instrument-list {
      margin-top: 20rpx;

      .instrument-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 25rpx;
        background: white;
        border-radius: 8rpx;
        margin-bottom: 15rpx;
        box-shadow: $box-shadow-sm;
        position: relative;
        overflow: hidden;
        transition: $transition-base;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 4rpx;
          background: linear-gradient(180deg, $primary-color, $secondary-color);
        }

        &:hover {
          background-color: #f9fafc;
          box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.15);
        }

        .item-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 8rpx;

          .item-name {
            font-size: $font-size-large;
            color: $text-primary;
            font-weight: 500;
          }

          .item-product {
            font-size: $font-size-medium;
            color: $text-secondary;
          }
        }

        .item-arrow {
          display: flex;
          align-items: center;
          color: $text-light;
          font-size: $font-size-large;
          font-weight: bold;
        }
      }
    }

    .empty-list,
    .loading-indicator {
      text-align: center;
      padding: 60rpx 20rpx;
      color: $text-light;
      font-size: $font-size-medium;
    }
  }
}

// 深度选择器样式
:deep() {
  .back-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1rpx solid rgba(255, 255, 255, 0.3);
    color: white;
    font-size: 24rpx;
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
  }

  .dj-btn-primary {
    background: $primary-gradient;
    border: none;
    color: white;
    font-weight: 500;
    box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
    transition: $transition-base;

    &:hover {
      box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
      transform: translateY(-1rpx);
    }
  }

  .dj-btn-danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    border: none;
    color: white;
    font-weight: 500;
    box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
    transition: $transition-base;

    &:hover {
      box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.4);
      transform: translateY(-1rpx);
    }
  }

  .dj-search {
    background: white;
    border-radius: 40rpx;
    padding: 0 16rpx;
    box-shadow: $box-shadow-sm;
    transition: $transition-base;

    &:focus-within {
      box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.15);
    }
  }

  .dj-radio {
    .wd-radio__label {
      color: $text-primary;
      font-size: $font-size-medium;
      font-weight: 500;
      padding-left: 12rpx;
    }

    .wd-radio__shape {
      border-color: #c0c4cc;
      transition: $transition-base;

      &.is-checked {
        background-color: $primary-color;
        border-color: $primary-color;
        box-shadow: 0 0 4rpx rgba(102, 126, 234, 0.3);
      }
    }
  }

  .dj-popup {
    border-radius: 20rpx 20rpx 0 0;
    overflow: hidden;
  }
}
</style>
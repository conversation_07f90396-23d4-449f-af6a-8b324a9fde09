apiVersion: apps/v1
kind: Deployment
metadata:
  name: gva-web
  annotations:
    flipped-aurora/gin-vue-admin: ui
    github: "https://github.com/flipped-aurora/gin-vue-admin.git"
    app.kubernetes.io/version: 0.0.1
  labels:
    app: gva-web
    version: gva-vue3
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gva-web
      version: gva-vue3
  template:
    metadata:
      labels:
        app: gva-web
        version: gva-vue3
    spec:
      containers:
        - name: gin-vue-admin-nginx-container
          image: registry.cn-hangzhou.aliyuncs.com/gva/web:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 8080
              name: http
          readinessProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 10
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 3
          resources:
            limits:
              cpu: 500m
              memory: 1000Mi
            requests:
              cpu: 100m
              memory: 100Mi
          volumeMounts:
            - mountPath: /etc/nginx/conf.d/
              name: nginx-config
      volumes:
        - name: nginx-config
          configMap:
            name: my.conf

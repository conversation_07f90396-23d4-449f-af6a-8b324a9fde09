"""
认证服务

负责与服务器进行认证相关的HTTP通信，包括：
- 用户名密码认证
- 手机验证码认证
- 短信验证码发送
- Token刷新
- Token验证
"""

import logging
from typing import Optional, Dict, Any

from ..models import AuthResponse, LoginErrorCode
from ..exceptions import NetworkException, handle_async_auth_exception
from ..utils.config import ConfigManager
from .http_client import HttpClient


class AuthService:
    """认证服务类
    
    提供与服务器进行认证相关的HTTP通信功能。
    """
    
    def __init__(self, config_manager: Optional[ConfigManager] = None,
                 http_client: Optional[HttpClient] = None):
        """初始化认证服务
        
        Args:
            config_manager: 配置管理器（可选）
            http_client: HTTP客户端（可选）
        """
        self.config_manager = config_manager or ConfigManager()
        self.http_client = http_client or HttpClient(self.config_manager)
        self.logger = logging.getLogger(__name__)
        
        # 从配置获取服务器信息
        self.base_url = self.config_manager.get("auth.server.base_url",None)
        if not self.base_url:
            raise ValueError("服务器地址未配置")
        
        # API端点 - 与前端保持一致
        self.endpoints = {
            "login_username": "/user/login",           # 用户名密码登录
            "login_phone": "/user/loginByPhone",       # 手机号验证码登录
            "login_wechat": "/user/loginByWechat",     # 微信登录
            "send_login_code": "/user/sendLoginCode",  # 发送登录验证码
            "get_profile": "/user/getProfile",         # 获取用户信息
            "update_profile": "/user/updateProfile",  # 更新用户资料
            "get_captcha": "/base/captcha",           # 获取图形验证码
            "refresh": "/auth/refresh",               # 刷新token（如果后端支持）
            "validate": "/auth/validate",             # 验证token（如果后端支持）
            "logout": "/auth/logout"                  # 登出（如果后端支持）
        }
        
        self.logger.info(f"认证服务初始化完成 - 服务器: {self.base_url}")
    
    @handle_async_auth_exception
    async def authenticate_password(self, username: str, password: str,
                                  captcha: Optional[str] = None,
                                  captcha_id: Optional[str] = None) -> AuthResponse:
        """用户名密码认证

        Args:
            username: 用户名
            password: 密码
            captcha: 验证码（可选）
            captcha_id: 验证码ID（可选）

        Returns:
            AuthResponse: 认证响应
        """
        self.logger.info(f"开始密码认证: {username}")

        try:
            url = f"{self.base_url}{self.endpoints['login_username']}"
            data = {
                "username": username,
                "password": password
            }

            # 添加验证码信息（如果提供）
            if captcha and captcha_id:
                data["captcha"] = captcha
                data["captchaId"] = captcha_id

            response = await self.http_client.post(url, json=data)

            if response.status_code == 200:
                result = response.json()

                # 检查业务状态码
                if result.get("code") == 0:
                    # 成功响应，提取data字段
                    login_data = result.get("data", {})
                    auth_response = AuthResponse(
                        success=True,
                        access_token=login_data.get("token"),
                        refresh_token=login_data.get("refresh_token"),
                        expires_in=login_data.get("expiresAt", 3600),
                        user_info=login_data.get("user")
                    )
                    self.logger.info(f"密码认证成功: {username}")
                    return auth_response
                else:
                    # 业务错误
                    error_message = result.get("msg", "认证失败")
                    error_code = result.get("code", LoginErrorCode.INVALID_CREDENTIALS.value)

                    self.logger.warning(f"密码认证失败: {username} - {error_message}")
                    return AuthResponse(
                        success=False,
                        error_message=error_message,
                        error_code=str(error_code)
                    )
            else:
                error_data = response.json() if response.content else {}
                error_message = error_data.get("msg", "认证失败")
                error_code = error_data.get("code", LoginErrorCode.INVALID_CREDENTIALS.value)

                self.logger.warning(f"密码认证失败: {username} - {error_message}")
                return AuthResponse(
                    success=False,
                    error_message=error_message,
                    error_code=str(error_code)
                )

        except NetworkException as e:
            self.logger.error(f"密码认证网络错误: {username} - {str(e)}")
            return AuthResponse(
                success=False,
                error_message=f"网络错误: {str(e)}",
                error_code=LoginErrorCode.NETWORK_ERROR.value
            )
        except Exception as e:
            self.logger.error(f"密码认证异常: {username} - {str(e)}")
            return AuthResponse(
                success=False,
                error_message=f"认证失败: {str(e)}",
                error_code=LoginErrorCode.UNKNOWN_ERROR.value
            )
    
    @handle_async_auth_exception
    async def authenticate_phone(self, phone: str, sms_code: str) -> AuthResponse:
        """手机验证码认证

        Args:
            phone: 手机号
            sms_code: 短信验证码

        Returns:
            AuthResponse: 认证响应
        """
        self.logger.info(f"开始手机认证: {phone}")

        try:
            url = f"{self.base_url}{self.endpoints['login_phone']}"
            data = {
                "phone": phone,
                "code": sms_code  # 根据前端API，字段名为code
            }

            response = await self.http_client.post(url, json=data)

            if response.status_code == 200:
                result = response.json()

                # 检查业务状态码
                if result.get("code") == 0:
                    # 成功响应，提取data字段
                    login_data = result.get("data", {})
                    auth_response = AuthResponse(
                        success=True,
                        access_token=login_data.get("token"),
                        refresh_token=login_data.get("refresh_token"),
                        expires_in=login_data.get("expiresAt", 3600),
                        user_info=login_data.get("user")
                    )
                    self.logger.info(f"手机认证成功: {phone}")
                    return auth_response
                else:
                    # 业务错误
                    error_message = result.get("msg", "认证失败")
                    error_code = result.get("code", LoginErrorCode.SMS_CODE_INVALID.value)

                    self.logger.warning(f"手机认证失败: {phone} - {error_message}")
                    return AuthResponse(
                        success=False,
                        error_message=error_message,
                        error_code=str(error_code)
                    )
            else:
                error_data = response.json() if response.content else {}
                error_message = error_data.get("msg", "认证失败")
                error_code = error_data.get("code", LoginErrorCode.SMS_CODE_INVALID.value)

                self.logger.warning(f"手机认证失败: {phone} - {error_message}")
                return AuthResponse(
                    success=False,
                    error_message=error_message,
                    error_code=str(error_code)
                )

        except NetworkException as e:
            self.logger.error(f"手机认证网络错误: {phone} - {str(e)}")
            return AuthResponse(
                success=False,
                error_message=f"网络错误: {str(e)}",
                error_code=LoginErrorCode.NETWORK_ERROR.value
            )
        except Exception as e:
            self.logger.error(f"手机认证异常: {phone} - {str(e)}")
            return AuthResponse(
                success=False,
                error_message=f"认证失败: {str(e)}",
                error_code=LoginErrorCode.UNKNOWN_ERROR.value
            )
    
    @handle_async_auth_exception
    async def send_sms_code(self, phone: str) -> bool:
        """发送短信验证码

        Args:
            phone: 手机号

        Returns:
            bool: 发送是否成功
        """
        self.logger.info(f"发送短信验证码: {phone}")

        try:
            url = f"{self.base_url}{self.endpoints['send_login_code']}"
            data = {"phone": phone}

            response = await self.http_client.post(url, json=data)

            if response.status_code == 200:
                result = response.json()

                # 检查业务状态码
                if result.get("code") == 0:
                    self.logger.info(f"短信验证码发送成功: {phone}")
                    return True
                else:
                    error_message = result.get("msg", "发送失败")
                    self.logger.warning(f"短信验证码发送失败: {phone} - {error_message}")
                    return False
            else:
                self.logger.error(f"短信验证码发送失败: {phone} - HTTP {response.status_code}")
                return False

        except NetworkException as e:
            self.logger.error(f"短信验证码发送网络错误: {phone} - {str(e)}")
            return False
        except Exception as e:
            self.logger.error(f"短信验证码发送异常: {phone} - {str(e)}")
            return False

    @handle_async_auth_exception
    async def get_captcha(self) -> Optional[Dict[str, Any]]:
        """获取图形验证码

        Returns:
            Dict[str, Any]: 验证码信息，包含captchaId、picPath等
        """
        self.logger.info("获取图形验证码")

        try:
            url = f"{self.base_url}{self.endpoints['get_captcha']}"

            response = await self.http_client.post(url)

            if response.status_code == 200:
                result = response.json()

                # 检查业务状态码
                if result.get("code") == 0:
                    captcha_data = result.get("data", {})
                    self.logger.info("图形验证码获取成功")
                    return captcha_data
                else:
                    error_message = result.get("msg", "获取验证码失败")
                    self.logger.error(f"图形验证码获取失败: {error_message}")
                    return None
            else:
                self.logger.error(f"图形验证码获取失败 - HTTP {response.status_code}")
                return None

        except NetworkException as e:
            self.logger.error(f"图形验证码获取网络错误: {str(e)}")
            return None
        except Exception as e:
            self.logger.error(f"图形验证码获取异常: {str(e)}")
            return None

    @handle_async_auth_exception
    async def refresh_token(self, refresh_token: str) -> AuthResponse:
        """刷新Token

        Args:
            refresh_token: 刷新令牌

        Returns:
            AuthResponse: 认证响应
        """
        self.logger.info("开始刷新Token")

        try:
            url = f"{self.base_url}{self.endpoints['refresh']}"
            data = {"refresh_token": refresh_token}

            response = await self.http_client.post(url, json=data)

            if response.status_code == 200:
                result = response.json()

                # 检查业务状态码
                if result.get("code") == 0:
                    # 成功响应，提取data字段
                    token_data = result.get("data", {})
                    auth_response = AuthResponse(
                        success=True,
                        access_token=token_data.get("token"),
                        refresh_token=token_data.get("refresh_token"),
                        expires_in=token_data.get("expiresAt", 3600)
                    )
                    self.logger.info("Token刷新成功")
                    return auth_response
                else:
                    # 业务错误
                    error_message = result.get("msg", "Token刷新失败")
                    error_code = result.get("code", LoginErrorCode.TOKEN_REFRESH_FAILED.value)

                    self.logger.warning(f"Token刷新失败: {error_message}")
                    return AuthResponse(
                        success=False,
                        error_message=error_message,
                        error_code=str(error_code)
                    )
            else:
                error_data = response.json() if response.content else {}
                error_message = error_data.get("msg", "Token刷新失败")
                error_code = error_data.get("code", LoginErrorCode.TOKEN_REFRESH_FAILED.value)

                self.logger.warning(f"Token刷新失败: {error_message}")
                return AuthResponse(
                    success=False,
                    error_message=error_message,
                    error_code=str(error_code)
                )

        except NetworkException as e:
            self.logger.error(f"Token刷新网络错误: {str(e)}")
            return AuthResponse(
                success=False,
                error_message=f"网络错误: {str(e)}",
                error_code=LoginErrorCode.NETWORK_ERROR.value
            )
        except Exception as e:
            self.logger.error(f"Token刷新异常: {str(e)}")
            return AuthResponse(
                success=False,
                error_message=f"Token刷新失败: {str(e)}",
                error_code=LoginErrorCode.UNKNOWN_ERROR.value
            )
    
    @handle_async_auth_exception
    async def validate_token(self, access_token: str) -> bool:
        """验证Token有效性
        
        Args:
            access_token: 访问令牌
            
        Returns:
            bool: Token是否有效
        """
        self.logger.info("验证Token有效性")
        
        try:
            url = f"{self.base_url}{self.endpoints['validate']}"
            headers = {"Authorization": f"Bearer {access_token}"}
            
            response = await self.http_client.get(url, headers=headers)
            
            is_valid = response.status_code == 200
            
            if is_valid:
                self.logger.info("Token验证成功")
            else:
                self.logger.warning(f"Token验证失败: HTTP {response.status_code}")
            
            return is_valid
            
        except NetworkException as e:
            self.logger.error(f"Token验证网络错误: {str(e)}")
            return False
        except Exception as e:
            self.logger.error(f"Token验证异常: {str(e)}")
            return False
    
    @handle_async_auth_exception
    async def logout(self, access_token: str) -> bool:
        """登出
        
        Args:
            access_token: 访问令牌
            
        Returns:
            bool: 登出是否成功
        """
        self.logger.info("开始登出")
        
        try:
            url = f"{self.base_url}{self.endpoints['logout']}"
            headers = {"Authorization": f"Bearer {access_token}"}
            
            response = await self.http_client.post(url, headers=headers)
            
            success = response.status_code == 200
            
            if success:
                self.logger.info("登出成功")
            else:
                self.logger.warning(f"登出失败: HTTP {response.status_code}")
            
            return success
            
        except NetworkException as e:
            self.logger.error(f"登出网络错误: {str(e)}")
            return False
        except Exception as e:
            self.logger.error(f"登出异常: {str(e)}")
            return False
    
    async def close(self):
        """关闭认证服务"""
        if self.http_client:
            await self.http_client.close()
            self.logger.info("认证服务已关闭")
    
    def get_service_info(self) -> dict:
        """获取服务信息
        
        Returns:
            dict: 服务信息
        """
        return {
            "base_url": self.base_url,
            "endpoints": self.endpoints,
            "http_client_info": self.http_client.get_client_info() if self.http_client else None
        }

"""
登录管理器

负责协调整个登录流程，包括：
- 用户名密码登录
- 手机验证码登录
- 自动登录
- 登出处理
"""

import logging
from typing import Optional, Dict, Any
from datetime import datetime, timedelta

from vnpy.event import EventEngine, Event

from ..models import LoginCredentials, LoginResult, TokenData, LoginErrorCode
from ..events import (
    EVENT_USER_LOGIN_START, EVENT_USER_LOGIN_SUCCESS, EVENT_USER_LOGIN_FAILED,
    EVENT_USER_LOGOUT, EVENT_TOKEN_REFRESHED
)
from ..exceptions import (
    LoginFailedException, TokenExpiredException, ValidationException,
    handle_async_auth_exception
)
from ..services.auth_service import AuthService
from .token_manager import TokenManager
from ..utils.validators import PhoneValidator


class LoginManager:
    """登录管理器

    协调整个登录流程，管理登录状态。
    """
    
    def __init__(self, event_engine: EventEngine, auth_service: Optional[AuthService] = None,
                 token_manager: Optional[TokenManager] = None):
        """初始化登录管理器
        
        Args:
            event_engine: 事件引擎
            auth_service: 认证服务（可选，默认创建新实例）
            token_manager: Token管理器（可选，默认创建新实例）
        """
        self.event_engine = event_engine
        self.auth_service = auth_service or AuthService()
        self.token_manager = token_manager or TokenManager()
        
        self.logger = logging.getLogger(__name__)
        self.current_user = None

        # 验证器
        self.phone_validator = PhoneValidator()
    

    @handle_async_auth_exception
    async def login_with_password(self, username: str, password: str,
                                remember_me: bool = False,
                                captcha: Optional[str] = None,
                                captcha_id: Optional[str] = None) -> LoginResult:
        """用户名密码登录

        Args:
            username: 用户名
            password: 密码
            remember_me: 是否记住密码
            captcha: 验证码（可选）
            captcha_id: 验证码ID（可选）

        Returns:
            LoginResult: 登录结果
        """
        self.logger.info(f"开始密码登录: {username}")
        
        # 参数验证
        if not username or not password:
            error_msg = "用户名和密码不能为空"
            self.logger.warning(error_msg)
            return LoginResult.failure_result(error_msg, LoginErrorCode.MISSING_PARAMETERS.value)
        
        if len(password) < 6:
            error_msg = "密码长度不能少于6位"
            self.logger.warning(error_msg)
            return LoginResult.failure_result(error_msg, LoginErrorCode.INVALID_PARAMETERS.value)
        
        # 发送登录开始事件
        credentials = LoginCredentials(
            login_type="password",
            username=username,
            password=password,
            captcha=captcha,
            captcha_id=captcha_id,
            remember_me=remember_me
        )
        self.event_engine.put(Event(EVENT_USER_LOGIN_START, credentials))
        
        try:
            # 调用认证服务
            auth_response = await self.auth_service.authenticate_password(
                username, password, captcha, captcha_id
            )
            
            if not auth_response.success:
                error_msg = auth_response.error_message or "登录失败"
                error_code = auth_response.error_code or LoginErrorCode.INVALID_CREDENTIALS.value
                
                self.logger.warning(f"密码登录失败: {error_msg}")
                result = LoginResult.failure_result(error_msg, error_code)
                self.event_engine.put(Event(EVENT_USER_LOGIN_FAILED, result))
                return result
            
            # 创建Token数据
            token_data = auth_response.to_token_data()
            if not token_data:
                error_msg = "Token数据无效"
                self.logger.error(error_msg)
                result = LoginResult.failure_result(error_msg, LoginErrorCode.TOKEN_INVALID.value)
                self.event_engine.put(Event(EVENT_USER_LOGIN_FAILED, result))
                return result
            
            # 保存Token（如果需要记住）
            if remember_me:
                self.token_manager.save_token(token_data)
                self.logger.info("Token已保存到本地")
            
            # 更新当前用户信息
            self.current_user = auth_response.user_info

            # 发送登录成功事件
            result = LoginResult.success_result("登录成功", auth_response.user_info, token_data)
            self.event_engine.put(Event(EVENT_USER_LOGIN_SUCCESS, token_data))
            
            self.logger.info(f"密码登录成功: {username}")
            return result
            
        except Exception as e:
            error_msg = f"登录失败: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            result = LoginResult.failure_result(error_msg, LoginErrorCode.UNKNOWN_ERROR.value)
            self.event_engine.put(Event(EVENT_USER_LOGIN_FAILED, result))
            return result
    
    @handle_async_auth_exception
    async def login_with_phone(self, phone: str, sms_code: str, 
                             remember_me: bool = False) -> LoginResult:
        """手机验证码登录
        
        Args:
            phone: 手机号
            sms_code: 短信验证码
            remember_me: 是否记住登录信息
            
        Returns:
            LoginResult: 登录结果
        """
        self.logger.info(f"开始手机登录: {phone}")
        
        # 参数验证
        if not phone or not sms_code:
            error_msg = "手机号和验证码不能为空"
            self.logger.warning(error_msg)
            return LoginResult.failure_result(error_msg, LoginErrorCode.MISSING_PARAMETERS.value)
        
        if not self.phone_validator.validate(phone):
            error_msg = "手机号格式不正确"
            self.logger.warning(error_msg)
            return LoginResult.failure_result(error_msg, LoginErrorCode.PHONE_FORMAT_INVALID.value)
        
        if len(sms_code) != 6 or not sms_code.isdigit():
            error_msg = "验证码应为6位数字"
            self.logger.warning(error_msg)
            return LoginResult.failure_result(error_msg, LoginErrorCode.SMS_CODE_INVALID.value)
        
        # 发送登录开始事件
        credentials = LoginCredentials(
            login_type="phone",
            phone=phone,
            sms_code=sms_code,
            remember_me=remember_me
        )
        self.event_engine.put(Event(EVENT_USER_LOGIN_START, credentials))
        
        try:
            # 调用认证服务
            auth_response = await self.auth_service.authenticate_phone(phone, sms_code)
            
            if not auth_response.success:
                error_msg = auth_response.error_message or "登录失败"
                error_code = auth_response.error_code or LoginErrorCode.SMS_CODE_INVALID.value
                
                self.logger.warning(f"手机登录失败: {error_msg}")
                result = LoginResult.failure_result(error_msg, error_code)
                self.event_engine.put(Event(EVENT_USER_LOGIN_FAILED, result))
                return result
            
            # 创建Token数据
            token_data = auth_response.to_token_data()
            if not token_data:
                error_msg = "Token数据无效"
                self.logger.error(error_msg)
                result = LoginResult.failure_result(error_msg, LoginErrorCode.TOKEN_INVALID.value)
                self.event_engine.put(Event(EVENT_USER_LOGIN_FAILED, result))
                return result
            
            # 保存Token（如果需要记住）
            if remember_me:
                self.token_manager.save_token(token_data)
                self.logger.info("Token已保存到本地")
            
            # 更新当前用户信息
            self.current_user = auth_response.user_info

            # 发送登录成功事件
            result = LoginResult.success_result("登录成功", auth_response.user_info, token_data)
            self.event_engine.put(Event(EVENT_USER_LOGIN_SUCCESS, token_data))
            
            self.logger.info(f"手机登录成功: {phone}")
            return result
            
        except Exception as e:
            error_msg = f"登录失败: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            result = LoginResult.failure_result(error_msg, LoginErrorCode.UNKNOWN_ERROR.value)
            self.event_engine.put(Event(EVENT_USER_LOGIN_FAILED, result))
            return result
    
    @handle_async_auth_exception
    async def auto_login(self) -> LoginResult:
        """自动登录（使用保存的Token）
        
        Returns:
            LoginResult: 登录结果
        """
        self.logger.info("开始自动登录")
        
        try:
            # 加载本地Token
            token_data = self.token_manager.load_token()
            if not token_data:
                error_msg = "没有保存的登录信息"
                self.logger.info(error_msg)
                return LoginResult.failure_result(error_msg, LoginErrorCode.TOKEN_INVALID.value)
            
            # 检查Token是否过期
            if token_data.is_expired():
                self.logger.info("Token已过期，尝试刷新")
                
                # 尝试刷新Token
                auth_response = await self.auth_service.refresh_token(token_data.refresh_token)
                if not auth_response.success:
                    self.token_manager.clear_token()
                    error_msg = "登录信息已过期，请重新登录"
                    self.logger.warning(error_msg)
                    return LoginResult.failure_result(error_msg, LoginErrorCode.TOKEN_EXPIRED.value)
                
                # 更新Token
                new_token_data = auth_response.to_token_data()
                if new_token_data:
                    new_token_data.user_info = token_data.user_info  # 保留用户信息
                    self.token_manager.save_token(new_token_data)
                    token_data = new_token_data
                    
                    # 发送Token刷新事件
                    self.event_engine.put(Event(EVENT_TOKEN_REFRESHED, token_data))
                    self.logger.info("Token刷新成功")
            
            # 验证Token有效性
            if not await self.auth_service.validate_token(token_data.access_token):
                self.token_manager.clear_token()
                error_msg = "登录信息无效，请重新登录"
                self.logger.warning(error_msg)
                return LoginResult.failure_result(error_msg, LoginErrorCode.TOKEN_INVALID.value)
            
            # 更新当前用户信息
            self.current_user = token_data.user_info

            # 发送登录成功事件
            result = LoginResult.success_result("自动登录成功", token_data.user_info, token_data)
            self.event_engine.put(Event(EVENT_USER_LOGIN_SUCCESS, token_data))
            
            self.logger.info("自动登录成功")
            return result
            
        except Exception as e:
            error_msg = f"自动登录失败: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return LoginResult.failure_result(error_msg, LoginErrorCode.UNKNOWN_ERROR.value)
    
    def logout(self):
        """登出

        清除本地Token，发送登出事件。
        """
        self.logger.info("开始登出")

        try:
            # 清除本地Token
            self.token_manager.clear_token()

            # 清除当前用户信息
            self.current_user = None

            # 发送登出事件
            self.event_engine.put(Event(EVENT_USER_LOGOUT, None))

            self.logger.info("登出成功")

        except Exception as e:
            self.logger.error(f"登出失败: {str(e)}", exc_info=True)
    
    def is_logged_in(self) -> bool:
        """检查登录状态
        
        Returns:
            bool: 是否已登录
        """
        return self.current_user is not None
    
    def get_current_user(self) -> Optional[dict]:
        """获取当前用户信息
        
        Returns:
            Optional[dict]: 当前用户信息，未登录时返回None
        """
        return self.current_user
    
    def get_current_token(self) -> Optional[str]:
        """获取当前有效的访问令牌
        
        Returns:
            Optional[str]: 当前有效的访问令牌，无效或不存在时返回None
        """
        token_data = self.token_manager.load_token()
        if token_data and not token_data.is_expired():
            return token_data.access_token
        return None
    
    async def get_captcha(self) -> Optional[Dict[str, Any]]:
        """获取图形验证码

        Returns:
            Dict[str, Any]: 验证码信息，如果获取失败返回None
        """
        self.logger.info("获取图形验证码")

        try:
            captcha_data = await self.auth_service.get_captcha()
            if captcha_data:
                self.logger.info("图形验证码获取成功")
                return captcha_data
            else:
                self.logger.warning("图形验证码获取失败")
                return None

        except Exception as e:
            self.logger.error(f"获取图形验证码异常: {str(e)}")
            return None

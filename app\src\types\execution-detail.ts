// 前向声明以避免循环依赖
interface ITradeRequestBase {
  ID: number
  userID: number
  instrumentRefID: number
  requestType: string
  requestedQuantity: number
  requestedPrice?: number
  executedQuantity: number
  status: string
}

interface IContractBase {
  ID: number
  contractCode: string
  setterID: number
  pricerID: number
  instrumentRefID: number
  totalQuantity: number
  remainingQuantity: number
  priceType: string
  priceValue: number
  status: string
}

// 执行类型枚举
export type ExecutionType = 'Online' | 'Offline' // Online: 线上执行, Offline: 线下执行

// 执行状态枚举
export type ExecutionStatus = 'Success' | 'Failed' // Success: 成功, Failed: 失败

// 执行明细基础接口
export interface IExecutionDetail {
  // 基础字段
  ID: number
  CreatedAt: string
  UpdatedAt: string
  DeletedAt?: string
  
  // 关联ID
  tradeRequestID: number
  tradeRequest?: ITradeRequestBase
  contractID: number
  contract?: IContractBase
  
  // 执行信息
  executedQuantity: number      // 从该合同执行的数量
  executedPrice: number         // 本次执行的成交价
  executionType: ExecutionType  // 执行类型(Online/Offline)
  contractPrice: number         // 执行时使用的合同价格
  resultPrice: number           // 执行结果价格
  status: ExecutionStatus       // 执行状态(Success/Failed)
  
  // 备注信息
  remarks?: string
}

// 创建执行明细的请求结构
export interface ICreateExecutionDetailRequest {
  tradeRequestID: number
  contractID: number
  executedQuantity: number
  executedPrice: number
  executionType: ExecutionType
  remarks?: string
}

// 更新执行明细的请求结构
export interface IUpdateExecutionDetailRequest {
  id: number
  executedQuantity: number
  executedPrice: number
  executionType: ExecutionType
  status: ExecutionStatus
  remarks?: string
}

// 执行明细列表查询请求
export interface IExecutionDetailListRequest {
  page?: number
  pageSize?: number
  tradeRequestID?: number
  contractID?: number
  executionType?: ExecutionType
  status?: ExecutionStatus
  startDate?: string
  endDate?: string
}

// 执行明细响应结构
export interface IExecutionDetailResponse extends IExecutionDetail {
  // 可以在这里添加额外的响应字段
}

// 执行汇总信息接口 - 用于报表和统计
export interface IExecutionSummary {
  tradeRequestID: number
  totalQuantity: number      // 总执行数量
  totalValue: number         // 总执行金额
  contractCount: number      // 涉及的合同数量
  onlineQuantity: number     // 线上执行数量
  offlineQuantity: number    // 线下执行数量
  successQuantity: number    // 成功执行数量
  failedQuantity: number     // 失败执行数量
}

// 分页响应类型
export interface IExecutionDetailListResponse {
  list: IExecutionDetailResponse[]
  total: number
  page: number
  pageSize: number
}
#!/usr/bin/env python3
"""
集成登录测试脚本

将测试控制功能集成到登录窗口中，避免模态对话框问题
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import (
    QApplication, QVBoxLayout, QHBoxLayout, QWidget, 
    QPushButton, QLabel, QGroupBox
)
from PySide6.QtCore import QTimer

# 设置日志
logging.basicConfig(level=logging.INFO)

def test_login_integrated():
    """集成登录测试"""
    try:
        # 创建应用
        app = QApplication(sys.argv)
        
        # 导入必要的模块
        from vnpy.event import EventEngine
        from ui.login.login_window import LoginWindow
        
        # 创建事件引擎
        event_engine = EventEngine()
        
        # 创建登录窗口
        login_window = LoginWindow(event_engine)
        
        # 创建测试控制面板
        test_panel = QGroupBox("测试控制面板")
        test_layout = QVBoxLayout(test_panel)
        
        # 状态显示
        status_label = QLabel("按钮状态: 未知")
        test_layout.addWidget(status_label)
        
        def update_status():
            enabled = login_window.login_button.isEnabled()
            text = login_window.login_button.text()
            status_label.setText(f"按钮状态: {'启用' if enabled else '禁用'} | 文本: {text}")
        
        # 定时更新状态
        status_timer = QTimer()
        status_timer.timeout.connect(update_status)
        status_timer.start(500)
        
        # 测试按钮行
        button_row1 = QHBoxLayout()
        button_row2 = QHBoxLayout()
        button_row3 = QHBoxLayout()
        
        # 填充密码登录数据
        fill_pwd_btn = QPushButton("填充密码数据")
        def fill_password_data():
            login_window.login_widget.tab_widget.setCurrentIndex(0)
            login_window.login_widget.username_edit.setText("test_user")
            login_window.login_widget.password_edit.setText("test_password")
            login_window.login_widget.captcha_edit.setText("1234")
            print("✅ 密码登录数据已填充")
            update_status()
        fill_pwd_btn.clicked.connect(fill_password_data)
        button_row1.addWidget(fill_pwd_btn)
        
        # 填充手机登录数据
        fill_phone_btn = QPushButton("填充手机数据")
        def fill_phone_data():
            login_window.login_widget.tab_widget.setCurrentIndex(1)
            login_window.login_widget.phone_edit.setText("18678863949")
            login_window.login_widget.code_edit.setText("123456")
            print("✅ 手机登录数据已填充")
            update_status()
        fill_phone_btn.clicked.connect(fill_phone_data)
        button_row1.addWidget(fill_phone_btn)
        
        # Tab切换
        switch_tab_btn = QPushButton("切换Tab")
        def switch_tab():
            current = login_window.login_widget.tab_widget.currentIndex()
            new_index = 1 if current == 0 else 0
            login_window.login_widget.tab_widget.setCurrentIndex(new_index)
            tab_name = "密码登录" if new_index == 0 else "手机登录"
            print(f"✅ 切换到{tab_name}")
            update_status()
        switch_tab_btn.clicked.connect(switch_tab)
        button_row2.addWidget(switch_tab_btn)
        
        # 清空输入
        clear_btn = QPushButton("清空输入")
        def clear_inputs():
            login_window.login_widget.username_edit.clear()
            login_window.login_widget.password_edit.clear()
            login_window.login_widget.captcha_edit.clear()
            login_window.login_widget.phone_edit.clear()
            login_window.login_widget.code_edit.clear()
            print("✅ 输入已清空")
            update_status()
        clear_btn.clicked.connect(clear_inputs)
        button_row2.addWidget(clear_btn)
        
        # 模拟登录
        test_login_btn = QPushButton("测试登录")
        def test_login():
            try:
                login_window.on_login_clicked()
                print("✅ 登录测试完成")
                update_status()
            except Exception as e:
                print(f"❌ 登录测试异常: {str(e)}")
        test_login_btn.clicked.connect(test_login)
        button_row3.addWidget(test_login_btn)
        
        # 重置状态
        reset_btn = QPushButton("重置状态")
        def reset_state():
            login_window.set_login_state(False)
            print("✅ 状态已重置")
            update_status()
        reset_btn.clicked.connect(reset_state)
        button_row3.addWidget(reset_btn)
        
        # 添加按钮行到布局
        test_layout.addLayout(button_row1)
        test_layout.addLayout(button_row2)
        test_layout.addLayout(button_row3)
        
        # 将测试面板添加到登录窗口
        main_layout = login_window.layout()
        main_layout.addWidget(test_panel)
        
        # 调整窗口大小（宽度固定，高度自适应）
        login_window.setFixedWidth(500)
        login_window.adjust_size_to_content()  # 根据内容调整高度
        
        # 显示窗口
        login_window.show()
        
        # 初始状态更新
        update_status()
        
        # 设置窗口关闭时退出应用
        def on_window_closed():
            status_timer.stop()
            app.quit()
        
        login_window.finished.connect(on_window_closed)
        login_window.closeEvent = lambda event: (on_window_closed(), event.accept())
        
        print("=" * 60)
        print("集成登录测试启动")
        print("=" * 60)
        print("测试控制面板已集成到登录窗口底部")
        print("1. 使用测试按钮填充数据和测试功能")
        print("2. 观察按钮状态的实时变化")
        print("3. 测试Tab切换、登录、状态重置等功能")
        print("4. 所有操作都在同一个窗口中，无模态对话框问题")
        print("=" * 60)
        
        # 运行应用
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_login_integrated()

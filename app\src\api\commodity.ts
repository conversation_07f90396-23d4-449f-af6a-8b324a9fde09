import { http } from '@/http/http'
import type {
  ICommodity,
  ICommodityRequest,
  ICommodityListRequest,
  ICommodityListResponse
} from '@/types'

// 为了保持兼容性，重新导出类型
export type {
  ICommodity,
  ICommodityRequest,
  ICommodityListRequest,
  ICommodityListResponse,
}

/**
 * 获取所有商品列表
 */
export function getAllCommodityList() {
  return http.get<ICommodity[]>('/commodity/getAllCommodityList')
}

/**
 * 分页获取商品列表
 */
export function getCommodityList(params: ICommodityListRequest) {
  return http.get<ICommodityListResponse>('/commodity/getCommodityList', params)
}

/**
 * 根据ID获取商品详情
 */
export function getCommodityById(id: number) {
  return http.get<ICommodity>('/commodity/findCommodity', { ID: id })
}

/**
 * 创建商品
 */
export function createCommodity(data: ICommodityRequest) {
  return http.post<ICommodity>('/commodity/createCommodity', data)
}

/**
 * 更新商品
 */
export function updateCommodity(data: ICommodityRequest) {
  return http.put<boolean>('/commodity/updateCommodity', data)
}

/**
 * 删除商品
 */
export function deleteCommodity(id: number) {
  return http.delete<boolean>('/commodity/deleteCommodity', { params: { ID: id } })
}

/**
 * 批量删除商品
 */
export function deleteCommodityByIds(ids: number[]) {
  return http.delete<boolean>('/commodity/deleteCommodityByIds', { params: { IDs: ids } })
}
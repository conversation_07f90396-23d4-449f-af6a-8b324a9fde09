package dianjia

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/dianjia"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type QuotationApi struct{}

// CreateQuotation 创建报价
// @Tags Quotation
// @Summary 创建报价
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body dianjia.CreateQuotationRequest true "创建报价请求"
// @Success 200 {object} response.Response{data=dianjia.QuotationResponse,msg=string} "创建成功"
// @Router /api/v1/dianjia/quotations [post]
func (quotationApi *QuotationApi) CreateQuotation(c *gin.Context) {
	var req dianjia.CreateQuotationRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户信息
	uuid := utils.GetUserUuid(c)
	user, err := service.ServiceGroupApp.SystemServiceGroup.UserService.GetUserInfo(uuid)
	if err != nil {
		global.GVA_LOG.Error("获取用户信息失败!", zap.Error(err))
		response.FailWithMessage("获取用户信息失败", c)
		return
	}

	quotationResponse, err := service.ServiceGroupApp.DianjiaServiceGroup.QuotationService.CreateOrUpdateQuotation(req, user.ID)
	if err != nil {
		global.GVA_LOG.Error("创建报价失败!", zap.Error(err))
		response.FailWithMessage("创建报价失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(quotationResponse, "创建报价成功", c)
}

// UpdateQuotation 更新报价
// @Tags Quotation
// @Summary 更新报价
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param quotationId path uint true "报价ID"
// @Param data body dianjia.UpdateQuotationRequest true "更新报价请求"
// @Success 200 {object} response.Response{data=dianjia.QuotationResponse,msg=string} "更新成功"
// @Router /api/v1/dianjia/quotations/{quotationId} [put]
func (quotationApi *QuotationApi) UpdateQuotation(c *gin.Context) {
	var req dianjia.UpdateQuotationRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 从路径参数获取报价ID
	quotationIDStr := c.Param("quotationId")
	quotationID, err := strconv.ParseUint(quotationIDStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的报价ID", c)
		return
	}
	req.ID = uint(quotationID)

	// 获取当前用户信息
	uuid := utils.GetUserUuid(c)
	user, err := service.ServiceGroupApp.SystemServiceGroup.UserService.GetUserInfo(uuid)
	if err != nil {
		global.GVA_LOG.Error("获取用户信息失败!", zap.Error(err))
		response.FailWithMessage("获取用户信息失败", c)
		return
	}

	quotationResponse, err := service.ServiceGroupApp.DianjiaServiceGroup.QuotationService.UpdateQuotation(req, user.ID)
	if err != nil {
		global.GVA_LOG.Error("更新报价失败!", zap.Error(err))
		response.FailWithMessage("更新报价失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(quotationResponse, "更新报价成功", c)
}

// PublishQuotation 发布报价
// @Tags Quotation
// @Summary 发布报价
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param quotationId path uint true "报价ID"
// @Success 200 {object} response.Response{msg=string} "发布成功"
// @Router /api/v1/dianjia/quotations/{quotationId}/publish [post]
func (quotationApi *QuotationApi) PublishQuotation(c *gin.Context) {
	// 从路径参数获取报价ID
	quotationIDStr := c.Param("quotationId")
	quotationID, err := strconv.ParseUint(quotationIDStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的报价ID", c)
		return
	}

	// 获取当前用户信息
	uuid := utils.GetUserUuid(c)
	user, err := service.ServiceGroupApp.SystemServiceGroup.UserService.GetUserInfo(uuid)
	if err != nil {
		global.GVA_LOG.Error("获取用户信息失败!", zap.Error(err))
		response.FailWithMessage("获取用户信息失败", c)
		return
	}

	// 绑定请求数据
	var req dianjia.PublishQuotationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("请求参数错误", c)
		return
	}

	err = service.ServiceGroupApp.DianjiaServiceGroup.QuotationService.PublishQuotation(uint(quotationID), user.ID, req.ExpiresAt)
	if err != nil {
		global.GVA_LOG.Error("发布报价失败!", zap.Error(err))
		response.FailWithMessage("发布报价失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("发布报价成功", c)
}

// ToggleQuotationStatus 切换报价状态
// @Tags Quotation
// @Summary 切换报价状态
// @Description 在Draft和Active状态之间切换
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param quotationId path uint true "报价ID"
// @Success 200 {object} response.Response{msg=string} "状态切换成功"
// @Router /api/v1/dianjia/quotations/{quotationId}/toggle [post]
func (quotationApi *QuotationApi) ToggleQuotationStatus(c *gin.Context) {
	// 从路径参数获取报价ID
	quotationIDStr := c.Param("quotationId")
	quotationID, err := strconv.ParseUint(quotationIDStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的报价ID", c)
		return
	}

	// 获取当前用户信息
	uuid := utils.GetUserUuid(c)
	user, err := service.ServiceGroupApp.SystemServiceGroup.UserService.GetUserInfo(uuid)
	if err != nil {
		global.GVA_LOG.Error("获取用户信息失败!", zap.Error(err))
		response.FailWithMessage("获取用户信息失败", c)
		return
	}

	err = service.ServiceGroupApp.DianjiaServiceGroup.QuotationService.ToggleQuotationStatus(uint(quotationID), user.ID)
	if err != nil {
		global.GVA_LOG.Error("切换报价状态失败!", zap.Error(err))
		response.FailWithMessage("切换状态失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("状态切换成功", c)
}

// DeleteQuotation 删除报价
// @Tags Quotation
// @Summary 删除报价
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param quotationId path uint true "报价ID"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /api/v1/dianjia/quotations/{quotationId} [delete]
func (quotationApi *QuotationApi) DeleteQuotation(c *gin.Context) {
	// 从路径参数获取报价ID
	quotationIDStr := c.Param("quotationId")
	quotationID, err := strconv.ParseUint(quotationIDStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的报价ID", c)
		return
	}

	// 获取当前用户信息
	uuid := utils.GetUserUuid(c)
	user, err := service.ServiceGroupApp.SystemServiceGroup.UserService.GetUserInfo(uuid)
	if err != nil {
		global.GVA_LOG.Error("获取用户信息失败!", zap.Error(err))
		response.FailWithMessage("获取用户信息失败", c)
		return
	}

	err = service.ServiceGroupApp.DianjiaServiceGroup.QuotationService.DeleteQuotation(uint(quotationID), user.ID)
	if err != nil {
		global.GVA_LOG.Error("删除报价失败!", zap.Error(err))
		response.FailWithMessage("删除报价失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("删除报价成功", c)
}

// GetMyQuotationList 获取我的报价列表
// @Tags Quotation
// @Summary 获取我的报价列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Param filter query string false "过滤条件(valid/invalid)"
// @Param status query string false "具体状态"
// @Success 200 {object} response.Response{data=dianjia.QuotationListResponse,msg=string} "获取成功"
// @Router /api/v1/dianjia/my-quotations [get]
func (quotationApi *QuotationApi) GetMyQuotationList(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	filter := c.DefaultQuery("filter", "")
	status := c.DefaultQuery("status", "")

	// 获取当前用户信息
	uuid := utils.GetUserUuid(c)
	user, err := service.ServiceGroupApp.SystemServiceGroup.UserService.GetUserInfo(uuid)
	if err != nil {
		global.GVA_LOG.Error("获取用户信息失败!", zap.Error(err))
		response.FailWithMessage("获取用户信息失败", c)
		return
	}

	req := dianjia.MyQuotationListRequest{
		Page:     page,
		PageSize: pageSize,
		Filter:   filter,
		Status:   status,
	}

	result, err := service.ServiceGroupApp.DianjiaServiceGroup.QuotationService.GetMyQuotationList(req, user.ID)
	if err != nil {
		global.GVA_LOG.Error("获取我的报价列表失败!", zap.Error(err))
		response.FailWithMessage("获取我的报价列表失败", c)
		return
	}

	response.OkWithDetailed(result, "获取成功", c)
}

// GetPublicQuotationList 获取公开报价列表
// @Tags Quotation
// @Summary 获取公开报价列表
// @accept application/json
// @Produce application/json
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Param commodityID query uint false "商品种类ID"
// @Param keyword query string false "关键词搜索"
// @Param priceType query string false "价格类型"
// @Success 200 {object} response.Response{data=dianjia.QuotationListResponse,msg=string} "获取成功"
// @Router /api/v1/dianjia/quotations [get]
func (quotationApi *QuotationApi) GetPublicQuotationList(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	keyword := c.DefaultQuery("keyword", "")
	priceType := c.DefaultQuery("priceType", "")

	var commodityID *uint
	if commodityIDStr := c.DefaultQuery("commodityID", ""); commodityIDStr != "" {
		if id, err := strconv.ParseUint(commodityIDStr, 10, 32); err == nil {
			commodityIDUint := uint(id)
			commodityID = &commodityIDUint
		}
	}

	req := dianjia.QuotationListRequest{
		Page:        page,
		PageSize:    pageSize,
		CommodityID: commodityID,
		Keyword:     keyword,
		PriceType:   priceType,
	}

	result, err := service.ServiceGroupApp.DianjiaServiceGroup.QuotationService.GetPublicQuotationList(req)
	if err != nil {
		global.GVA_LOG.Error("获取公开报价列表失败!", zap.Error(err))
		response.FailWithMessage("获取公开报价列表失败", c)
		return
	}

	response.OkWithDetailed(result, "获取成功", c)
}

// GetQuotationDetail 获取报价详情
// @Tags Quotation
// @Summary 获取报价详情
// @accept application/json
// @Produce application/json
// @Param quotationId path uint true "报价ID"
// @Success 200 {object} response.Response{data=dianjia.QuotationResponse,msg=string} "获取成功"
// @Router /api/v1/dianjia/quotations/{quotationId} [get]
func (quotationApi *QuotationApi) GetQuotationDetail(c *gin.Context) {
	// 从路径参数获取报价ID
	quotationIDStr := c.Param("quotationId")
	quotationID, err := strconv.ParseUint(quotationIDStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的报价ID", c)
		return
	}

	// 尝试获取当前用户信息（可能是未登录用户）
	userID := utils.GetUserID(c)
	var result *dianjia.QuotationResponse
	if userID > 0 {
		// 已登录用户，使用带权限验证的方法
		result, err = service.ServiceGroupApp.DianjiaServiceGroup.QuotationService.GetQuotationDetailWithPermission(uint(quotationID), userID)
	} else {
		// 未登录用户，只能查看公开的报价详情
		result, err = service.ServiceGroupApp.DianjiaServiceGroup.QuotationService.GetQuotationDetail(uint(quotationID))
		// 如果不是Active状态，拒绝访问
		if err == nil && result.Status != dianjia.QuotationStatusActive {
			response.FailWithMessage("报价不存在或无权限查看", c)
			return
		}
	}

	if err != nil {
		global.GVA_LOG.Error("获取报价详情失败!", zap.Error(err))
		response.FailWithMessage("获取报价详情失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(result, "获取成功", c)
}

# Order客户端登录功能

基于vnpy框架的期货交易客户端登录认证系统，提供安全可靠的用户认证功能。

## 🎯 功能特性

### 核心功能
- ✅ **双重登录方式**: 用户名密码 + 手机验证码
- ✅ **安全Token管理**: AES加密存储，自动刷新机制
- ✅ **WebSocket集成**: 登录后自动更新WebSocket认证状态
- ✅ **事件驱动**: 基于vnpy EventEngine的事件通知系统
- ✅ **自动登录**: 支持记住密码和自动登录功能
- ✅ **现代化UI**: 基于PySide6的美观登录界面
- ✅ **状态指示**: 实时显示连接和登录状态
- ✅ **主程序集成**: 无缝集成到vnpy主程序中

### 安全特性
- 🔐 **Token加密存储**: 使用机器特征生成密钥，AES加密
- 🔄 **自动Token刷新**: 过期前自动刷新，无感知体验
- ✅ **输入验证**: 用户名、密码、手机号格式验证
- 🛡️ **防暴力破解**: 登录失败次数限制
- 📱 **短信验证**: 支持短信验证码登录

### 技术特性
- 🏗️ **分层架构**: UI层、业务层、数据层清晰分离
- 🔧 **模块化设计**: 每个功能模块独立，低耦合高内聚
- 🧪 **可测试性**: 便于单元测试和集成测试
- 📈 **可扩展性**: 便于后续功能扩展和维护
- ⚙️ **配置化**: 关键参数可配置，环境适应性强

## 📁 项目结构

```
order/
├── auth/                           # 认证模块
│   ├── __init__.py                 # 模块初始化
│   ├── models.py                   # 数据模型定义
│   ├── events.py                   # 事件常量定义
│   ├── exceptions.py               # 异常类定义
│   ├── managers/                   # 管理器模块
│   │   ├── __init__.py
│   │   ├── login_manager.py        # 登录管理器
│   │   └── token_manager.py        # Token管理器
│   ├── services/                   # 服务模块
│   │   ├── __init__.py
│   │   ├── auth_service.py         # 认证服务
│   │   └── http_client.py          # HTTP客户端
│   └── utils/                      # 工具模块
│       ├── __init__.py
│       ├── validators.py           # 验证器
│       ├── crypto.py               # 加密工具
│       └── config.py               # 配置管理
├── ui/                             # UI界面模块
│   ├── __init__.py                 # UI模块导出
│   ├── base/                       # 基础UI组件
│   │   ├── __init__.py
│   │   └── base_dialog.py          # 基础对话框
│   ├── login/                      # 登录界面
│   │   ├── __init__.py
│   │   ├── login_window.py         # 登录窗口
│   │   ├── login_widget.py         # 登录表单
│   │   └── status_indicator.py     # 状态指示器
│   └── styles/                     # 样式管理
│       ├── __init__.py
│       ├── common_styles.py        # 通用样式
│       └── login_styles.py         # 登录样式
├── config/                         # 配置文件
│   ├── auth_config.yaml            # 认证配置
│   ├── ui_config.yaml              # UI配置
│   └── logging_config.yaml         # 日志配置
├── main.py                         # 主程序（集成登录）
├── run.py                          # 启动脚本
├── test_login.py                   # 登录测试脚本
├── example_usage.py                # 使用示例
├── requirements.txt                # 依赖包列表
└── README.md                       # 项目说明
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置设置

编辑配置文件 `config/auth_config.yaml`:

```yaml
auth:
  server:
    base_url: "https://your-api-server.com"
    timeout: 30
  token:
    storage_file: "user_token.json"
    encryption_enabled: true
```

### 3. 基本使用

```python
import asyncio
from vnpy.event import EventEngine
from auth import LoginManager

# 初始化
event_engine = EventEngine()
login_manager = LoginManager(event_engine)

# 用户名密码登录
async def login_example():
    result = await login_manager.login_with_password(
        username="your_username",
        password="your_password",
        remember_me=True
    )

    if result.success:
        print(f"登录成功: {result.user_info}")
    else:
        print(f"登录失败: {result.message}")

# 运行示例
asyncio.run(login_example())
```

### 4. 运行应用程序

```bash
# 运行主程序（集成登录功能）
python main.py

# 或使用启动脚本
python run.py main

# 测试登录功能
python run.py test

# 运行使用示例
python run.py example
```

## 📚 API文档

### LoginManager

登录管理器，协调整个登录流程。

#### 主要方法

```python
# 用户名密码登录
async def login_with_password(username: str, password: str, remember_me: bool = False) -> LoginResult

# 手机验证码登录
async def login_with_phone(phone: str, sms_code: str, remember_me: bool = False) -> LoginResult

# 自动登录
async def auto_login() -> LoginResult

# 登出
def logout()

# 检查登录状态
def is_logged_in() -> bool
```

### TokenManager

Token管理器，负责Token的安全存储和管理。

#### 主要方法

```python
# 保存Token
def save_token(token_data: TokenData)

# 加载Token
def load_token() -> Optional[TokenData]

# 清除Token
def clear_token()

# 检查Token有效性
def is_token_valid(token_data: TokenData) -> bool
```

### AuthService

认证服务，处理HTTP认证请求。

#### 主要方法

```python
# 密码认证
async def authenticate_password(username: str, password: str) -> AuthResponse

# 手机验证码认证
async def authenticate_phone(phone: str, sms_code: str) -> AuthResponse

# 发送短信验证码
async def send_sms_code(phone: str) -> bool

# 刷新Token
async def refresh_token(refresh_token: str) -> AuthResponse

# 验证Token
async def validate_token(access_token: str) -> bool
```

### LoginWindow

登录窗口，提供完整的登录界面。

#### 主要方法

```python
# 显示登录窗口
def show_centered()

# 模态显示登录窗口
def exec_centered() -> int

# 检查登录状态并显示
def check_login_and_show() -> bool
```

#### 信号

```python
# 登录成功信号
login_success = Signal(object)  # LoginResult

# 登录取消信号
login_cancelled = Signal()
```

### LoginWidget

登录表单组件，处理用户输入。

#### 主要方法

```python
# 获取登录凭据
def get_login_credentials() -> LoginCredentials

# 验证当前输入
def validate_current_input() -> Optional[str]

# 保存登录凭据
def save_credentials(credentials: LoginCredentials)

# 清空表单
def clear_form()
```

### StatusIndicator

状态指示器，显示连接和登录状态。

#### 主要方法

```python
# 设置状态
def set_status(status: str, message: str = "")

# 显示加载状态
def show_loading(message: str = "加载中...")

# 显示成功状态
def show_success(message: str = "操作成功")

# 显示错误状态
def show_error(message: str = "操作失败")
```

## 🔧 配置说明

### 认证配置 (auth_config.yaml)

```yaml
auth:
  server:
    base_url: "https://api.server.com"  # API服务器地址
    timeout: 30                         # 请求超时时间（秒）
    retry_count: 3                      # 重试次数

  token:
    storage_file: "user_token.json"     # Token存储文件
    encryption_enabled: true            # 是否启用加密
    auto_refresh: true                  # 是否自动刷新

  sms:
    countdown_seconds: 60               # 验证码倒计时（秒）
    max_retry_count: 3                  # 最大重试次数
```

### UI配置 (ui_config.yaml)

```yaml
ui:
  login_window:
    width: 400                          # 窗口宽度
    height: 350                         # 窗口高度
    resizable: false                    # 是否可调整大小

  theme:
    primary_color: "#3498db"            # 主色调
    success_color: "#27ae60"            # 成功色
    error_color: "#e74c3c"              # 错误色
```

## 🧪 测试

### 运行单元测试

```bash
pytest tests/unit/ -v
```

### 运行集成测试

```bash
pytest tests/integration/ -v
```

### 测试覆盖率

```bash
pytest --cov=auth tests/ --cov-report=html
```

## 🔒 安全考虑

1. **Token加密**: 使用AES加密存储Token，密钥基于机器特征生成
2. **输入验证**: 所有用户输入都经过严格验证
3. **网络安全**: 使用HTTPS通信，验证SSL证书
4. **权限控制**: Token文件设置适当的文件权限
5. **错误处理**: 不泄露敏感信息的错误处理

## 📈 性能优化

1. **异步处理**: 所有网络请求使用异步处理
2. **连接复用**: HTTP客户端支持连接池
3. **缓存策略**: Token本地缓存，减少网络请求
4. **资源管理**: 及时释放网络连接和文件句柄

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如果您有任何问题或建议，请：

1. 查看 [文档](docs/)
2. 搜索 [Issues](issues)
3. 创建新的 [Issue](issues/new)

## 🔄 更新日志

### v1.0.0 (2024-12-26)
- ✨ 初始版本发布
- ✅ 实现双重登录方式
- ✅ 实现Token安全管理
- ✅ 实现WebSocket集成
- ✅ 实现事件驱动架构
- ✅ 完善的配置管理
- ✅ 完整的测试覆盖
<script lang="ts" setup>
import { ref, computed, onMounted, type PropType, watch } from 'vue'
import type { 
  IInstrumentSelectItem, 
  IInstrumentSelectorProps, 
  IInstrumentSelectorEmits 
} from '@/types'
import { getInstrumentSelectList } from '@/api/instrument'
import { ExchangeMap } from '@/types/instrument'

// 组件定义
defineOptions({
  name: 'InstrumentSelector'
})


const props = defineProps({
  modelValue: {
    type: Number,
    default: 0
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入期货合约'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: true
  },
  customClass: {
    type: String,
    default: ''
  },
  customLabelClass: {
    type: String,
    default: ''
  },
  customValueClass: {
    type: String,
    default: ''
  }
})



// Emits 定义
const emit = defineEmits<{
  'update:modelValue': [value: number]
  'change': [value: number, instrumentData?: IInstrumentSelectItem | null]
}>()

// 状态管理
const showPicker = ref(false)
const isLoading = ref(false)
const selectedValues = ref<string[]>([])
const dataInitialized = ref(false)
const currentDisplayValue = ref('')
const selectedInstrument = ref<IInstrumentSelectItem | null>(null)

// 选择器的列数据，按需动态更新
const columns = ref<Array<Array<{value: string, label: string}>>>([])

// 确保columns始终是有效的数组结构
const safeColumns = computed(() => {
  if (!Array.isArray(columns.value) || columns.value.length === 0) {
    return [[]]
  }
  return columns.value.filter(col => Array.isArray(col) && col.length > 0)
})

// 缓存数据结构
interface GroupedData {
  exchanges: {
    value: string
    label: string
  }[]
  productsByExchange: Record<string, {
    value: string
    label: string
  }[]>
  contractsByProduct: Record<string, {
    value: string
    label: string
    instrumentData: IInstrumentSelectItem
  }[]>
  instrumentsById: Record<number, IInstrumentSelectItem>
}

// 缓存数据
const cachedData = ref<GroupedData>({
  exchanges: [],
  productsByExchange: {},
  contractsByProduct: {},
  instrumentsById: {}
})

// 监听modelValue变化，查找对应的instrument并更新显示
watch(() => props.modelValue, (newId) => {
  if (newId && cachedData.value.instrumentsById[newId]) {
    selectedInstrument.value = cachedData.value.instrumentsById[newId]
    updateDisplayValueFromInstrument(selectedInstrument.value)
  } else if (!newId) {
    selectedInstrument.value = null
    currentDisplayValue.value = ''
  }
}, { immediate: true })

// 初始化加载全部数据
async function initializeData() {
  try {
    isLoading.value = true

    // 1. 获取交易所列表
    cachedData.value.exchanges = Object.entries(ExchangeMap).map(([value, label]) => ({
      value,
      label
    }))

    // 2. 获取所有合约数据
    const instrumentResponse = await getInstrumentSelectList()
    const instruments = instrumentResponse.data || []

    if (instruments.length === 0) {
      uni.showToast({ title: '未获取到合约数据', icon: 'none' })
      return
    }

    // 3. 从合约数据中提取商品信息，按交易所分组
    const productsByExchange: Record<string, any[]> = {}
    const productSet = new Map<string, string>()
    const instrumentsById: Record<number, IInstrumentSelectItem> = {}

    for (const instrument of instruments) {
      // 建立ID到instrument的映射
      instrumentsById[instrument.id] = instrument
      
      const productKey = `${instrument.exchange_id}_${instrument.product_name}`
      if (!productSet.has(productKey)) {
        productSet.set(productKey, instrument.product_name)

        if (!productsByExchange[instrument.exchange_id]) {
          productsByExchange[instrument.exchange_id] = []
        }

        productsByExchange[instrument.exchange_id].push({
          value: instrument.product_name,
          label: instrument.product_name
        })
      }
    }
    cachedData.value.productsByExchange = productsByExchange
    cachedData.value.instrumentsById = instrumentsById

    // 4. 按商品分组合约
    const contractsByProduct: Record<string, any[]> = {}
    for (const instrument of instruments) {
      const key = `${instrument.exchange_id}_${instrument.product_name}`
      if (!contractsByProduct[key]) {
        contractsByProduct[key] = []
      }

      contractsByProduct[key].push({
        value: instrument.instrument_id,
        label: instrument.instrument_id,
        instrumentData: instrument
      })
    }
    cachedData.value.contractsByProduct = contractsByProduct
    
    // 初始化列数据，确保第一列是交易所列表
    if (cachedData.value.exchanges.length > 0) {
      columns.value = [cachedData.value.exchanges]
    } else {
      columns.value = [[]]
    }
    
    // 如果有初始值，查找对应的instrument并设置显示值
    if (props.modelValue && cachedData.value.instrumentsById[props.modelValue]) {
      selectedInstrument.value = cachedData.value.instrumentsById[props.modelValue]
      updateDisplayValueFromInstrument(selectedInstrument.value)
    }
    
    selectedValues.value = []
    dataInitialized.value = true
  } catch (error) {
    uni.showToast({ title: '数据加载失败，请重试', icon: 'error' })
    throw error
  } finally {
    isLoading.value = false
  }
}

// 更新显示值
function updateDisplayValueFromInstrument(instrument: IInstrumentSelectItem) {
  if (instrument) {
    currentDisplayValue.value = `${ExchangeMap[instrument.exchange_id] || instrument.exchange_id}.${instrument.instrument_id}`
  } else {
    currentDisplayValue.value = ''
  }
}

// 处理列变化
function handleColumnChange({ selectedItem, index, resolve, finish }) {
  try {
    if (!selectedItem?.value) {
      finish()
      return
    }

    if (index === 0) {
      // 选择了交易所，加载商品列表
      const exchangeId = selectedItem.value
      const products = cachedData.value.productsByExchange[exchangeId] || []

      if (products.length > 0) {
        // 更新selectedValues[0]来记录当前选择的交易所
        selectedValues.value[0] = exchangeId
        // 重置后续的选择
        selectedValues.value.splice(1)
        
        // 更新columns，添加商品列
        columns.value = [
          cachedData.value.exchanges,
          products
        ]
        resolve(products)
      } else {
        uni.showToast({ title: '该交易所暂无商品', icon: 'none' })
        finish()
      }
    } else if (index === 1) {
      // 选择了商品，加载合约列表
      const productName = selectedItem.value
      // 从selectedValues中获取当前选择的交易所ID
      const exchangeId = selectedValues.value[0]
      
      if (!exchangeId) {
        uni.showToast({ title: '请先选择交易所', icon: 'none' })
        finish()
        return
      }
      
      // 更新selectedValues[1]来记录当前选择的商品
      selectedValues.value[1] = productName
      // 重置第三级选择
      selectedValues.value.splice(2)
      
      const key = `${exchangeId}_${productName}`
      const contracts = cachedData.value.contractsByProduct[key] || []

      if (contracts.length > 0) {
        // 更新columns，添加合约列
        columns.value = [
          cachedData.value.exchanges,
          cachedData.value.productsByExchange[exchangeId] || [],
          contracts
        ]
        resolve(contracts)
      } else {
        uni.showToast({ title: '该商品暂无合约', icon: 'none' })
        finish()
      }
    } else {
      // 选择了合约，结束选择
      finish()
    }
  } catch (error) {
    console.error('列变化处理错误:', error)
    uni.showToast({ title: '数据加载失败', icon: 'error' })
    finish()
  }
}

// 处理选择确认
function handleConfirm({ value, selectedItems }) {
  try {
    let instrumentData: IInstrumentSelectItem | null = null
    
    // 优先使用selectedItems数据
    if (selectedItems && selectedItems.length === 3) {
      const [, , contract] = selectedItems
      instrumentData = contract.instrumentData
    } 
    // 从selectedValues数组恢复（使用最新的选择状态）
    else if (selectedValues.value && selectedValues.value.length >= 3) {
      const [exchangeId, productName, contractId] = selectedValues.value
      const key = `${exchangeId}_${productName}`
      const contracts = cachedData.value.contractsByProduct[key] || []
      const contract = contracts.find(c => c.value === contractId || c.label === contractId)
      
      if (contract) {
        instrumentData = contract.instrumentData
      }
    }
    // 最后尝试从value数组恢复
    else if (value && value.length >= 3) {
      const [exchangeId, productName, contractId] = value
      const key = `${exchangeId}_${productName}`
      const contracts = cachedData.value.contractsByProduct[key] || []
      const contract = contracts.find(c => c.value === contractId || c.label === contractId)
      
      if (contract) {
        instrumentData = contract.instrumentData
      }
    }
    
    if (instrumentData) {
      selectedInstrument.value = instrumentData
      updateDisplayValueFromInstrument(instrumentData)
      emit('update:modelValue', instrumentData.id)
      emit('change', instrumentData.id, instrumentData)
    } else {
      // 清空选择
      selectedInstrument.value = null
      currentDisplayValue.value = ''
      emit('update:modelValue', 0)
      emit('change', 0, null)
    }
  } catch (error) {
    console.error('确认选择时出错:', error)
    uni.showToast({ title: '选择失败，请重试', icon: 'error' })
  }
}

// 处理取消
function handleCancel() {
  // 取消时不需要特殊处理
}

// 清空选择
function handleClear() {
  selectedValues.value = []
  selectedInstrument.value = null
  currentDisplayValue.value = ''
  // 重置columns为只有交易所列表
  columns.value = cachedData.value.exchanges.length > 0 
    ? [cachedData.value.exchanges] 
    : [[]]
  emit('update:modelValue', 0)
  emit('change', 0, null)
}

// 自定义显示格式
function displayFormat(selectedItems) {
  if (currentDisplayValue.value) {
    return currentDisplayValue.value
  }
  if (selectedItems && selectedItems.length === 3) {
    const exchange = selectedItems[0]?.label || selectedItems[0]?.value || ''
    const contract = selectedItems[2]?.label || selectedItems[2]?.value || ''
    return exchange && contract ? `${exchange}.${contract}` : ''
  }
  // 如果selectedItems不完整，尝试从selectedValues构建显示文本
  if (selectedValues.value && selectedValues.value.length >= 2) {
    const exchangeId = selectedValues.value[0]
    const contractId = selectedValues.value[2]
    if (exchangeId && contractId) {
      const exchangeName = ExchangeMap[exchangeId] || exchangeId
      return `${exchangeName}.${contractId}`
    }
  }
  return ''
}

// 组件挂载时初始化数据
onMounted(async () => {
  // 确保columns有初始值，防止渲染错误
  columns.value = [[]]
  
  try {
    await initializeData()
  } catch (error) {
    console.error('数据初始化失败:', error)
    // 初始化失败时显示重试选项
    uni.showModal({
      title: '加载失败',
      content: '数据加载失败，请检查网络连接后重试',
      showCancel: true,
      confirmText: '重试',
      success: (res) => {
        if (res.confirm) {
          initializeData()
        }
      }
    })
  }
})
</script>

<template>
  <wd-col-picker
    v-model="selectedValues"
    v-model:show="showPicker"
    :label="props.label"
    :title="props.placeholder"
    :columns="safeColumns"
    :display-format="displayFormat"
    :loading="isLoading"
    :disabled="props.disabled"
    :clearable="props.clearable"
    popup-title="选择期货合约"
    :column-change="handleColumnChange"
    @confirm="handleConfirm"
    @cancel="handleCancel"
    @clear="handleClear"
    :custom-class="props.customClass"
    :custom-label-class="props.customLabelClass"
    :custom-value-class="props.customValueClass"
  />
</template>

<style lang="scss">

</style>

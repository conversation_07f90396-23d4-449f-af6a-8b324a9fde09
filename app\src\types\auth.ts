/**
 * 认证相关类型定义
 * 统一管理所有登录、认证相关的接口和类型
 */

/**
 * 发送登录验证码请求
 */
export interface ISendCodeRequest {
  phone: string
}

/**
 * 手机号登录请求
 */
export interface IPhoneLoginRequest {
  phone: string
  code: string
}

/**
 * 用户名密码登录请求
 */
export interface IUsernameLoginRequest {
  username: string
  password: string
  captcha?: string
  captchaId?: string
}

/**
 * 微信登录请求
 */
export interface IWechatLoginRequest {
  code: string
}

/**
 * 图形验证码响应
 */
export interface ICaptchaResponse {
  captchaId: string
  picPath: string
  captchaLength: number
  openCaptcha: boolean
}

/**
 * 获取验证码（旧版兼容）
 * @deprecated 使用 ICaptchaResponse 代替
 */
export interface ICaptcha {
  captchaEnabled: boolean
  uuid: string
  image: string
}


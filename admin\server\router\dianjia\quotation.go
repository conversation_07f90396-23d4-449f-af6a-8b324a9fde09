package dianjia

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type QuotationRouter struct{}

// InitQuotationRouter 初始化报价路由信息
func (s *QuotationRouter) InitQuotationRouter(Router *gin.RouterGroup) {
	// 需要操作记录的路由组（写操作）
	quotationRouter := Router.Group("api/v1/dianjia/quotations").Use(middleware.OperationRecord())

	// 不需要操作记录的路由组（读操作）
	quotationRouterWithoutRecord := Router.Group("api/v1/dianjia/quotations")

	// 我的报价相关路由
	myQuotationRouterWithoutRecord := Router.Group("api/v1/dianjia/my-quotations")

	quotationApi := v1.ApiGroupApp.DianjiaApiGroup.QuotationApi

	// 公开的报价相关路由（无需认证）
	{
		quotationRouterWithoutRecord.GET("", quotationApi.GetPublicQuotationList)         // 获取公开报价列表
		quotationRouterWithoutRecord.GET(":quotationId", quotationApi.GetQuotationDetail) // 获取报价详情
	}

	// 需要认证的报价管理路由
	{
		// 基础CRUD操作
		quotationRouter.POST("", quotationApi.CreateQuotation)               // 创建报价
		quotationRouter.PUT(":quotationId", quotationApi.UpdateQuotation)    // 更新报价
		quotationRouter.DELETE(":quotationId", quotationApi.DeleteQuotation) // 删除报价

		// 状态管理操作
		quotationRouter.POST(":quotationId/publish", quotationApi.PublishQuotation)     // 发布报价
		quotationRouter.POST(":quotationId/toggle", quotationApi.ToggleQuotationStatus) // 切换报价状态
	}

	// 我的报价相关路由（需要认证）
	{
		myQuotationRouterWithoutRecord.GET("", quotationApi.GetMyQuotationList) // 获取我的报价列表
	}
}

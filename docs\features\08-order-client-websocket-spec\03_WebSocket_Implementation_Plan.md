# WebSocket客户端实现详细开发计划

## 1. 文档信息
- **文档版本**: v2.0
- **创建日期**: 2025-01-26
- **更新日期**: 2025-01-26
- **文档类型**: 开发实施计划
- **目标受众**: 开发团队、AI开发助手
- **依赖文档**: 
  - `01_Feature_Overview_and_Data_Model.md`
  - `02_Connection_Management_and_Interaction.md`
  - `/order/docs/Order客户端软件开发规格说明书.md`
  - 服务端实现代码: `admin/server/ws_hub/order_hub.go`

## 2. 项目概述

### 2.1 实施目标
基于现有的登录认证系统和服务端WebSocket实现，完成下单客户端的WebSocket通信功能，确保与业务服务器建立稳定可靠的实时通信链路。

### 2.2 核心特性
- **连接唯一性**: 同一期货账户只允许一个活跃连接，与服务端`OrderHub`保持一致
- **Token认证**: 基于现有`LoginManager`和`TokenManager`的JWT Token认证
- **心跳机制**: 30秒心跳间隔，45秒超时断开，对应服务端的pingPeriod/pongWait配置
- **订单处理**: 完整的订单接收-确认-执行-反馈闭环，对应服务端的`OrderMessageEnvelope`格式
- **自动重连**: 网络断开时的指数退避重连策略
- **事件驱动**: 完全集成vnpy EventEngine事件系统

### 2.3 技术约束
- **框架依赖**: 基于现有的vnpy EventEngine事件系统
- **认证集成**: 与已实现的`LoginManager`和`TokenManager`无缝集成
- **消息格式**: 严格按照服务端`OrderMessageEnvelope`的JSON消息格式
- **线程安全**: 确保WebSocket线程与UI线程的安全通信
- **与服务端对齐**: 消息类型和处理逻辑与`admin/server/ws_hub/order_hub.go`保持一致

## 3. 架构设计

### 3.1 模块结构
```
order/
├── websocket/
│   ├── __init__.py
│   ├── client.py              # WebSocket客户端核心
│   ├── manager.py             # WebSocket连接管理器
│   ├── handlers/              # 消息处理器
│   │   ├── __init__.py
│   │   ├── base_handler.py    # 基础消息处理器
│   │   ├── auth_handler.py    # 认证消息处理器
│   │   ├── heartbeat_handler.py # 心跳消息处理器
│   │   ├── order_handler.py   # 订单消息处理器
│   │   └── conflict_handler.py # 冲突处理器
│   ├── models.py              # WebSocket数据模型
│   ├── events.py              # WebSocket事件定义
│   ├── exceptions.py          # WebSocket异常定义
│   └── utils.py               # 工具函数
├── config/
│   └── websocket_config.yaml  # WebSocket配置文件
└── tests/
    └── test_websocket/        # WebSocket测试文件
        ├── test_client.py
        ├── test_manager.py
        └── test_handlers.py
```

### 3.2 核心类设计

#### 3.2.1 WebSocketClient - 核心客户端
```python
class WebSocketClient:
    """WebSocket客户端核心类，对应服务端OrderClient"""
    
    def __init__(self, event_engine: EventEngine, config: WSConfig):
        self.event_engine = event_engine
        self.config = config
        self.websocket = None
        self.is_connected = False
        self.is_authenticated = False
        self.client_id = None  # 对应服务端OrderClient.clientID
        self.subscriptions = {}  # 对应服务端OrderClient.subscriptions
        self.message_handlers = {}
        self.heartbeat_task = None
        self.reconnect_task = None
        self.send_queue = asyncio.Queue()  # 对应服务端OrderClient.send
        
    async def connect(self, server_url: str, account_id: str, token: str) -> bool
    async def disconnect(self) -> None
    async def send_message(self, message: dict) -> bool
    async def _handle_message(self, message: str) -> None
    async def _start_heartbeat(self) -> None
    async def _send_heartbeat(self) -> None
    async def _handle_reconnect(self) -> None
```

#### 3.2.2 WebSocketManager - 连接管理器
```python
class WebSocketManager:
    """WebSocket连接管理器，与LoginManager集成"""
    
    def __init__(self, event_engine: EventEngine, login_manager: LoginManager):
        self.event_engine = event_engine
        self.login_manager = login_manager
        self.client = None
        self.config = WSConfig()
        self.connection_state = ConnectionState.DISCONNECTED
        self.retry_count = 0
        self.max_retries = 3
        
        # 注册LoginManager事件监听
        self._setup_auth_event_listeners()
        
    async def connect_with_auth(self, server_url: str, account_id: str) -> bool
    async def disconnect(self) -> None
    def get_connection_state(self) -> ConnectionState
    async def force_disconnect_old_connection(self, conn_id: str) -> bool
    def register_message_handler(self, msg_type: str, handler: MessageHandler) -> None
    
    def _setup_auth_event_listeners(self):
        """设置认证事件监听器"""
        self.event_engine.register(EVENT_USER_LOGIN_SUCCESS, self._on_user_login)
        self.event_engine.register(EVENT_USER_LOGOUT, self._on_user_logout)
        self.event_engine.register(EVENT_TOKEN_REFRESHED, self._on_token_refreshed)
```

#### 3.2.3 消息处理器架构
```python
class BaseMessageHandler:
    """基础消息处理器"""
    
    def __init__(self, event_engine: EventEngine):
        self.event_engine = event_engine
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def handle(self, message: dict, client: WebSocketClient) -> bool:
        """处理消息的抽象方法"""
        raise NotImplementedError
    
    def validate_message(self, message: dict) -> bool:
        """验证消息格式，对应服务端OrderMessageEnvelope结构"""
        required_fields = ["event", "timestamp", "source"]
        return all(field in message for field in required_fields)

class AuthHandler(BaseMessageHandler):
    """认证消息处理器，处理register/register_response"""
    async def handle(self, message: dict, client: WebSocketClient) -> bool

class HeartbeatHandler(BaseMessageHandler):
    """心跳消息处理器，处理ping/pong"""
    async def handle(self, message: dict, client: WebSocketClient) -> bool

class OrderHandler(BaseMessageHandler):
    """订单消息处理器，处理order_update等"""
    async def handle(self, message: dict, client: WebSocketClient) -> bool

class ConflictHandler(BaseMessageHandler):
    """连接冲突处理器"""
    async def handle(self, message: dict, client: WebSocketClient) -> bool
```

### 3.3 数据模型定义

#### 3.3.1 WebSocket消息模型（匹配服务端OrderMessageEnvelope）
```python
@dataclass
class WSMessageEnvelope:
    """WebSocket消息基类，对应服务端OrderMessageEnvelope"""
    event: str                          # 对应服务端Event字段
    payload: Optional[Dict[str, Any]]   # 对应服务端Payload字段
    seq: Optional[int] = None          # 对应服务端Seq字段
    timestamp: int = field(default_factory=lambda: int(time.time() * 1000))
    source: str = "order_client"       # 标识消息来源
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        
    @classmethod
    def from_json(cls, json_str: str) -> 'WSMessageEnvelope':
        """从JSON字符串创建消息"""

@dataclass
class RegisterMessage(WSMessageEnvelope):
    """客户端注册消息，对应服务端register事件"""
    def __init__(self, client_id: str):
        super().__init__(
            event="register",
            payload={"clientID": client_id}
        )

@dataclass
class PingMessage(WSMessageEnvelope):
    """心跳请求消息，对应服务端ping事件"""
    def __init__(self):
        super().__init__(
            event="ping",
            payload={}
        )

@dataclass
class OrderUpdateMessage(WSMessageEnvelope):
    """订单更新消息，对应服务端order_update事件"""
    def __init__(self, order_data: dict):
        super().__init__(
            event="order_update",
            payload=order_data
        )

@dataclass
class SubscribeMessage(WSMessageEnvelope):
    """订阅消息，对应服务端subscribe_orders事件"""
    def __init__(self, channel: str):
        super().__init__(
            event="subscribe_orders",
            payload={"channel": channel}
        )
```

#### 3.3.2 连接状态模型
```python
class ConnectionState(Enum):
    """连接状态枚举"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    AUTHENTICATING = "authenticating"
    AUTHENTICATED = "authenticated"
    CONFLICT = "conflict"
    ERROR = "error"
    RECONNECTING = "reconnecting"

@dataclass
class ConnectionInfo:
    """连接信息"""
    client_id: Optional[str] = None
    account_id: Optional[str] = None
    server_url: Optional[str] = None
    connect_time: Optional[datetime] = None
    last_heartbeat: Optional[datetime] = None
    state: ConnectionState = ConnectionState.DISCONNECTED
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            "client_id": self.client_id,
            "account_id": self.account_id, 
            "server_url": self.server_url,
            "connect_time": self.connect_time.isoformat() if self.connect_time else None,
            "last_heartbeat": self.last_heartbeat.isoformat() if self.last_heartbeat else None,
            "state": self.state.value
        }
```

### 3.4 事件系统集成

#### 3.4.1 WebSocket事件定义
```python
# WebSocket连接事件
EVENT_WS_CONNECTING = "websocket.connecting"
EVENT_WS_CONNECTED = "websocket.connected"
EVENT_WS_AUTHENTICATED = "websocket.authenticated"
EVENT_WS_DISCONNECTED = "websocket.disconnected"
EVENT_WS_ERROR = "websocket.error"
EVENT_WS_CONFLICT = "websocket.conflict"

# WebSocket消息事件
EVENT_WS_MESSAGE_RECEIVED = "websocket.message.received"
EVENT_WS_MESSAGE_SENT = "websocket.message.sent"
EVENT_WS_HEARTBEAT = "websocket.heartbeat"

# WebSocket订单事件
EVENT_WS_ORDER_RECEIVED = "websocket.order.received"
EVENT_WS_ORDER_ACK_SENT = "websocket.order.ack_sent"
EVENT_WS_ORDER_RESPONSE_SENT = "websocket.order.response_sent"

# WebSocket重连事件
EVENT_WS_RECONNECT_START = "websocket.reconnect.start"
EVENT_WS_RECONNECT_SUCCESS = "websocket.reconnect.success"
EVENT_WS_RECONNECT_FAILED = "websocket.reconnect.failed"
```

#### 3.4.2 与现有登录系统完全集成
- 监听`EVENT_USER_LOGIN_SUCCESS`事件，自动建立WebSocket连接
- 监听`EVENT_USER_LOGOUT`事件，自动断开WebSocket连接
- 监听`EVENT_TOKEN_REFRESHED`事件，更新WebSocket认证
- 通过`LoginManager.set_websocket_client()`实现双向绑定

## 4. 详细实施计划

### 4.1 第一阶段：基础架构搭建 (2天)

#### 任务列表：
1. **创建模块结构** (0.3天)
   - 创建websocket包目录结构
   - 创建基础__init__.py文件
   - 设置模块导入路径

2. **实现数据模型** (0.8天)
   - 实现WSMessageEnvelope基类，严格对应服务端OrderMessageEnvelope
   - 实现所有消息子类（RegisterMessage, PingMessage等）
   - 实现ConnectionState和ConnectionInfo
   - 实现消息序列化和反序列化
   - 添加数据验证逻辑

3. **事件系统集成** (0.4天)
   - 定义WebSocket相关事件常量
   - 创建events.py文件
   - 与现有EventEngine集成测试

4. **配置管理** (0.3天)
   - 创建websocket_config.yaml配置文件
   - 实现WSConfig配置类
   - 支持开发/生产环境配置

5. **异常处理框架** (0.2天)
   - 定义WebSocket专用异常类
   - 实现异常装饰器
   - 建立错误码体系

**交付物：**
- 完整的websocket模块结构
- 与服务端对齐的数据模型和事件定义
- 配置管理系统
- 基础异常处理框架

### 4.2 第二阶段：WebSocket客户端核心 (3天)

#### 任务列表：
1. **WebSocketClient基础实现** (1.5天)
   - 实现基础连接/断开功能
   - 实现消息发送/接收机制，对应服务端OrderClient
   - 添加连接状态管理
   - 实现基础错误处理
   - 集成asyncio.Queue发送队列

2. **消息处理器框架** (0.8天)
   - 实现BaseMessageHandler基类
   - 实现消息路由和分发机制，对应服务端handleMessageForOrder
   - 添加消息处理器注册功能
   - 实现消息验证逻辑

3. **认证集成** (0.7天)
   - 实现与LoginManager的无缝集成
   - 集成现有TokenManager
   - 处理Token过期和刷新
   - 实现认证状态同步

**交付物：**
- 基础WebSocket客户端
- 消息处理框架
- 与认证系统的完整集成

### 4.3 第三阶段：连接管理和心跳机制 (2天)

#### 任务列表：
1. **WebSocketManager实现** (1天)
   - 实现连接生命周期管理
   - 添加连接状态监控
   - 实现连接配置管理
   - 集成事件系统
   - 绑定LoginManager事件监听

2. **心跳机制** (0.7天)
   - 实现HeartbeatHandler，对应服务端ping/pong处理
   - 添加心跳定时器（30秒间隔）
   - 实现服务器时间同步
   - 处理心跳超时

3. **自动重连机制** (0.3天)
   - 实现指数退避重连策略
   - 添加重连次数限制
   - 实现连接健康检查
   - 处理重连事件通知

**交付物：**
- 完整的连接管理系统
- 稳定的心跳机制
- 自动重连功能

### 4.4 第四阶段：冲突处理和强制下线 (1.5天)

#### 任务列表：
1. **冲突检测** (0.8天)
   - 实现ConflictHandler
   - 处理服务端的冲突通知
   - 实现冲突信息展示
   - 添加用户确认机制

2. **强制下线功能** (0.7天)
   - 实现强制断开请求发送
   - 处理强制下线响应
   - 实现旧连接断开逻辑
   - 新连接状态转换

**交付物：**
- 冲突检测和处理机制
- 强制下线功能

### 4.5 第五阶段：订单处理系统 (3天)

#### 任务列表：
1. **订单消息处理** (1.5天)
   - 实现OrderHandler，对应服务端order_update处理
   - 处理订单相关消息
   - 实现幂等性检查
   - 添加订单参数验证

2. **订阅机制** (0.8天)
   - 实现subscribe_orders/unsubscribe_orders
   - 处理订阅确认
   - 实现订阅状态跟踪

3. **业务逻辑集成** (0.7天)
   - 与vnpy网关接口集成
   - 处理执行结果收集
   - 实现执行状态更新

**交付物：**
- 完整的订单处理系统
- 订阅管理机制
- vnpy网关集成

### 4.6 第六阶段：UI集成和状态显示 (1.5天)

#### 任务列表：
1. **状态指示器扩展** (0.8天)
   - 扩展现有StatusIndicator
   - 添加WebSocket连接状态
   - 实现状态图标和文字
   - 添加状态变化动画

2. **主窗口集成** (0.7天)
   - 在主窗口添加WebSocket状态
   - 实现连接控制按钮
   - 添加冲突处理对话框
   - 实现状态事件响应

**交付物：**
- WebSocket状态显示
- UI控制界面
- 用户交互功能

### 4.7 第七阶段：测试和优化 (2天)

#### 任务列表：
1. **单元测试** (0.8天)
   - WebSocketClient测试
   - 消息处理器测试
   - 数据模型测试
   - 异常处理测试

2. **集成测试** (0.7天)
   - 与登录系统集成测试
   - 端到端连接测试
   - 订单处理流程测试
   - 冲突处理测试

3. **性能和稳定性测试** (0.5天)
   - 长时间连接稳定性测试
   - 网络异常处理测试
   - 内存泄露检查
   - 性能优化

**交付物：**
- 完整的测试套件
- 性能和稳定性报告
- 优化建议和实施

## 5. 技术实现细节

### 5.1 消息协议实现（与服务端对齐）

#### 5.1.1 连接建立流程
```python
async def connect_with_auth(self, server_url: str, account_id: str) -> bool:
    """带认证的连接建立，对应服务端ServeWsForOrder"""
    try:
        # 1. 获取当前有效token
        token = await self.login_manager.get_current_token()
        if not token:
            raise AuthenticationError("没有有效的登录token")
        
        # 2. 建立WebSocket连接
        self.client = WebSocketClient(self.event_engine, self.config)
        connected = await self.client.connect(server_url, account_id, token)
        
        if connected:
            # 3. 发送注册请求，对应服务端register事件处理
            register_msg = RegisterMessage(client_id=account_id)
            await self.client.send_message(register_msg.to_dict())
            
            # 4. 等待注册响应，对应服务端register_response
            response = await self._wait_for_register_response()
            
            if response and response.get("success"):
                self.connection_state = ConnectionState.AUTHENTICATED
                self.event_engine.put(Event(EVENT_WS_AUTHENTICATED, response))
                return True
            else:
                await self.client.disconnect()
                return False
        
        return False
        
    except Exception as e:
        self.logger.error(f"连接失败: {str(e)}")
        return False
```

#### 5.1.2 心跳机制实现（与服务端对齐）
```python
async def _start_heartbeat(self):
    """启动心跳机制，对应服务端ping/pong处理"""
    while self.is_connected:
        try:
            # 发送心跳请求，对应服务端ping事件
            ping_msg = PingMessage()
            await self.send_message(ping_msg.to_dict())
            
            # 等待30秒，对应服务端pingPeriod
            await asyncio.sleep(30)
            
        except Exception as e:
            self.logger.error(f"心跳发送失败: {str(e)}")
            break

class HeartbeatHandler(BaseMessageHandler):
    """心跳消息处理器，对应服务端pong处理"""
    
    async def handle(self, message: dict, client: WebSocketClient) -> bool:
        if message.get("event") == "pong":
            # 更新最后心跳时间
            client.last_heartbeat = datetime.now()
            
            # 发送心跳事件
            self.event_engine.put(Event(EVENT_WS_HEARTBEAT, {
                "timestamp": message.get("timestamp"),
                "source": message.get("source")
            }))
            
            return True
        return False
```

#### 5.1.3 订单处理实现（与服务端对齐）
```python
class OrderHandler(BaseMessageHandler):
    """订单消息处理器，对应服务端order_update等事件处理"""
    
    def __init__(self, event_engine: EventEngine):
        super().__init__(event_engine)
        self.processed_orders = set()  # 用于幂等性检查
    
    async def handle(self, message: dict, client: WebSocketClient) -> bool:
        event = message.get("event")
        
        if event == "order_update":
            return await self._handle_order_update(message, client)
        elif event == "market_data":
            return await self._handle_market_data(message, client)
        
        return False
    
    async def _handle_order_update(self, message: dict, client: WebSocketClient) -> bool:
        try:
            payload = message.get("payload", {})
            order_id = payload.get("order_id")
            
            if not order_id:
                self.logger.warning("订单更新消息缺少order_id")
                return False
            
            # 幂等性检查
            if order_id in self.processed_orders:
                self.logger.warning(f"重复的订单更新: {order_id}")
                return True
            
            # 记录已处理的订单
            self.processed_orders.add(order_id)
            
            # 发送订单事件给业务层处理
            self.event_engine.put(Event(EVENT_WS_ORDER_RECEIVED, payload))
            
            self.logger.info(f"处理订单更新: {order_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"处理订单更新失败: {str(e)}")
            return False
```

### 5.2 与现有系统集成

#### 5.2.1 LoginManager集成扩展
```python
# 在LoginManager中添加WebSocket集成
class LoginManager:
    def __init__(self, event_engine: EventEngine, ...):
        # ... 现有代码 ...
        self.websocket_manager = None
    
    def set_websocket_manager(self, websocket_manager):
        """设置WebSocket管理器"""
        self.websocket_manager = websocket_manager
    
    async def login_with_password(self, username: str, password: str, ...) -> LoginResult:
        # ... 现有登录逻辑 ...
        
        if result.success and self.websocket_manager:
            # 登录成功后自动建立WebSocket连接
            account_id = result.user_info.get("account_id") or username
            if account_id:
                await self.websocket_manager.connect_with_auth(
                    self.config.websocket_url, 
                    account_id
                )
        
        return result
    
    def logout(self):
        # ... 现有登出逻辑 ...
        
        if self.websocket_manager:
            # 登出时断开WebSocket连接
            asyncio.create_task(self.websocket_manager.disconnect())
    
    async def get_current_token(self) -> Optional[str]:
        """获取当前有效的访问令牌"""
        token_data = self.token_manager.load_token()
        if token_data and not token_data.is_expired():
            return token_data.access_token
        return None
```

#### 5.2.2 完整的事件监听机制
```python
class WebSocketManager:
    def _setup_auth_event_listeners(self):
        """设置认证事件监听器"""
        self.event_engine.register(EVENT_USER_LOGIN_SUCCESS, self._on_user_login)
        self.event_engine.register(EVENT_USER_LOGOUT, self._on_user_logout)
        self.event_engine.register(EVENT_TOKEN_REFRESHED, self._on_token_refreshed)
    
    def _on_user_login(self, event: Event):
        """处理用户登录成功事件"""
        token_data = event.data
        if token_data and hasattr(token_data, 'user_info'):
            account_id = token_data.user_info.get("account_id")
            if account_id:
                asyncio.create_task(
                    self.connect_with_auth(self.config.server_url, account_id)
                )
    
    def _on_user_logout(self, event: Event):
        """处理用户登出事件"""
        asyncio.create_task(self.disconnect())
    
    def _on_token_refreshed(self, event: Event):
        """处理Token刷新事件"""
        token_data = event.data
        if self.client and self.client.is_authenticated:
            # 更新WebSocket认证状态
            asyncio.create_task(self._update_auth_status(token_data.access_token))
```

### 5.3 配置管理

#### 5.3.1 配置文件示例
```yaml
# config/websocket_config.yaml
websocket:
  # 服务器配置
  server:
    url: "ws://localhost:8000/ws/order"  # 对应服务端路由
    timeout: 30
    max_retries: 3
    retry_interval: 5  # 重连间隔（秒）
    
  # 心跳配置（与服务端对齐）
  heartbeat:
    interval: 30  # 心跳间隔（秒），对应服务端pingPeriod
    timeout: 45   # 心跳超时（秒），对应服务端pongWait
    
  # 消息配置
  message:
    max_size: 1048576  # 最大消息大小（1MB），对应服务端maxMessageSize
    encoding: "utf-8"
    
  # 重连配置
  reconnect:
    enabled: true
    max_attempts: 10
    initial_delay: 1
    max_delay: 60
    backoff_factor: 2
    
  # 订单配置
  order:
    ack_timeout: 5    # ACK超时（秒）
    max_retries: 3    # 最大重试次数
    response_timeout: 30  # 响应超时（秒）

# 开发环境配置
development:
  websocket:
    server:
      url: "ws://localhost:8000/ws/order"
    heartbeat:
      interval: 10  # 开发环境心跳更频繁

# 生产环境配置
production:
  websocket:
    server:
      url: "wss://api.production.com/ws/order"
    heartbeat:
      interval: 30
```

## 6. 测试策略

### 6.1 单元测试计划

#### 6.1.1 WebSocketClient测试
```python
class TestWebSocketClient(unittest.TestCase):
    def setUp(self):
        self.event_engine = EventEngine()
        self.config = WSConfig()
        self.client = WebSocketClient(self.event_engine, self.config)
    
    async def test_connect_success(self):
        """测试连接成功"""
        with patch('websockets.connect') as mock_connect:
            mock_ws = AsyncMock()
            mock_connect.return_value = mock_ws
            
            result = await self.client.connect("ws://localhost:8000", "account1", "token123")
            self.assertTrue(result)
            self.assertTrue(self.client.is_connected)
    
    async def test_message_envelope_format(self):
        """测试消息格式是否符合服务端OrderMessageEnvelope"""
        ping_msg = PingMessage()
        msg_dict = ping_msg.to_dict()
        
        # 验证必需字段
        self.assertIn("event", msg_dict)
        self.assertIn("timestamp", msg_dict)
        self.assertIn("source", msg_dict)
        self.assertEqual(msg_dict["event"], "ping")
        self.assertEqual(msg_dict["source"], "order_client")
```

#### 6.1.2 消息处理器测试
```python
class TestOrderHandler(unittest.TestCase):
    def setUp(self):
        self.event_engine = EventEngine()
        self.handler = OrderHandler(self.event_engine)
        self.client = Mock()
    
    async def test_order_update_handling(self):
        """测试订单更新处理"""
        order_message = {
            "event": "order_update",
            "payload": {
                "order_id": "ORD001",
                "symbol": "RB2501",
                "status": "filled"
            },
            "timestamp": int(time.time() * 1000),
            "source": "order_hub"
        }
        
        result = await self.handler.handle(order_message, self.client)
        self.assertTrue(result)
```

### 6.2 集成测试计划

#### 6.2.1 端到端连接测试
```python
class TestWebSocketIntegration(unittest.TestCase):
    async def test_full_connection_flow(self):
        """测试完整连接流程"""
        # 1. 模拟登录获取token
        # 2. 建立WebSocket连接
        # 3. 发送注册请求
        # 4. 验证连接状态
        # 5. 心跳测试
        # 6. 断开连接
        pass
    
    async def test_auth_integration(self):
        """测试与认证系统的集成"""
        # 1. 验证LoginManager事件监听
        # 2. 测试自动连接/断开
        # 3. 测试Token刷新处理
        pass
```

## 7. 风险评估和应对策略

### 7.1 技术风险

#### 7.1.1 服务端兼容性风险
**风险描述**: 客户端实现与服务端OrderHub不兼容
**影响程度**: 高
**应对策略**:
- 严格按照服务端OrderMessageEnvelope格式实现
- 与服务端开发人员密切协作
- 建立全面的集成测试

#### 7.1.2 认证集成风险
**风险描述**: 与现有LoginManager集成出现问题
**影响程度**: 中
**应对策略**:
- 充分理解现有认证流程
- 实现松耦合的集成方式
- 添加详细的错误处理和日志

### 7.2 业务风险

#### 7.2.1 消息处理风险
**风险描述**: 消息处理异常导致交易中断
**影响程度**: 高
**应对策略**:
- 实现完整的错误处理机制
- 添加消息重试和补偿
- 建立消息审计日志

## 8. 项目交付标准

### 8.1 代码质量标准
- 代码覆盖率 ≥ 80%
- 类型提示覆盖率 ≥ 90%
- 代码复杂度符合PEP8标准
- 通过静态代码分析工具检查
- 与服务端消息格式100%兼容

### 8.2 性能标准
- 连接建立时间 ≤ 3秒
- 消息处理延迟 ≤ 100ms
- 心跳响应时间 ≤ 500ms
- 内存使用 ≤ 100MB

### 8.3 集成标准
- 与LoginManager无缝集成
- 与TokenManager无缝集成
- 与vnpy EventEngine完全兼容
- 24小时连续运行无崩溃

## 9. 项目时间表

### 9.1 总体时间安排
- **项目总时长**: 15天
- **开发时间**: 13天
- **测试时间**: 1.5天
- **文档整理**: 0.5天

### 9.2 里程碑计划
- **第1周末**: 完成基础架构和WebSocket客户端核心
- **第2周**: 完成所有核心功能开发和UI集成
- **第3周**: 完成测试、优化和文档，项目交付

### 9.3 关键节点
- **第2天**: 基础架构搭建完成
- **第5天**: WebSocket客户端核心完成
- **第7天**: 连接管理和心跳机制完成
- **第9天**: 冲突处理完成
- **第12天**: 订单处理系统完成
- **第13.5天**: UI集成完成
- **第15天**: 项目交付

## 10. 总结

本修订版开发计划在原计划基础上，深度分析了现有服务端WebSocket实现和认证系统，确保客户端实现与服务端完全对齐。重点改进包括：

1. **与服务端对齐**: 严格按照服务端OrderMessageEnvelope格式实现消息模型
2. **认证系统集成**: 与现有LoginManager和TokenManager无缝集成
3. **事件驱动设计**: 充分利用vnpy EventEngine实现松耦合架构
4. **简化实施流程**: 压缩开发周期至15天，提高开发效率

项目成功交付后，将为期货交易系统提供稳定可靠的实时通信能力，支持多账户管理和高频交易需求。

---

**审核要求**:
请仔细审核本修订版开发计划，重点关注：
1. 与服务端实现的兼容性和一致性
2. 与现有认证系统的集成方案
3. 开发时间安排的合理性
4. 技术方案的可行性和完整性
5. 测试计划的全面性

审核通过后即可开始实施开发工作。
"""
服务器管理器模块
负责期货公司服务器的选择、测速和优选逻辑
"""
import os
import concurrent.futures
from typing import Dict, List, Optional
from utils.broker_parser import BrokerParser, Broker
from utils.network_utils import ping_ip_port, batch_ping_addresses_sync, calculate_average_speed


class ServerManager:
    """服务器管理器，负责期货公司服务器的智能优选"""

    def __init__(self, broker_xml_path: str = None):
        """
        初始化服务器管理器
        
        Args:
            broker_xml_path: broker.xml文件路径，如果为None则使用默认路径
        """
        if broker_xml_path is None:
            script_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            broker_xml_path = os.path.join(script_dir, "broker.xml")
        print("broker.xml path:", broker_xml_path)
        self.broker_xml_path = broker_xml_path
        self.parser = None
        self.best_broker_info = None
        self._initialize_parser()

    def _initialize_parser(self):
        """初始化broker解析器"""
        try:
            self.parser = BrokerParser(self.broker_xml_path)
            print(f"成功加载期货公司配置文件: {self.broker_xml_path}")
            print(f"共加载 {len(self.parser.brokers)} 个期货公司")
        except Exception as e:
            raise Exception(f"初始化broker解析器失败: {str(e)}")

    def get_all_brokers(self) -> List[Broker]:
        """获取所有期货公司信息"""
        if not self.parser:
            return []
        return self.parser.get_all_brokers()

    def get_broker_by_id(self, broker_id: str) -> Optional[Broker]:
        """通过ID获取期货公司信息"""
        if not self.parser:
            return None
        return self.parser.get_broker_by_id(broker_id)

    def _test_broker_speed(self, broker: Broker) -> Dict[str, float]:
        """
        测试单个期货公司所有服务器的速度（合并所有server的MarketData地址）
        
        Args:
            broker: 期货公司对象
            
        Returns:
            dict: 地址到延迟时间的映射
        """
        all_addresses = []
        server_info = []
        for server_name, server in broker.servers.items():
            if server.market_data_items:
                all_addresses.extend(server.market_data_items)
                server_info.append(f"{server_name}({len(server.market_data_items)}个地址)")
        
        if not all_addresses:
            print("  没有找到行情地址")
            return {}
        
        print(f"[DIAG] _test_broker_speed: 将要测试的地址: {all_addresses}")
        print(f"  收集到 {len(all_addresses)} 个行情地址，来自服务器: {', '.join(server_info)}")
        
        speed_results = batch_ping_addresses_sync(all_addresses)
        print(f"[DIAG] _test_broker_speed: batch_ping_addresses_sync 返回的结果: {speed_results}")
        
        if speed_results:
            avg_speed = calculate_average_speed(speed_results)
            print(f"  整体平均延迟: {avg_speed:.2f}ms，可用地址: {len(speed_results)}/{len(all_addresses)}")
        else:
            print("  所有地址都无法连接")
        
        return speed_results

    def _test_single_broker(self, broker: Broker) -> Optional[Dict]:
        """
        测试单个期货公司的速度，返回结果字典或None

        Args:
            broker: 期货公司对象

        Returns:
            dict: 包含测试结果的字典，如果测试失败则返回None
        """
        print(f"\n测试期货公司: {broker.broker_name} ({broker.broker_id})")

        try:
            speed_results = self._test_broker_speed(broker)

            if speed_results:
                avg_speed = calculate_average_speed(speed_results)
                return {
                    "broker_id": broker.broker_id,
                    "broker_name": broker.broker_name,
                    "server_name": "全部服务器",
                    "addresses": list(speed_results.keys()),
                    "speed_results": speed_results,
                    "avg_speed": avg_speed
                }
            else:
                print(f"  {broker.broker_name}: 所有地址都无法连接")
                return None

        except Exception as e:
            print(f"  {broker.broker_name}: 测试失败: {str(e)}")
            return None

    def select_best_broker_concurrent(self, broker_ids: List[str] = None,
                                    exclude_broker_ids: List[str] = None,
                                    max_workers: int = None) -> Optional[Dict]:
        """
        并发选择最佳的期货公司服务器（推荐使用此方法）

        Args:
            broker_ids: 指定测试的期货公司ID列表，如果为None则测试所有
            exclude_broker_ids: 排除的期货公司ID列表
            max_workers: 最大并发线程数，如果为None则自动设置

        Returns:
            dict: 最佳服务器信息，包含broker_id, broker_name, server_name, addresses, avg_speed
        """
        if not self.parser:
            print("错误: broker解析器未初始化")
            return None
        print("broker_ids:", broker_ids)

        if broker_ids:
            brokers_to_test = []
            for broker_id in broker_ids:
                broker = self.parser.get_broker_by_id(broker_id)
                print("broker:",broker)
                if broker:
                    brokers_to_test.append(broker)
        else:
            brokers_to_test = self.parser.get_all_brokers()

        if exclude_broker_ids:
            brokers_to_test = [b for b in brokers_to_test if b.broker_id not in exclude_broker_ids]

        if not brokers_to_test:
            print("错误: 没有可测试的期货公司")
            return None

        print(f"[DIAG] select_best_broker_concurrent: 待测试的期货公司: {[b.broker_name for b in brokers_to_test]}")
        print(f"开始并发测试 {len(brokers_to_test)} 个期货公司的服务器速度...")

        # 设置合理的并发数，避免过多线程
        if max_workers is None:
            max_workers = min(len(brokers_to_test), 10)  # 最多10个并发

        best_result = None
        best_avg_speed = float('inf')

        # 使用线程池并发测试所有期货公司
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有测试任务
            future_to_broker = {
                executor.submit(self._test_single_broker, broker): broker
                for broker in brokers_to_test
            }

            # 收集结果
            for future in concurrent.futures.as_completed(future_to_broker):
                broker = future_to_broker[future]
                try:
                    result = future.result()
                    if result and result['avg_speed'] < best_avg_speed:
                        best_avg_speed = result['avg_speed']
                        best_result = result
                except Exception as e:
                    print(f"  {broker.broker_name}: 并发测试异常: {str(e)}")

        if best_result:
            print(f"\n=== 最佳服务器选择结果 ===")
            print(f"期货公司: {best_result['broker_name']} ({best_result['broker_id']})")
            print(f"服务器: {best_result['server_name']}")
            print(f"平均延迟: {best_result['avg_speed']:.2f}ms")
            print(f"可用地址数: {len(best_result['addresses'])}")
            print(f"地址列表: {best_result['addresses'][:3]}{'...' if len(best_result['addresses']) > 3 else ''}")

            self.best_broker_info = best_result
            return best_result
        else:
            print("\n未找到可用的期货公司服务器")
            return None

    def select_best_broker(self, broker_ids: List[str] = None,
                                exclude_broker_ids: List[str] = None) -> Optional[Dict]:
        """
        选择最佳的期货公司服务器（串行版本，兼容性保留）

        注意：推荐使用 select_best_broker_concurrent() 方法以获得更好的性能

        Args:
            broker_ids: 指定测试的期货公司ID列表，如果为None则测试所有
            exclude_broker_ids: 排除的期货公司ID列表

        Returns:
            dict: 最佳服务器信息，包含broker_id, broker_name, server_name, addresses, avg_speed
        """
        print("提示：正在使用串行版本，推荐使用 select_best_broker_concurrent() 获得更好性能")
        return self.select_best_broker_concurrent(broker_ids, exclude_broker_ids)

    def get_best_broker_info(self) -> Optional[Dict]:
        """获取缓存的最佳期货公司信息"""
        return self.best_broker_info

    def get_market_data_addresses(self, broker_id: str = None, server_name: str = None) -> List[str]:
        """
        获取行情数据地址
        
        Args:
            broker_id: 期货公司ID，如果为None则使用最佳期货公司
            server_name: 服务器名称，如果为None则使用最佳服务器
            
        Returns:
            list: 行情数据地址列表
        """
        if not self.parser:
            return []
        
        if broker_id is None and self.best_broker_info:
            return self.best_broker_info.get("addresses", [])
        
        if broker_id is None:
            print("错误: 未指定期货公司ID且没有缓存的最佳结果")
            return []
        
        broker = self.parser.get_broker_by_id(broker_id)
        if not broker:
            print(f"错误: 未找到期货公司 {broker_id}")
            return []
        
        if server_name:
            server = broker.servers.get(server_name)
            if server:
                return server.market_data_items
            else:
                print(f"错误: 期货公司 {broker_id} 中未找到服务器 {server_name}")
                return []
        else:
            all_addresses = []
            for server in broker.servers.values():
                if server.market_data_items:
                    all_addresses.extend(server.market_data_items)
            return list(dict.fromkeys(all_addresses))

    def health_check(self) -> bool:
        """
        同步健康检查，验证当前最佳服务器是否仍然可用
        
        Returns:
            bool: True表示健康，False表示需要重新选择
        """
        if not self.best_broker_info:
            return False
        
        addresses = self.best_broker_info.get("addresses", [])
        if not addresses:
            return False
        
        try:
            speed_results = batch_ping_addresses_sync(addresses[:3])
            if speed_results:
                avg_speed = calculate_average_speed(speed_results)
                original_speed = self.best_broker_info.get("avg_speed", 0)
                if avg_speed > original_speed * 2:
                    print(f"健康检查失败: 当前延迟 {avg_speed:.2f}ms 超过原始延迟 {original_speed:.2f}ms 的2倍")
                    return False
                return True
            else:
                return False
        except Exception as e:
            print(f"健康检查异常: {str(e)}")
            return False

if __name__ == "__main__":
    manager = ServerManager()
    # 使用并发版本进行测试
    best_info = manager.select_best_broker_concurrent()
    print(best_info)

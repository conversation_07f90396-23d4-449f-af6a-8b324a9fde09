"""
Order客户端登录功能使用示例

演示如何使用登录系统的各个组件。
"""

import asyncio
import logging
import sys
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

from vnpy.event import EventEngine, Event
from auth import (
    LoginManager, TokenManager, AuthService, 
    LoginCredentials, EVENT_USER_LOGIN_SUCCESS, EVENT_USER_LOGIN_FAILED
)


class LoginExample:
    """登录功能使用示例"""
    
    def __init__(self):
        # 初始化事件引擎
        self.event_engine = EventEngine()
        
        # 初始化登录管理器
        self.login_manager = LoginManager(self.event_engine)
        
        # 设置日志
        self.setup_logging()
        
        # 注册事件监听
        self.setup_event_handlers()
        
        self.logger = logging.getLogger(__name__)
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('login_example.log', encoding='utf-8')
            ]
        )
    
    def setup_event_handlers(self):
        """设置事件处理器"""
        self.event_engine.register(EVENT_USER_LOGIN_SUCCESS, self.on_login_success)
        self.event_engine.register(EVENT_USER_LOGIN_FAILED, self.on_login_failed)
    
    def on_login_success(self, event: Event):
        """处理登录成功事件"""
        token_data = event.data
        self.logger.info(f"登录成功事件: 用户 {token_data.user_info.get('username', 'Unknown')}")
    
    def on_login_failed(self, event: Event):
        """处理登录失败事件"""
        login_result = event.data
        self.logger.error(f"登录失败事件: {login_result.message}")
    
    async def example_password_login(self):
        """示例：用户名密码登录"""
        self.logger.info("=== 用户名密码登录示例 ===")
        
        # 模拟用户输入
        username = "testuser"
        password = "testpass123"
        
        try:
            result = await self.login_manager.login_with_password(
                username=username,
                password=password,
                remember_me=True
            )
            
            if result.success:
                self.logger.info(f"登录成功: {result.message}")
                self.logger.info(f"用户信息: {result.user_info}")
            else:
                self.logger.error(f"登录失败: {result.message}")
                
        except Exception as e:
            self.logger.error(f"登录异常: {str(e)}")
    
    async def example_phone_login(self):
        """示例：手机验证码登录"""
        self.logger.info("=== 手机验证码登录示例 ===")
        
        # 模拟用户输入
        phone = "13800138000"
        sms_code = "123456"
        
        try:
            # 先发送验证码
            sms_sent = await self.login_manager.auth_service.send_sms_code(phone)
            if sms_sent:
                self.logger.info("验证码发送成功")
            else:
                self.logger.warning("验证码发送失败")
            
            # 登录
            result = await self.login_manager.login_with_phone(
                phone=phone,
                sms_code=sms_code,
                remember_me=True
            )
            
            if result.success:
                self.logger.info(f"登录成功: {result.message}")
                self.logger.info(f"用户信息: {result.user_info}")
            else:
                self.logger.error(f"登录失败: {result.message}")
                
        except Exception as e:
            self.logger.error(f"登录异常: {str(e)}")
    
    async def example_auto_login(self):
        """示例：自动登录"""
        self.logger.info("=== 自动登录示例 ===")
        
        try:
            result = await self.login_manager.auto_login()
            
            if result.success:
                self.logger.info(f"自动登录成功: {result.message}")
                self.logger.info(f"用户信息: {result.user_info}")
            else:
                self.logger.error(f"自动登录失败: {result.message}")
                
        except Exception as e:
            self.logger.error(f"自动登录异常: {str(e)}")
    
    def example_token_management(self):
        """示例：Token管理"""
        self.logger.info("=== Token管理示例 ===")
        
        token_manager = TokenManager()
        
        # 获取Token信息
        token_info = token_manager.get_token_info()
        if token_info:
            self.logger.info(f"Token文件信息: {token_info}")
        else:
            self.logger.info("没有找到Token文件")
        
        # 加载Token
        token_data = token_manager.load_token()
        if token_data:
            self.logger.info(f"加载Token成功: 用户 {token_data.user_info.get('username', 'Unknown')}")
            self.logger.info(f"Token过期时间: {token_data.expires_at}")
            self.logger.info(f"Token是否有效: {token_manager.is_token_valid(token_data)}")
        else:
            self.logger.info("没有找到有效的Token")
    
    def example_logout(self):
        """示例：登出"""
        self.logger.info("=== 登出示例 ===")
        
        if self.login_manager.is_logged_in():
            self.login_manager.logout()
            self.logger.info("登出成功")
        else:
            self.logger.info("用户未登录")
    
    async def run_examples(self):
        """运行所有示例"""
        self.logger.info("开始运行登录功能示例")
        
        # 启动事件引擎
        self.event_engine.start()
        
        try:
            # 1. 自动登录示例
            await self.example_auto_login()
            await asyncio.sleep(1)
            
            # 2. 用户名密码登录示例
            await self.example_password_login()
            await asyncio.sleep(1)
            
            # 3. Token管理示例
            self.example_token_management()
            await asyncio.sleep(1)
            
            # 4. 手机验证码登录示例
            await self.example_phone_login()
            await asyncio.sleep(1)
            
            # 5. 登出示例
            self.example_logout()
            await asyncio.sleep(1)
            
        finally:
            # 停止事件引擎
            self.event_engine.stop()
            
            # 关闭认证服务
            await self.login_manager.auth_service.close()
        
        self.logger.info("登录功能示例运行完成")


async def main():
    """主函数"""
    example = LoginExample()
    await example.run_examples()


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())

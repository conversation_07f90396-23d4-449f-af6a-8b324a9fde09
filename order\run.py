"""
Order客户端启动脚本

提供多种启动方式的便捷脚本。
"""

import sys
import argparse
import logging
from pathlib import Path


def setup_path():
    """设置Python路径"""
    current_dir = Path(__file__).parent
    if str(current_dir) not in sys.path:
        sys.path.insert(0, str(current_dir))


def run_main():
    """运行主程序"""
    setup_path()
    from main import main
    return main()


def run_login_test():
    """运行登录测试"""
    setup_path()
    from order.tests.test_login import main
    return main()


def run_example():
    """运行使用示例"""
    setup_path()
    from order.tests.example_usage import main
    return main()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Order客户端启动脚本")
    parser.add_argument(
        "mode", 
        choices=["main", "test", "example"],
        help="运行模式：main=主程序, test=登录测试, example=使用示例"
    )
    parser.add_argument(
        "--debug", 
        action="store_true",
        help="启用调试模式"
    )
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 根据模式运行
    if args.mode == "main":
        print("启动Order客户端主程序...")
        return run_main()
    elif args.mode == "test":
        print("启动登录功能测试...")
        return run_login_test()
    elif args.mode == "example":
        print("启动使用示例...")
        return run_example()
    else:
        print(f"未知模式：{args.mode}")
        return 1


if __name__ == "__main__":
    sys.exit(main())

package dianjia

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type AuthService struct{}

var AuthServiceApp = new(AuthService)

const (
	VerifyCodeExpiration = 5 * time.Minute // 验证码有效期5分钟
	VerifyCodeLength     = 6               // 验证码长度
)

// SendLoginCode 发送登录验证码
func (authService *AuthService) SendLoginCode(phone string) error {
	// 生成6位数验证码
	code := generateVerifyCode()
	
	// 将验证码存入Redis，键名格式：login_code:手机号
	key := fmt.Sprintf("login_code:%s", phone)
	ctx := context.Background()
	
	err := global.GVA_REDIS.Set(ctx, key, code, VerifyCodeExpiration).Err()
	if err != nil {
		global.GVA_LOG.Error("验证码存储失败", zap.Error(err))
		return errors.New("验证码发送失败")
	}
	
	// TODO: 集成短信服务发送验证码
	// 现在只是打印到日志，实际项目中需要调用短信API
	global.GVA_LOG.Info("发送验证码", zap.String("phone", phone), zap.String("code", code))
	
	return nil
}

// VerifyLoginCode 验证登录验证码
func (authService *AuthService) VerifyLoginCode(phone, code string) error {
	key := fmt.Sprintf("login_code:%s", phone)
	ctx := context.Background()
	
	storedCode, err := global.GVA_REDIS.Get(ctx, key).Result()
	if err != nil {
		if err.Error() == "redis: nil" {
			return errors.New("验证码已过期或不存在")
		}
		return errors.New("验证码验证失败")
	}
	
	if storedCode != code {
		return errors.New("验证码错误")
	}
	
	// 验证成功后删除验证码
	global.GVA_REDIS.Del(ctx, key)
	
	return nil
}

// LoginByPhone 手机号登录/注册
func (authService *AuthService) LoginByPhone(phone, code string) (*system.SysUser, error) {
	// 验证验证码
	if err := authService.VerifyLoginCode(phone, code); err != nil {
		return nil, err
	}
	
	// 查找用户
	var user system.SysUser
	err := global.GVA_DB.Where("phone = ?", phone).First(&user).Error
	
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 用户不存在，自动注册
			return authService.registerUserByPhone(phone)
		}
		global.GVA_LOG.Error("查询用户失败", zap.Error(err))
		return nil, errors.New("登录失败")
	}
	
	// 检查用户是否被冻结
	if user.Enable != 1 {
		return nil, errors.New("账户已被冻结")
	}
	
	return &user, nil
}

// registerUserByPhone 通过手机号自动注册用户
func (authService *AuthService) registerUserByPhone(phone string) (*system.SysUser, error) {
	// 密码默认为手机号后六位
	defaultPassword := phone[len(phone)-6:]
	
	user := system.SysUser{
		UUID:        uuid.New(),
		Username:    phone, // 使用手机号作为用户名
		Phone:       phone,
		NickName:    "用户" + phone[len(phone)-4:], // 使用手机号后4位作为昵称
		Password:    utils.BcryptHash(defaultPassword), // 密码默认为手机号后六位
		AuthorityId: 888,                               // 默认用户角色ID
		Enable:      1,                                 // 启用状态
	}
	
	err := global.GVA_DB.Create(&user).Error
	if err != nil {
		global.GVA_LOG.Error("用户注册失败", zap.Error(err))
		return nil, errors.New("注册失败")
	}
	
	global.GVA_LOG.Info("新用户自动注册成功", zap.String("phone", phone), zap.Uint("userId", user.ID))
	
	return &user, nil
}

// generateVerifyCode 生成验证码
func generateVerifyCode() string {
	rand.Seed(time.Now().UnixNano())
	code := rand.Intn(1000000)
	return fmt.Sprintf("%06d", code) // 确保是6位数，不足前面补0
}

// GetUserProfile 获取用户资料
func (authService *AuthService) GetUserProfile(userUUID uuid.UUID) (*system.SysUser, error) {
	var user system.SysUser
	err := global.GVA_DB.Where("uuid = ?", userUUID).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// UpdateUserProfile 更新用户资料
func (authService *AuthService) UpdateUserProfile(userUUID uuid.UUID, updateData system.SysUser) error {
	// 只更新允许的字段
	updates := map[string]interface{}{
		"nick_name":       updateData.NickName,
		"header_img":      updateData.HeaderImg,
		"company_name":    updateData.CompanyName,
		"company_org_id":  updateData.CompanyOrgId,
		"company_address": updateData.CompanyAddress,
	}
	
	err := global.GVA_DB.Model(&system.SysUser{}).Where("uuid = ?", userUUID).Updates(updates).Error
	if err != nil {
		global.GVA_LOG.Error("更新用户资料失败", zap.Error(err))
		return errors.New("更新失败")
	}
	
	return nil
}

// LoginByUsernamePassword 用户名密码登录
func (authService *AuthService) LoginByUsernamePassword(username, password string) (*system.SysUser, error) {
	var user system.SysUser
	err := global.GVA_DB.Where("username = ?", username).First(&user).Error
	
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户名不存在")
		}
		global.GVA_LOG.Error("查询用户失败", zap.Error(err))
		return nil, errors.New("登录失败")
	}
	
	// 检查用户是否被冻结
	if user.Enable != 1 {
		return nil, errors.New("账户已被冻结")
	}
	
	// 验证密码
	if !utils.BcryptCheck(password, user.Password) {
		return nil, errors.New("密码错误")
	}
	
	return &user, nil
}

// LoginByWechat 微信登录/注册
func (authService *AuthService) LoginByWechat(code string) (*system.SysUser, error) {
	// TODO: 调用微信API获取openid和unionid
	// 这里暂时模拟，实际项目中需要调用微信API
	openID := "mock_openid_" + code
	unionID := "mock_unionid_" + code
	
	// 先尝试通过unionid查找用户
	var user system.SysUser
	err := global.GVA_DB.Where("wechat_union_id = ?", unionID).First(&user).Error
	
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 用户不存在，自动注册
			return authService.registerUserByWechat(openID, unionID)
		}
		global.GVA_LOG.Error("查询用户失败", zap.Error(err))
		return nil, errors.New("登录失败")
	}
	
	// 检查用户是否被冻结
	if user.Enable != 1 {
		return nil, errors.New("账户已被冻结")
	}
	
	return &user, nil
}

// registerUserByWechat 通过微信自动注册用户
func (authService *AuthService) registerUserByWechat(openID, unionID string) (*system.SysUser, error) {
	user := system.SysUser{
		UUID:           uuid.New(),
		Username:       openID,                     // 使用openid作为用户名
		NickName:       "微信用户",                    // 微信昵称，可以从微信API获取
		WechatOpenid:   openID,
		WechatUnionId:  unionID,
		Password:       utils.BcryptHash("123456"), // 默认密码
		AuthorityId:    888,                        // 默认用户角色ID
		Enable:         1,                          // 启用状态
	}
	
	err := global.GVA_DB.Create(&user).Error
	if err != nil {
		global.GVA_LOG.Error("微信用户注册失败", zap.Error(err))
		return nil, errors.New("注册失败")
	}
	
	global.GVA_LOG.Info("新微信用户自动注册成功", zap.String("openid", openID), zap.Uint("userId", user.ID))
	
	return &user, nil
}
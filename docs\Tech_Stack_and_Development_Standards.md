# 技术栈与开发规范说明

**版本:** 1.0
**日期:** 2025年7月21日

---

## 1. 项目概述

本项目是一个前后端分离的综合业务平台，包含三大部分：

1.  **`admin` 目录**: 负责后端服务 (`server`) 和后台管理界面 (`web`)。
    -   `server`: 提供核心业务逻辑、数据接口和第三方服务集成。
    -   `web`: 供管理员使用的后台管理前端界面。
2.  **`app` 目录**: 负责直接面向用户的跨端应用程序，基于 `uni-app` 开发，可编译为H5、小程序、App等多个平台。
3.  **`hq` 目录**: 负责行情数据的采集与服务，基于 `vn.py` 开发。
4.  **`trade` 目录**: 负责本地交易终端，基于 `vn.py` 开发。

---

## 2. 技术栈选型

### 2.1. 后端 (`admin/server`)

| 技术 | 名称/版本 | 说明 |
|---|---|---|
| **语言** | Go | 主要开发语言。 |
| **Web框架** | Gin | 高性能Web框架，用于构建RESTful API。 |
| **数据库** | MySQL | 主要业务关系型数据库，使用 `InnoDB` 引擎。 |
| **时序数据库** | TDengine | 用于存储和查询海量行情时序数据。 |
| **缓存** | Redis | 用于缓存业务数据、JWT管理，并存储实时行情Tick数据供前端使用。 |
| **ORM** | GORM | 数据库操作工具，简化数据库交互。 |
| **配置管理** | Viper | 用于管理 `config.yaml` 配置文件。 |
| **日志** | Zap | 高性能日志库，用于记录应用日志。 |
| **API文档** | Swagger | 自动生成并维护API接口文档。 |

### 2.2. 后台管理前端 (`admin/web`)

| 技术 | 名称/版本 | 说明 |
|---|---|---|
| **框架** | Vue 3 | 主要前端框架。 |
| **UI库** | Element Plus | 提供丰富的后台UI组件。 |
| **构建工具** | Vite | 提供快速的开发服务器和打包体验。 |
| **状态管理** | Pinia | Vue 3官方推荐的状态管理库。 |

### 2.3. 用户端 (`app`)

| 技术 | 名称/版本 | 说明 |
|---|---|---|
| **框架** | uni-app | 基于 Vue 3 的跨端开发框架。 |
| **开发语言** | TypeScript | 为项目提供类型安全。 |
| **UI样式** | UnoCSS | 原子化CSS框架，实现快速、灵活的样式开发。 |
| **状态管理** | Pinia | 统一的状态管理方案。 |
| **路由** | 约定式路由 | 基于文件的路由系统，由 `vite-plugin-uni-pages` 插件管理。 |
| **包管理器**| pnpm | 高效的包管理工具。 |

### 2.4. 行情服务 (`hq`)

| 技术 | 名称/版本 | 说明 |
|---|---|---|
| **语言** | Python | 主要开发语言。 |
| **核心框架** | vn.py | 用于对接期货行情接口，实现数据的订阅、合成与存储。 |
| **数据库** | TDengine, Redis | 分别用于时序数据持久化和实时Tick数据缓存。 |

### 2.5. 本地交易终端 (下单模块)

| 技术 | 名称/版本 | 说明 |
|---|---|---|
| **语言** | Python | 主要开发语言。 |
| **核心框架** | vn.py | 用于对接期货交易柜台（如CTP），执行下单、撤单等操作。 |
| **本地数据库** | SQLite | 用于存储本地配置、交易日志、缓存数据等。 |
| **通信协议** | WebSocket | 用于与业务服务器进行实时双向通信，接收指令并回报状态。 |

---

## 3. 开发规范与约束

### 3.1. 通用规范

1.  **代码风格**: 
    -   所有前端代码（Vue, TS, JS）提交前必须通过 `ESLint` 和 `Prettier` 的格式化检查。
    -   后端Go代码遵循 `gofmt` 和 `goimports` 标准。
2.  **Git提交规范**:
    -   严格遵守 `Conventional Commits` 规范（约定式提交）。
    -   提交信息格式为：`type(scope): subject`，例如 `feat(用户): 新增用户注册功能`。
    -   使用 `.commitlintrc.cjs` 中定义的规则进行校验。
3.  **依赖管理**:
    -   项目统一使用 `pnpm` 作为包管理器，请勿使用 `npm` 或 `yarn`。

### 3.2. 后端开发规范 (`admin/server`)

1.  **目录结构**: 遵循现有 `gin-vue-admin` 的标准目录结构。
    -   `api`: API接口层，负责请求的接收和响应。
    -   `service`: 服务逻辑层，处理核心业务逻辑。
    -   `model`: 数据模型层，定义数据库表结构和请求/响应结构体。
    -   `router`: 路由层，定义API路由规则。
    -   `config`: 存放配置文件对应的结构体。
    -   `global`: 全局变量和对象。
    -   `initialize`: 初始化各项服务和连接。
2.  **API设计**: 
    -   所有API必须在 `router` 目录中注册，并遵循 `RESTful` 设计风格。
    -   API路径统一使用小写，并采用中划线 `-` 分隔（如 `/user-info`）。
    -   所有API都应在 `docs` 目录下通过Swagger注解生成清晰的文档。
3.  **数据库**: 
    -   数据库迁移和表结构变更应通过 `GORM` 的 `AutoMigrate` 功能管理，或编写独立的迁移脚本。
    -   禁止直接在代码中拼接SQL语句，必须使用GORM进行操作，以防止SQL注入。
4.  **配置**: 所有配置项（数据库、Redis、JWT等）均在 `config.yaml` 文件中统一管理，禁止硬编码。
5.  **错误处理**: 
    -   业务逻辑中的错误必须返回，并逐层向上传递，最终在 `api` 层统一处理成标准JSON格式返回给前端。
    -   使用 `zap` 记录详细的错误日志，方便问题排查。

### 3.3. 用户端开发规范 (`app`)

1.  **跨端兼容性**: 
    -   核心要求：所有组件和页面必须保证在 **H5、微信小程序、iOS App、Android App** 四个主要平台上的表现和功能一致。
    -   条件编译：仅在必要时使用条件编译（`#ifdef` / `#endif`）处理平台差异。
    -   API使用：优先使用 `uni-app` 提供的跨端API，避免直接调用特定平台的原生API。
2.  **响应式布局与自适应**: 
    -   **必须** 使用 `rpx` 作为主要尺寸单位，以确保在不同尺寸的屏幕上（手机、Pad、PC H5）都能良好自适应。
    -   使用 `UnoCSS` 的响应式断点功能处理复杂的布局适配。
    -   禁止使用固定的像素（`px`）单位，除非有特殊且明确的理由。
3.  **组件化**: 
    -   可复用的UI和业务逻辑应封装成组件，存放在 `src/components` 目录下。
    -   遵循 Vue 3 的 `Composition API` (`<script setup>`) 风格编写组件。
4.  **状态管理**: 全局状态、用户登录信息等应通过 `Pinia` 进行管理，存放于 `src/store` 目录。
5.  **路由与页面**: 
    -   页面存放于 `src/pages`（主包）和 `src/pages-sub`（分包）目录下。
    -   路由为约定式，由目录结构自动生成，页面配置（如导航栏标题、布局等）在 `.vue` 文件的 `<route>` 块中定义。
6.  **静态资源**: 图片等静态资源统一存放于 `src/static` 目录，并使用绝对路径 `/static/...` 引用。

---

## 4. 环境与部署

-   **开发环境**: 开发者应使用 `pnpm dev:*` 命令启动对应平台的开发环境。
-   **生产构建**: 使用 `pnpm build:*` 命令进行生产环境的打包。
-   **CI/CD**: 项目已配置GitHub Actions，可实现自动化测试、构建和部署，具体流程见 `.github/workflows` 目录。

---

## 5. AI 辅助开发流程规范

为了提升开发效率和代码质量，我们鼓励在开发流程中合理使用AI辅助工具。以下是针对AI辅助开发的流程规范：

### 5.1. 核心原则

1.  **开发者主导**: AI是辅助工具，开发者始终是代码的最终负责人。**严禁直接复制粘贴未经审查和测试的AI生成代码**。
2.  **遵循规范**: 所有通过AI生成的代码，必须严格遵守本项目既定的代码风格、架构和开发规范。
3.  **安全第一**: 严禁向AI工具提供任何敏感信息，如代码库私有访问权限、API密钥、数据库凭证、用户个人数据等。
4.  **小步快跑**: 将复杂任务分解为小块，分步向AI提问，更容易获得准确、高质量的输出。

### 5.2. 推荐使用场景

-   **代码生成与补全**:
    -   根据注释或函数签名生成函数体。
    -   快速生成重复性、模板化的代码（如API的CRUD、表单、表格列配置等）。
    -   使用 `admin` 后台的自动化代码生成器（AutoCode）快速创建模块基础代码。
-   **代码解释与文档**:
    -   解释不熟悉的函数、类或代码块的用途和逻辑。
    -   为已有代码生成注释或Markdown格式的文档。
-   **重构与优化**:
    -   提出代码重构建议，以提高可读性、性能或遵循更优的设计模式。
    -   识别并修复潜在的性能瓶颈。
-   **测试用例生成**:
    -   根据函数或组件的功能，生成单元测试或组件测试的初始框架和测试用例。
-   **Bug修复**:
    -   粘贴错误信息或有问题的代码片段，请求AI分析原因并提供修复建议。

### 5.3. 开发工作流

1.  **明确需求 (Define)**:
    -   在请求AI辅助前，清晰地定义你的目标。例如：“为用户模型创建一个Go函数，通过ID查询用户信息，并处理未找到的情况。”

2.  **提供上下文 (Contextualize)**:
    -   向AI提供必要的上下文信息，这至关重要。
    -   **代码上下文**: 粘贴相关的代码片段，如模型定义（Go struct, TypeScript interface）、已有的函数、组件结构等。
    -   **规范上下文**: 在提示词中明确指出需要遵循的规范，例如：“请使用GORM编写查询”、“请为Vue 3 `<script setup>` 语法生成代码”、“请确保代码符合ESLint规则”。
    -   **示例**:
        ```
        // 好的例子
        "这是我的用户模型定义：[粘贴struct代码]。请为它编写一个GORM的Create方法，并遵循我们的错误处理规范：[粘贴错误处理代码示例]。"

        // 坏的例子
        "写个创建用户的函数。"
        ```

3.  **生成与审查 (Generate & Review)**:
    -   获取AI生成的代码。
    -   **代码审查是强制步骤**:
        -   **逻辑正确性**: 代码是否能正确实现需求？是否存在边界情况未处理？
        -   **规范符合性**: 是否遵循了本项目的代码风格、命名约定和目录结构？
        -   **安全性**: 是否引入了任何安全漏洞（如SQL注入、XSS等）？是否有可能泄露敏感数据？
        -   **效率**: 是否存在明显的性能问题？

4.  **集成与测试 (Integrate & Test)**:
    -   将审查通过的代码集成到项目中。
    -   **本地测试**: 运行项目，手动测试新功能，确保其按预期工作。
    -   **单元/集成测试**: 如果适用，为新代码编写或补充单元测试，并运行整个测试套件。

5.  **迭代优化 (Iterate)**:
    -   如果AI生成的代码不完美，不要直接放弃。可以基于它的输出进行修改，或向AI提供更精确的反馈以获得更好的结果。例如：“这段代码可以工作，但请使用我们项目中的 `response.OkWithData` 函数来返回结果。”

### 5.4. 提示词（Prompt）技巧

-   **角色扮演**: 让AI扮演特定角色。例如：“你是一位经验丰富的Go后端工程师...”、“你是一位精通uni-app和Vue 3的专家...”。
-   **明确指令**: 使用清晰、直接的动词。例如：“重构以下代码...”、“为这个函数编写单元测试...”、“解释这段代码的作用...”。
-   **提供正/反例**: 如果可能，提供一个好的代码示例和一个不好的代码示例，帮助AI理解你的具体要求。
-   **追问**: 如果第一次生成的结果不理想，进行追问和澄清，逐步引导AI给出满意的答案。

通过遵循以上规范，我们可以最大限度地发挥AI在软件开发中的潜力，同时确保项目的质量、安全和可维护性。

---

## 6. 服务端代码结构

为保持项目结构清晰、可维护性高，所有服务端业务代码需遵循以下约定：

- **API 层:** 所有业务相关的 API 接口代码，统一存放于 `server/api/v1/dianjia/` 目录下。
- **Model 层:** 数据库模型及结构体定义，统一存放于 `server/model/dianjia/` 目录下。
- **Service 层:** 核心业务逻辑处理，统一存放于 `server/service/dianjia/` 目录下。
- **Router 层:** 路由注册与中间件配置，统一存放于 `server/router/dianjia/` 目录下。

**总结:** 所有新增的业务模块代码，必须按其功能（api, model, service, router）分别归入 `server/*/dianjia/` 对应的子目录中。任何需要在上述指定位置之外新建或修改代码的操作，必须经过项目负责人（用户）的明确同意。

---

## 7. 项目最佳开发工作流程实践

本章结合《产品需求规格说明书（PRD）》、《CLAUDE.md》及本规范，给出本项目推荐的全流程开发最佳实践，适用于大宗商品交易风险管理平台。

### 7.1 需求确定与任务拆解

1. **需求梳理**
   - 以PRD为唯一需求源，逐条梳理功能（FR）和非功能需求（NFR）。
   - 组织产品、研发、测试三方评审，确保每条需求都“可实现、可测试、可验收”。
   - 对每个需求补充验收标准和边界条件，形成需求基线。
2. **任务拆解与排期**
   - 按PRD优先级（如P1先于P2）将需求拆分为可交付的开发任务（Story/Issue）。
   - 每个任务需明确目标、输入/输出、负责人、预计工时、验收标准。
   - 建议使用项目管理工具（如GitHub Projects、Jira等）进行可视化追踪。

### 7.2 分支管理与Git最佳实践

1. **分支模型推荐：Git Flow（简化版）**
   - `main`：始终保持可部署、稳定的生产分支。
   - `develop`：日常开发集成分支，所有新功能、修复先合并到此分支。
   - `feature/xxx`：每个新功能/需求单独建分支，命名如`feature/报价管理`。
   - `fix/xxx`：每个bug修复单独建分支，命名如`fix/点价接口异常`。
   - `release/x.y.z`：发布前合并测试、修复，准备上线。
   - `hotfix/x.y.z`：线上紧急修复。
2. **分支操作规范**
   - 所有开发均基于`develop`分支拉取新分支。
   - 功能开发完成后，提交PR到`develop`，需至少1人Code Review。
   - 发布前将`develop`合并到`release`，测试通过后再合并到`main`并打Tag。
   - 线上紧急修复直接基于`main`拉`hotfix`，修复后合并回`main`和`develop`。
3. **提交规范**
   - 严格遵循Conventional Commits（见本规范3.1），如：
     `feat(报价): 新增卖方在线报价功能`
     `fix(点价): 修复点价锁价时价格未锁定bug`
   - 每次提交前本地运行`lint`和`type-check`，保证代码质量。

### 7.3 开发与代码规范

1. **目录结构与分层**
   - 后端：严格遵循`api/service/model/router`四层分离。
   - 前端：组件、页面、API、状态管理、工具函数分目录管理，保持高内聚低耦合。
2. **代码风格**
   - 前端：ESLint + Prettier自动格式化，TypeScript强类型。
   - 后端：gofmt/goimports自动格式化，结构体、接口、错误处理规范。
3. **AI辅助开发**
   - 仅用AI生成模板/重复性代码，所有AI代码必须人工审查、测试。

### 7.4 测试流程

1. **本地开发自测**
   - 后端：每个API需有单元测试（go test），关键业务逻辑需集成测试。
   - 前端：组件/页面需有基本的交互测试，建议引入自动化测试（如Vitest、Cypress）。
2. **集成测试**
   - 功能开发完成后，合并到`develop`分支，自动触发CI（见CLAUDE.md、.github/workflows）。
   - CI流程包括：依赖安装、lint检查、单元测试、构建、部署到测试环境。
3. **验收测试**
   - 测试环境由测试人员根据PRD验收标准逐条验证。
   - 验收通过后，方可合并到`release`/`main`分支。

### 7.5 部署与上线

1. **自动化部署**
   - 利用GitHub Actions实现CI/CD，自动化测试、构建、部署。
   - 生产环境部署前，需在`release`分支完成最终回归测试。
2. **回滚与监控**
   - 每次上线打Tag，便于回滚。
   - 生产环境需接入日志、监控、告警系统，及时发现和处理异常。

### 7.6 持续优化与迭代

1. **需求变更管理**
   - 任何需求变更需评审、记录，更新PRD和任务列表。
2. **定期回顾**
   - 每个迭代结束后，团队回顾流程、工具、代码质量，持续改进。

### 7.7 流程图示意

```mermaid
graph TD
A[需求评审] --> B[任务拆解]
B --> C[feature分支开发]
C --> D[本地自测]
D --> E[PR到develop]
E --> F[CI自动化测试]
F --> G[测试环境验收]
G --> H[release分支]
H --> I[生产环境部署]
I --> J[上线回顾与优化]
```

---

本流程充分结合了PRD、技术栈与开发规范、CLAUDE.md，强调了需求基线、分支管理、自动化测试、CI/CD、AI辅助开发的安全边界等关键点。这样既能保证开发效率，也能最大程度保障产品质量和可维护性。

# 公共报价市场功能测试指南

## 📋 功能概览

公共报价市场功能已完成开发，包含以下核心模块：

### 🎯 核心功能
- ✅ 报价创建与编辑
- ✅ 报价状态管理（草稿→激活→过期/撤回）
- ✅ 我的报价管理
- ✅ 公开市场浏览
- ✅ 报价详情查看（自适应显示）
- ✅ 定时过期任务

### 📁 文件结构
```
app/src/pages/quotes/
├── edit.vue          # 统一的创建/编辑页面
├── my-list.vue       # 我的报价列表
├── marketplace.vue   # 公开市场
├── detail.vue        # 报价详情（自适应）
└── README.md        # 本测试指南
```

## 🧪 测试流程

### 1. 启动应用
```bash
# 后端
cd admin/server
go run .

# 前端
cd app
pnpm dev
```

### 2. 功能测试步骤

#### 📝 创建报价测试
1. 访问 `/pages/quotes/edit`
2. 填写报价信息：
   - 报价标题：螺纹钢现货报价
   - 商品种类：螺纹钢
   - 交货地点：上海港
   - 价格类型：一口价/基差报价
   - 价格：3850.00
   - 有效期：3天内有效
3. 测试操作：
   - ✅ 保存草稿
   - ✅ 直接发布

#### 📋 我的报价管理测试
1. 访问 `/pages/quotes/my-list`
2. 测试筛选功能：
   - ✅ 全部报价
   - ✅ 有效报价（Active）
   - ✅ 无效报价（Draft, Expired, Withdrawn）
3. 测试操作按钮：
   - ✅ 编辑草稿
   - ✅ 发布草稿
   - ✅ 撤回激活报价
   - ✅ 删除草稿

#### 🏪 公开市场测试
1. 访问 `/pages/quotes/marketplace`
2. 测试搜索功能：
   - ✅ 关键词搜索
   - ✅ 商品种类筛选
   - ✅ 价格类型筛选
3. 测试报价卡片：
   - ✅ 基本信息显示
   - ✅ 剩余时间显示
   - ✅ 查看详情跳转

#### 📄 报价详情测试
1. 访问 `/pages/quotes/detail?id={报价ID}`
2. 测试自适应显示：
   - **发布者视角**：
     - ✅ 不显示发布者信息
     - ✅ 显示编辑按钮（草稿状态）
     - ✅ 显示发布/撤回/删除按钮
   - **公众视角**：
     - ✅ 显示发布者企业信息
     - ✅ 显示"发起点价"按钮
     - ✅ 隐藏管理操作按钮

### 3. 状态机测试

测试报价状态流转：
```
Draft (草稿) → [发布] → Active (激活)
Active → [撤回] → Withdrawn (已撤回)
Active → [过期] → Expired (已过期)
```

#### ⏰ 过期任务测试
1. 创建一个短期有效报价（1小时）
2. 等待定时任务执行（每小时触发）
3. 验证报价状态从 Active → Expired

## 🔧 技术特性

### 后端架构
- **数据模型**: `server/model/dianjia/quotation.go`
- **业务逻辑**: `server/service/dianjia/quotation.go`
- **API接口**: `server/api/v1/dianjia/quotation.go`
- **路由配置**: `server/router/dianjia/quotation.go`

### 前端架构
- **类型定义**: `app/src/types/quotation.ts`
- **API封装**: `app/src/api/quotation.ts`
- **页面组件**: `app/src/pages/quotes/`

### 核心特性
- 🔒 **权限控制**: 用户只能管理自己的报价
- 🎯 **状态机**: 严格的状态流转控制
- ⚡ **自适应UI**: 根据用户身份显示不同内容
- 🕒 **定时任务**: 自动处理过期报价
- 📱 **跨端兼容**: 支持H5、小程序、App

## 🚀 API接口

### 核心接口列表
```
POST   /api/v1/dianjia/quotations           # 创建报价
PUT    /api/v1/dianjia/quotations/{id}      # 更新报价
DELETE /api/v1/dianjia/quotations/{id}      # 删除报价
POST   /api/v1/dianjia/quotations/{id}/publish    # 发布报价
POST   /api/v1/dianjia/quotations/{id}/withdraw   # 撤回报价
GET    /api/v1/dianjia/quotations           # 公开报价列表
GET    /api/v1/dianjia/my-quotations        # 我的报价列表
GET    /api/v1/dianjia/quotations/{id}      # 报价详情
```

## 📊 数据库表结构

### dj_quotations 表
```sql
CREATE TABLE `dj_quotations` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '报价发布者ID',
  `title` varchar(255) NOT NULL COMMENT '报价标题',
  `commodity_id` bigint NOT NULL COMMENT '商品种类ID',
  `delivery_location` varchar(255) NOT NULL COMMENT '交货地点',
  `brand` varchar(100) COMMENT '品牌',
  `specifications` text COMMENT '规格说明',
  `description` text COMMENT '补充说明',
  `price_type` varchar(50) NOT NULL COMMENT '价格类型(Fixed/Basis)',
  `price` decimal(10,2) NOT NULL COMMENT '价格或基差值',
  `instrument_ref_id` bigint COMMENT '关联期货合约ID',
  `expires_at` timestamp NOT NULL COMMENT '过期时间',
  `status` varchar(50) NOT NULL DEFAULT 'Draft' COMMENT '状态',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 🐛 常见问题

### 1. UUID编译错误
如果遇到UUID比较错误，确保使用 `uuid.Nil` 进行零值比较。

### 2. 路由未注册
确保在 `initialize/router_biz.go` 中添加了报价路由注册：
```go
dianjiaRouter.InitQuotationRouter(privateGroup)
```

### 3. 前端组件问题
确保使用了正确的UI组件库（如 wd-button, wd-input 等）。

## 🎉 完成状态

- ✅ 后端完整四层架构
- ✅ 前端完整页面功能
- ✅ 状态机流转控制
- ✅ 权限安全验证
- ✅ 定时任务处理
- ✅ 自适应UI显示
- ✅ 跨端兼容性

功能已完整实现，可以开始业务使用和进一步优化！
# 03 - 交易请求与执行 (Trade Request & Execution) (V3)

## 1. 功能概述 (Functional Overview)

本功能详述了一笔**交易请求 (Trade Request)** 从点价方发起，到最终被执行或拒绝的完整生命周期。它涵盖了核心数据模型、系统执行逻辑、前端UI设计，以及与风险组件联动的多种执行流程。

**核心路径 (V3)**: 点价方直接发起交易意向，不指定具体合同。请求被审核通过后，由**系统自动匹配**一个或多个可用的合同进行履约。

---

## 2. 核心数据模型 (Core Data Models)

### 2.1. 交易请求表 (`trade_requests`)

这张表用于记录用户的每一次交易意图。

| 字段名 | 类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| id | BIGINT | PK, AI | 唯一ID |
| pricer_id | BIGINT | NOT NULL, FK | 点价方ID (发起者) |
| setter_id | BIGINT | NOT NULL, FK | 被点价方ID (对手方) |
| instrument_ref_id | BIGINT | NOT NULL, FK | 关联的期货合约ID |
| request_type | VARCHAR(50) | NOT NULL | 请求类型 (`PointPrice`: 点价, `BasisWash`: 洗基差) |
| requested_quantity | INT | NOT NULL | 用户请求的总数量 |
| requested_price | DECIMAL(10,2) | | 用户请求的价格（点价时） |
| executed_quantity | INT | NOT NULL, DEFAULT 0 | 已执行的数量 |
| **executed_price** | DECIMAL(10,2) | DEFAULT 0 | **加权平均执行价格** |
| **status** | VARCHAR(50) | NOT NULL | **请求状态** (见下方V4状态机) |
| **execution_mode** | VARCHAR(20) | NOT NULL | **执行模式** (`AUTOMATIC`, `MANUAL`, `SIMULATED`) |
| **rejection_reason** | TEXT | NULL | 拒绝理由 |
| **expires_at** | TIMESTAMP | NOT NULL | 请求的过期时间 |
| notes | TEXT | | 备注信息 |
| created_at | TIMESTAMP | | 创建时间 |
| updated_at | TIMESTAMP | | 更新时间 |

### 2.2. 执行明细表 (`execution_details`)

这张表变得至关重要，它记录了**一笔交易请求**是如何被**一个或多个合同**履约的。

| 字段名 | 类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| id | BIGINT | PK, AI | 唯一ID |
| trade_request_id | BIGINT | NOT NULL, FK | 关联的交易请求ID |
| contract_id | BIGINT | NOT NULL, FK | **用于履约的合同ID** |
| executed_quantity | INT | NOT NULL | 从该合同执行的数量 |
| executed_price | DECIMAL(10,2) | NOT NULL | 本次执行的成交价 |
| execution_type | VARCHAR(50) | NOT NULL | 执行类型 (`AUTOMATIC`, `MANUAL`, `SIMULATED`) |
| status | VARCHAR(50) | NOT NULL | 执行状态 (`Success`, `Failed`) |
| remarks | TEXT | | 执行备注 |
| created_at | TIMESTAMP | | 创建时间 |

---

## 3. 点价方UI与工作流 (Pricer's UI & Workflow)

### 3.1. 入口：合同中心 (`/pages/contract/pricer-list.vue`)
此页面是点价方浏览可用合同资源并發起交易的入口。用户在此选择一个资源组合（如“中粮集团”的“IF2506”合约），点击“去交易”进入交易执行页面。

### 3.2. 核心：交易执行页面 (`/pages/trade/execute.vue`)
此页面是点价方发起“点价”和“洗基差”交易请求的**统一操作界面**。
- **A. 上下文信息**: 显示交易对手和交易标的。
- **B. 合同资源汇总**: 显示可用于“点价”和“洗基差”的合同总剩余数量及加权均价/均基差。
- **C. 实时行情**: 通过WebSocket展示实时行情，并动态计算“点后价格”和“洗后价格”。
- **D. 统一操作区**: 用户输入数量，并通过“确认点价”或“确认洗基差”按钮发起交易请求。

---

## 4. 后端执行逻辑与状态机 (Backend Logic & State Machine) (V4 - Simplified Flow)

### 4.1. 核心原则 (V4)

1.  **立即执行**: 用户提交请求后，系统立即进行合同可用数量的匹配与冻结。成功后，请求直接进入 `Executing` 状态，不再需要“审核”步骤。
2.  **模式决定流程**: 请求的 `execution_mode` (由风险组件在创建时确定) 决定了 `Executing` 状态下的具体流程（人工、模拟或自动）。
3.  **增量式成交**: 多次部分成交会持续更新同一条 `TradeRequest` 记录的 `executed_quantity` 和 `executed_price`，而不会改变其状态，直到请求被完全满足或终止。
4.  **生命周期管理**: 请求有明确的有效期，通过 `expires_at` 字段控制，由后台定时任务负责清理。

### 4.2. 交易请求状态机 (V4)

#### 4.2.1. 状态定义

| 状态值 | 中文名 | 描述 |
| :--- | :--- | :--- |
| `Executing` | **执行中** | 请求已成功提交，系统正在根据其执行模式进行处理。所有部分成交都在此状态下完成。 |
| `Completed` | **已完成** | 请求已结束。这包括100%成交，或部分成交后剩余部分被取消/拒绝的场景。 |
| `Rejected` | **已拒绝** | 请求被对手方或系统（如交易所）拒绝，是最终状态。需附带拒绝理由。 |
| `Cancelled` | **已取消** | 请求在完成前被发起方主动撤销，是最终状态。 |
| `Expired` | **已过期** | 请求在到达 `expires_at` 时间点时仍未完成，被系统自动标记为过期，是最终状态。 |

#### 4.2.2. 状态流转图

```mermaid
graph TD
    A[Start] --> B{Create & Match};
    B -- Match Success --> C(Executing);
    B -- Match Failed --> D[Rejected];
    
    C -- 部分成交 --> C;
    C -- 完全成交 --> E[Completed];
    C -- 部分成交后结束 --> E;
    C -- 被对手方/系统拒绝 --> D;
    C -- 被发起方取消 --> F[Cancelled];
    C -- 到期 --> G[Expired];
```

### 4.3. 关键业务逻辑实现

#### **1. 执行模式处理 (Handling Execution Modes)**
-   在 `Executing` 状态下，后端逻辑根据 `execution_mode` 字段将任务分发给不同的处理器：
    -   **`AUTOMATIC`**: 将指令推送到 **本地交易终端** 的WebSocket队列。
    -   **`MANUAL`**: 在被点价方的待办事项列表中创建一个“待回报”任务。
    -   **`SIMULATED`**: 立即使用快照价格完成交易，将状态推进到 `Completed`。

#### **2. 部分成交与加权平均价 (Partial Fills & VWAP)**
-   当系统收到一笔部分成交回报时（无论是人工还是自动），它会更新 `TradeRequest` 的记录。
-   **`executed_quantity`**: `new_executed_quantity = old_executed_quantity + current_fill_quantity`
-   **`executed_price` (加权平均价)**: 
    `new_avg_price = ((old_avg_price * old_executed_quantity) + (current_fill_price * current_fill_quantity)) / (old_executed_quantity + current_fill_quantity)`
-   **示例**: 一笔10手的请求，先成交3手@$3000，再成交2手@$4000。
    -   第一次更新后: `executed_quantity`=3, `executed_price`=3000。
    -   第二次更新后: `executed_quantity`=5, `executed_price`= `((3000*3)+(4000*2))/(3+2)` = $3400。

#### **3. 请求的终结 (Finalizing a Request)**
-   当 `executed_quantity` 等于 `requested_quantity` 时，请求状态自动变为 `Completed`。
-   在 `Executing` 状态下，如果剩余未成交部分被对手方拒绝或发起方取消，请求状态也应变为 `Completed`，此时 `executed_quantity` < `requested_quantity`。

#### **4. 过期处理 (Expiration Handling)**
-   需要一个后台定时任务（如每小时执行一次的 Cron Job）。
-   该任务查询所有 `status = 'Executing'` 且 `expires_at < NOW()` 的 `TradeRequest` 记录。
-   将这些记录的状态更新为 `Expired`，并解冻关联的 `contracts` 表中的 `frozen_quantity`。

---

## 5. API 接口定义 (API Definition) (V4)

| 功能描述 | HTTP 方法 | 路径 (Endpoint) | 核心参数/Body | 角色 |
| :--- | :--- | :--- | :--- | :--- |
| **创建交易请求** | `POST` | `/api/v1/dianjia/traderequests` | `setterId`, `instrumentId`, `quantity`, `expires_at` | Pricer |
| **取消交易请求** | `POST` | `/api/v1/dianjia/traderequests/{id}/cancel` | (无) | Pricer |
| **拒绝交易请求** | `POST` | `/api/v1/dianjia/traderequests/{id}/reject` | `{"reason": "价格不合适"}` | Setter |
| **人工回报交易结果** | `POST` | `/api/v1/dianjia/traderequests/{id}/feedback` | `{"action": "fill", "quantity": 10, "price": 3500}` | Setter |
| **查看交易请求详情** | `GET` | `/api/v1/dianjia/traderequests/{id}` | (无) | pricer setter 只能查看自己的 |
| **查看所有交易请求** | `GET` | `/api/v1/dianjia/traderequests/as-pricer` | (无) | pricer setter 才能查看 |
| **查看所有交易请求** | `GET` | `/api/v1/dianjia/traderequests/as-setter` | (无) | pricer setter 才能查看 |

---

## 6. 测试用例 (Test Cases) (V4)

| 用例ID | 模块 | 场景描述 | 预期结果 |
| :--- | :--- | :--- | :--- |
| `TC-TRADE-V4-01` | 后端 | Pricer发起请求，系统匹配成功。 | 请求状态直接变为 `Executing`。 |
| `TC-TRADE-V4-02` | 后端 | 对一个10手的请求，人工回报成交3手@$100，后又回报成交5手@$110。 | 请求状态保持 `Executing`。`executed_quantity`变为8，`executed_price`被正确地加权计算为$106.25。 |
| `TC-TRADE-V4-03` | 后端 | 一个`Executing`的请求到达了其 `expires_at` 时间。 | 后台任务将其状态更新为 `Expired`，并释放合同的冻结数量。 |
| `TC-TRADE-V4-04` | 后端 | Setter对一个`Executing`的请求执行“拒绝”操作。 | 请求状态变为 `Rejected`，并记录了拒绝理由。 |

---

## 7. 注意事项 (Notes)

- **并发控制**: 系统自动匹配和冻结的过程必须是原子的，需要通过数据库事务和行级锁来保证数据一致性，防止并发下的超额冻结。
- **前端适配**: App的UI需要相应调整，将交易发起的入口从“合同”转移到更直接的“交易”功能模块。

"""
缓存管理模块
负责合约信息的本地缓存读写
"""
import os
import json
import time
from typing import List, Dict, Optional, Tuple
from pathlib import Path
from .instrument_fetcher import InstrumentInfo


class CacheManager:
    """缓存管理器"""
    
    def __init__(self, cache_dir: str = None):
        """
        初始化缓存管理器
        
        Args:
            cache_dir: 缓存目录路径，如果为None则使用默认路径
        """
        if cache_dir is None:
            # 使用默认路径: hq/cache/
            script_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            cache_dir = os.path.join(script_dir, "cache")
        
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        self.instruments_cache_file = self.cache_dir / "instruments.json"
        self.metadata_cache_file = self.cache_dir / "cache_metadata.json"
        
        print(f"缓存管理器初始化完成，缓存目录: {self.cache_dir}")
    
    def save_instruments(self, instruments: List[InstrumentInfo]) -> bool:
        """
        保存合约信息到缓存
        
        Args:
            instruments: 合约信息列表
            
        Returns:
            bool: 保存是否成功
        """
        try:
            # 准备要保存的数据
            cache_data = {
                "timestamp": time.time(),
                "count": len(instruments),
                "instruments": [inst.to_dict() for inst in instruments]
            }
            
            # 保存到缓存文件
            with open(self.instruments_cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
            
            # 更新元数据
            metadata = {
                "last_update": time.time(),
                "last_update_readable": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
                "instruments_count": len(instruments),
                "cache_file_size": os.path.getsize(self.instruments_cache_file)
            }
            
            with open(self.metadata_cache_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            
            print(f"成功保存 {len(instruments)} 个合约信息到缓存文件")
            print(f"缓存文件: {self.instruments_cache_file}")
            print(f"文件大小: {metadata['cache_file_size'] / 1024:.1f} KB")
            
            return True
            
        except Exception as e:
            print(f"保存缓存失败: {str(e)}")
            return False
    
    def load_instruments(self) -> Tuple[List[InstrumentInfo], Optional[str]]:
        """
        从缓存加载合约信息
        
        Returns:
            tuple: (合约信息列表, 错误信息)
        """
        try:
            if not self.instruments_cache_file.exists():
                error_msg = "缓存文件不存在"
                print(f"警告: {error_msg}")
                return [], error_msg
            
            with open(self.instruments_cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            # 检查缓存数据格式
            if not isinstance(cache_data, dict) or 'instruments' not in cache_data:
                error_msg = "缓存文件格式不正确"
                print(f"错误: {error_msg}")
                return [], error_msg
            
            # 解析合约数据
            instruments_data = cache_data.get('instruments', [])
            instruments = []
            
            for item in instruments_data:
                try:
                    instrument = InstrumentInfo.from_dict(item)
                    instruments.append(instrument)
                except Exception as e:
                    print(f"警告: 解析缓存合约数据失败: {item}, 错误: {e}")
                    continue
            
            cache_timestamp = cache_data.get('timestamp', 0)
            cache_age_hours = (time.time() - cache_timestamp) / 3600
            
            print(f"成功从缓存加载 {len(instruments)} 个合约信息")
            print(f"缓存时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(cache_timestamp))}")
            print(f"缓存年龄: {cache_age_hours:.1f} 小时")
            
            return instruments, None
            
        except FileNotFoundError:
            error_msg = "缓存文件不存在"
            print(f"警告: {error_msg}")
            return [], error_msg
        except json.JSONDecodeError:
            error_msg = "缓存文件JSON格式错误"
            print(f"错误: {error_msg}")
            return [], error_msg
        except Exception as e:
            error_msg = f"加载缓存失败: {str(e)}"
            print(f"错误: {error_msg}")
            return [], error_msg
    
    def get_cache_info(self) -> Dict:
        """
        获取缓存信息
        
        Returns:
            dict: 缓存信息
        """
        info = {
            "cache_dir": str(self.cache_dir),
            "instruments_cache_exists": self.instruments_cache_file.exists(),
            "metadata_cache_exists": self.metadata_cache_file.exists()
        }
        
        if self.instruments_cache_file.exists():
            stat = self.instruments_cache_file.stat()
            info["instruments_cache_size"] = stat.st_size
            info["instruments_cache_mtime"] = stat.st_mtime
            info["instruments_cache_mtime_readable"] = time.strftime(
                "%Y-%m-%d %H:%M:%S", time.localtime(stat.st_mtime)
            )
        
        if self.metadata_cache_file.exists():
            try:
                with open(self.metadata_cache_file, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                info["metadata"] = metadata
            except Exception as e:
                info["metadata_error"] = str(e)
        
        return info
    
    def is_cache_valid(self, max_age_hours: float = 24.0) -> bool:
        """
        检查缓存是否有效（未过期）
        
        Args:
            max_age_hours: 最大缓存年龄（小时）
            
        Returns:
            bool: 缓存是否有效
        """
        if not self.instruments_cache_file.exists():
            return False
        
        try:
            stat = self.instruments_cache_file.stat()
            cache_age_hours = (time.time() - stat.st_mtime) / 3600
            return cache_age_hours <= max_age_hours
        except Exception:
            return False
    
    def clear_cache(self) -> bool:
        """
        清空缓存
        
        Returns:
            bool: 清空是否成功
        """
        try:
            files_removed = 0
            
            if self.instruments_cache_file.exists():
                self.instruments_cache_file.unlink()
                files_removed += 1
                print(f"删除缓存文件: {self.instruments_cache_file}")
            
            if self.metadata_cache_file.exists():
                self.metadata_cache_file.unlink()
                files_removed += 1
                print(f"删除元数据文件: {self.metadata_cache_file}")
            
            if files_removed > 0:
                print(f"成功清空缓存，删除了 {files_removed} 个文件")
            else:
                print("没有需要删除的缓存文件")
            
            return True
            
        except Exception as e:
            print(f"清空缓存失败: {str(e)}")
            return False
    
    def get_instruments_list_from_cache(self) -> List[Tuple[str, str]]:
        """
        从缓存获取兼容格式的合约列表
        
        Returns:
            list: [(symbol, exchange), ...] 格式的合约列表
        """
        instruments, error = self.load_instruments()
        if error:
            print(f"从缓存获取合约列表失败: {error}")
            return []
        
        return [(inst.InstrumentID, inst.ExchangeID) for inst in instruments]


# 示例用法
def main():
    """示例用法"""
    from .instrument_fetcher import InstrumentFetcher
    
    print("=== 缓存管理器测试 ===")
    
    # 创建缓存管理器
    cache_manager = CacheManager()
    
    # 显示缓存信息
    print("\n1. 缓存信息:")
    cache_info = cache_manager.get_cache_info()
    for key, value in cache_info.items():
        print(f"  {key}: {value}")
    
    # 检查缓存是否有效
    print(f"\n2. 缓存是否有效(24小时内): {cache_manager.is_cache_valid()}")
    
    # 尝试从缓存加载
    print("\n3. 从缓存加载合约信息:")
    instruments, error = cache_manager.load_instruments()
    if error:
        print(f"  从缓存加载失败: {error}")
        
        # 如果缓存不存在或无效，从API获取
        print("\n4. 从API获取合约信息:")
        fetcher = InstrumentFetcher()
        instruments, error = fetcher.fetch_instruments_sync()
        
        if error:
            print(f"  从API获取失败: {error}")
        else:
            print(f"  从API获取成功，共 {len(instruments)} 个合约")
            
            # 保存到缓存
            print("\n5. 保存到缓存:")
            success = cache_manager.save_instruments(instruments)
            if success:
                print("  保存缓存成功")
                
                # 验证缓存
                print("\n6. 验证缓存:")
                cached_instruments, cache_error = cache_manager.load_instruments()
                if cache_error:
                    print(f"  验证失败: {cache_error}")
                else:
                    print(f"  验证成功，缓存中有 {len(cached_instruments)} 个合约")
            else:
                print("  保存缓存失败")
    else:
        print(f"  从缓存加载成功，共 {len(instruments)} 个合约")


if __name__ == "__main__":
    main()
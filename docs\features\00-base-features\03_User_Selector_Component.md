# 03 - 用户选择器组件 (User Selector Component)

## 1. 功能简介 (Feature Introduction)

为了在应用中提供一个统一、可复用且保护用户隐私的用户选择方式，我们设计了本用户选择器组件。该组件允许用户通过一个交互友好的弹窗界面，从系统用户列表中搜索并选择一个用户。

核心特性包括：
- **可复用性**: 作为一个独立的Vue组件，可以轻松地在任何需要用户选择的页面中集成。
- **数据驱动**: 组件可以通过传入一个用户ID (`userId`) 来自动初始化并显示对应的用户信息。如果传入数据是空或者0， 或传入的数据不存在，则显示placeholder。
- **搜索功能**: 支持通过关键词（如昵称、手机号）实时搜索，方便在大量用户中快速定位。
- **隐私保护**: 后端接口经过专门设计，仅返回选择所必需的非敏感信息（ID、昵称、手机号），确保用户数据安全。

## 2. 后端 API 设计 (Backend API Design)

为了支持前端组件，后端需要提供两个接口：一个用于获取单个用户的公开信息，另一个用于获取可供选择的用户列表。

### 2.1. 获取可选择的用户列表

- **功能**: 提供一个经过滤的用户列表，用于在选择器弹窗中展示。
- **HTTP 方法**: `GET`
- **路径**: `/api/v1/user/getSelectableList`
- **查询参数**:
  | 参数名 | 类型 | 是否必须 | 描述 |
  | :--- | :--- | :--- | :--- |
  | `search` | String | 否 | 搜索关键词，可匹配用户的昵称或手机号。 |
- **权限**: 需要用户登录认证。
- **成功响应 (200 OK)**:
  ```json
  {
    "code": 0,
    "msg": "获取成功",
    "data": [
      {
        "ID": 101,
        "nickName": "张三",
        "phone": "138****1234"
      },
      {
        "ID": 102,
        "nickName": "李四",
        "phone": "139****5678"
      }
    ]
  }
  ```
- **响应数据说明**:
  - `ID`: 用户的唯一ID。
  - `nickName`: 用户的昵称。
  - `phone`: 用户的手机号（建议进行脱敏处理）。
  - **注意**: 绝不返回密码、UUID、微信ID等敏感信息。

### 2.2. 获取指定用户的公开信息

- **功能**: 根据用户ID获取单个用户的基本公开信息，用于组件初始化显示。
- **HTTP 方法**: `GET`
- **路径**: `/api/v1/user/getSelectableProfile/{id}`
- **路径参数**:
  | 参数名 | 类型 | 描述 |
  | :--- | :--- | :--- |
  | `id` | Number | 要查询的用户ID。 |
- **权限**: 需要用户登录认证。
- **成功响应 (200 OK)**:
  ```json
  {
    "code": 0,
    "msg": "获取成功",
    "data": {
      "ID": 101,
      "nickName": "张三",
      "phone": "138****1234"
    }
  }
  ```

## 3. 前端组件设计 (Frontend Component Design)

组件将基于现有的 `@app/src/components/UserSelector.vue` 进行实现，并接入新的API。

- **文件路径**: `@app/src/components/UserSelector.vue`
- **Props (属性)**:
  | 属性名 | 类型 | 默认值 | 描述 |
  | :--- | :--- | :--- | :--- |
  | `modelValue` | Number | `0` | 双向绑定的用户ID (`v-model`)。 |
  | `label` | String | `'用户选择'` | 弹窗选择器的标题。 |
  | `placeholder`| String | `'请选择用户'` | 未选择用户时的占位提示文字。 |
- **Events (事件)**:
  | 事件名 | 参数 | 描述 |
  | :--- | :--- | :--- |
  | `update:modelValue` | `(value: number)` | `v-model` 更新事件。 |
  | `change` | `(user: IUser | null)` | 用户选择变更时触发，返回选中的用户对象或`null`。 |

### 3.1. 核心逻辑

1.  **初始化**:
    - 组件创建时，会侦听 `modelValue` (即传入的 `userId`) 的变化。
    - 如果 `modelValue` 大于0，组件将调用 `GET /api/v1/user/getSelectableProfile/{id}` 接口。
    - 获取到用户信息后，更新内部的 `selectedUser` 状态，从而在界面上显示该用户的昵称和手机号。

2.  **打开选择器**:
    - 用户点击“选择”按钮，弹窗 (`wd-popup`) 显示。
    - 弹窗显示时，调用 `GET /api/v1/user/getSelectableList` 接口（不带`search`参数），获取完整的用户列表并渲染。

3.  **搜索用户**:
    - 用户在弹窗内的搜索框 (`wd-search`) 中输入内容。
    - 组件将调用 `GET /api/v1/user/getSelectableList?search=关键词` 接口，根据返回结果动态更新列表。

4.  **确认选择**:
    - 用户在列表中点击某一个用户。
    - 组件将更新 `modelValue` 为选中用户的 `ID`，并触发 `change` 事件。
    - 弹窗关闭，界面上显示新选中的用户信息。

5.  **移除选择**:
    - 用户点击“移除”按钮。
    - 组件将 `modelValue` 设置为 `0`，并触发 `change` 事件，参数为 `null`。

## 4. 实现方案 (Implementation Plan)

### 4.1. 后端

1.  **DTO 定义**: 在 `admin/server/model/system/response` 中创建一个 `SysUserSelectableResponse` 结构体，只包含 `ID`, `NickName`, `Phone` 字段。
2.  **Service 层**:
    - 在 `admin/server/service/system/sys_user.go` 中：
      - 新增 `GetSelectableList(search string)` 方法。该方法从数据库查询用户，支持模糊搜索，并将结果映射到 `SysUserSelectableResponse` 结构体列表。
      - 新增 `GetSelectableProfile(id uint)` 方法。该方法根据ID查询单个用户，并映射到 `SysUserSelectableResponse` 结构体。
3.  **API 层**:
    - 在 `admin/server/api/v1/system/sys_user.go` 中：
      - 创建 `GetSelectableList` 和 `GetSelectableProfile` 两个新的API方法，分别调用对应的Service。
4.  **Router 层**:
    - 在 `admin/server/router/system/sys_user.go` 中注册新的路由：
      - `GET /user/getSelectableList`
      - `GET /user/getSelectableProfile/:id`

### 4.2. 前端

1.  **API 定义**: 在 `@app/src/api/user.ts` 中添加 `getSelectableList` 和 `getSelectableProfile` 两个函数，用于请求新的后端接口。
2.  **组件实现**:
    - 修改 `@app/src/components/UserSelector.vue`。
    - 在 `loadAvailableUsers` 方法中，调用 `getSelectableList` API，并传入搜索关键词。
    - 在 `initSelectedUser` 方法中，调用 `getSelectableProfile` API，根据传入的 `modelValue` 获取用户信息。
    - 移除所有模拟数据，完全替换为API调用。

## 5. 测试用例 (Test Cases)

| 用例ID | 模块 | 场景描述 | 预期结果 |
| :--- | :--- | :--- | :--- |
| `TC-USRSEL-001` | 后端 | 调用用户列表接口（无搜索） | 返回包含ID、昵称、手机号的用户列表，信息已脱敏。 |
| `TC-USRSEL-002` | 后端 | 调用用户列表接口（有搜索） | 返回与搜索关键词匹配的用户列表。 |
| `TC-USRSEL-003` | 后端 | 调用用户列表接口（未登录） | 返回401未授权错误。 |
| `TC-USRSEL-004` | 后端 | 调用获取单个用户接口 | 返回指定ID用户的公开信息。 |
| `TC-USRSEL-005` | 前端 | 带 `userId` 初始化组件 | 组件直接显示对应用户的昵称和手机号。 |
| `TC-USRSEL-006` | 前端 | 打开选择器并选择一个用户 | 组件正确更新显示，`v-model` 的值变为选中用户的ID。 |
| `TC-USRSEL-007` | 前端 | 在选择器中搜索用户 | 列表内容根据搜索词正确过滤。 |
| `TC-USRSEL-008` | 前端 | 移除已选中的用户 | 组件显示占位符，`v-model` 的值变为0。 |

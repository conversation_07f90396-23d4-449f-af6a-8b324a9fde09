"""
Token管理器

负责Token的安全存储和管理，包括：
- Token加密存储
- Token加载和验证
- Token清除
- 机器特征密钥生成
"""

import os
import json
import logging
from typing import Optional
from datetime import datetime, timedelta

from ..models import TokenData
from ..exceptions import CryptoException, ConfigException, handle_auth_exception
from ..utils.crypto import CryptoManager
from ..utils.config import ConfigManager


class TokenManager:
    """Token管理器
    
    提供安全的Token存储和管理功能。
    """
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """初始化Token管理器
        
        Args:
            config_manager: 配置管理器（可选）
        """
        self.config_manager = config_manager or ConfigManager()
        self.crypto_manager = CryptoManager()
        self.logger = logging.getLogger(__name__)
        
        # 从配置获取Token文件路径
        self.token_file = self.config_manager.get("auth.token.storage_file", "user_token.json")
        self.encryption_enabled = self.config_manager.get("auth.token.encryption_enabled", True)
        
        # 确保Token文件目录存在
        self._ensure_token_directory()
    
    def _ensure_token_directory(self):
        """确保Token文件目录存在"""
        token_dir = os.path.dirname(self.token_file)
        if token_dir and not os.path.exists(token_dir):
            try:
                os.makedirs(token_dir, mode=0o700)  # 只有所有者可以访问
                self.logger.info(f"创建Token目录: {token_dir}")
            except OSError as e:
                self.logger.error(f"创建Token目录失败: {e}")
                raise ConfigException(f"无法创建Token目录: {token_dir}")
    
    @handle_auth_exception
    def save_token(self, token_data: TokenData):
        """保存Token到本地（加密存储）
        
        Args:
            token_data: Token数据
            
        Raises:
            CryptoException: 加密失败
            ConfigException: 文件操作失败
        """
        try:
            self.logger.info("开始保存Token")
            
            # 序列化Token数据
            token_dict = token_data.to_dict()
            json_str = json.dumps(token_dict, ensure_ascii=False, indent=2)
            
            # 加密数据（如果启用）
            if self.encryption_enabled:
                encrypted_data = self.crypto_manager.encrypt(json_str)
                file_data = {
                    "encrypted": True,
                    "data": encrypted_data,
                    "created_at": datetime.now().isoformat()
                }
            else:
                file_data = {
                    "encrypted": False,
                    "data": token_dict,
                    "created_at": datetime.now().isoformat()
                }
            
            # 保存到文件
            with open(self.token_file, 'w', encoding='utf-8') as f:
                json.dump(file_data, f, ensure_ascii=False, indent=2)
            
            # 设置文件权限（只有所有者可以读写）
            os.chmod(self.token_file, 0o600)
            
            self.logger.info("Token保存成功")
            
        except Exception as e:
            self.logger.error(f"Token保存失败: {str(e)}")
            if isinstance(e, (CryptoException, ConfigException)):
                raise
            raise ConfigException(f"Token保存失败: {str(e)}")
    
    @handle_auth_exception
    def load_token(self) -> Optional[TokenData]:
        """从本地加载Token
        
        Returns:
            Optional[TokenData]: Token数据，不存在或加载失败时返回None
            
        Raises:
            CryptoException: 解密失败
            ConfigException: 文件操作失败
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(self.token_file):
                self.logger.info("Token文件不存在")
                return None
            
            self.logger.info("开始加载Token")
            
            # 读取文件
            with open(self.token_file, 'r', encoding='utf-8') as f:
                file_data = json.load(f)
            
            # 检查文件格式
            if not isinstance(file_data, dict) or "data" not in file_data:
                self.logger.warning("Token文件格式无效")
                return None
            
            # 解密数据（如果需要）
            if file_data.get("encrypted", False):
                encrypted_data = file_data["data"]
                if not isinstance(encrypted_data, str):
                    self.logger.warning("加密Token数据格式无效")
                    return None
                
                json_str = self.crypto_manager.decrypt(encrypted_data)
                token_dict = json.loads(json_str)
            else:
                token_dict = file_data["data"]
            
            # 反序列化Token数据
            token_data = TokenData.from_dict(token_dict)
            
            self.logger.info("Token加载成功")
            return token_data
            
        except json.JSONDecodeError as e:
            self.logger.error(f"Token文件JSON格式错误: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Token加载失败: {str(e)}")
            if isinstance(e, (CryptoException, ConfigException)):
                raise
            return None
    
    @handle_auth_exception
    def clear_token(self):
        """清除本地Token
        
        Raises:
            ConfigException: 文件操作失败
        """
        try:
            if os.path.exists(self.token_file):
                os.remove(self.token_file)
                self.logger.info("Token文件已删除")
            else:
                self.logger.info("Token文件不存在，无需删除")
                
        except OSError as e:
            self.logger.error(f"Token清除失败: {e}")
            raise ConfigException(f"Token清除失败: {str(e)}")
    
    def is_token_valid(self, token_data: TokenData, buffer_minutes: int = 5) -> bool:
        """检查Token是否有效（未过期）
        
        Args:
            token_data: Token数据
            buffer_minutes: 缓冲时间（分钟），提前判断过期
            
        Returns:
            bool: Token是否有效
        """
        try:
            return not token_data.is_expired(buffer_minutes)
        except Exception as e:
            self.logger.error(f"Token有效性检查失败: {str(e)}")
            return False
    
    def get_token_info(self) -> Optional[dict]:
        """获取Token文件信息
        
        Returns:
            Optional[dict]: Token文件信息，包括创建时间、文件大小等
        """
        try:
            if not os.path.exists(self.token_file):
                return None
            
            stat = os.stat(self.token_file)
            
            # 尝试读取文件中的创建时间
            created_at = None
            try:
                with open(self.token_file, 'r', encoding='utf-8') as f:
                    file_data = json.load(f)
                    created_at = file_data.get("created_at")
            except:
                pass
            
            return {
                "file_path": self.token_file,
                "file_size": stat.st_size,
                "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "created_at": created_at,
                "encrypted": self.encryption_enabled
            }
            
        except Exception as e:
            self.logger.error(f"获取Token信息失败: {str(e)}")
            return None
    
    def backup_token(self, backup_path: Optional[str] = None) -> bool:
        """备份Token文件
        
        Args:
            backup_path: 备份文件路径（可选）
            
        Returns:
            bool: 备份是否成功
        """
        try:
            if not os.path.exists(self.token_file):
                self.logger.warning("Token文件不存在，无法备份")
                return False
            
            if not backup_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = f"{self.token_file}.backup_{timestamp}"
            
            # 复制文件
            import shutil
            shutil.copy2(self.token_file, backup_path)
            
            # 设置备份文件权限
            os.chmod(backup_path, 0o600)
            
            self.logger.info(f"Token备份成功: {backup_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Token备份失败: {str(e)}")
            return False
    
    def restore_token(self, backup_path: str) -> bool:
        """从备份恢复Token文件
        
        Args:
            backup_path: 备份文件路径
            
        Returns:
            bool: 恢复是否成功
        """
        try:
            if not os.path.exists(backup_path):
                self.logger.error(f"备份文件不存在: {backup_path}")
                return False
            
            # 验证备份文件格式
            try:
                with open(backup_path, 'r', encoding='utf-8') as f:
                    json.load(f)
            except json.JSONDecodeError:
                self.logger.error("备份文件格式无效")
                return False
            
            # 复制文件
            import shutil
            shutil.copy2(backup_path, self.token_file)
            
            # 设置文件权限
            os.chmod(self.token_file, 0o600)
            
            self.logger.info(f"Token恢复成功: {backup_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Token恢复失败: {str(e)}")
            return False
    
    def cleanup_old_backups(self, keep_days: int = 7):
        """清理旧的备份文件
        
        Args:
            keep_days: 保留天数
        """
        try:
            token_dir = os.path.dirname(self.token_file) or "."
            token_name = os.path.basename(self.token_file)
            
            cutoff_time = datetime.now() - timedelta(days=keep_days)
            
            for filename in os.listdir(token_dir):
                if filename.startswith(f"{token_name}.backup_"):
                    file_path = os.path.join(token_dir, filename)
                    
                    try:
                        stat = os.stat(file_path)
                        file_time = datetime.fromtimestamp(stat.st_mtime)
                        
                        if file_time < cutoff_time:
                            os.remove(file_path)
                            self.logger.info(f"删除旧备份文件: {filename}")
                            
                    except OSError as e:
                        self.logger.warning(f"删除备份文件失败 {filename}: {e}")
            
        except Exception as e:
            self.logger.error(f"清理备份文件失败: {str(e)}")

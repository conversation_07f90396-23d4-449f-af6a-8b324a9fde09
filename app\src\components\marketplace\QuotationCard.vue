<script lang="ts" setup>
import type { IQuotationResponse } from '@/types/quotation'

defineOptions({
  name: 'QuotationCard'
})

// Props
interface Props {
  quotation: IQuotationResponse
}

const props = defineProps<Props>()

// Emits
interface Emits {
  (e: 'click', quotation: IQuotationResponse): void
}

const emit = defineEmits<Emits>()

// ==================== 格式化和工具函数 ====================

/**
 * 格式化剩余时间显示
 * @param quotation 报价对象
 * @returns 格式化的时间字符串
 */
function formatRemainingTime(quotation: IQuotationResponse): string {
  if (quotation.isExpired) {
    return '已过期'
  }

  if (quotation.remainingHours <= 0) {
    return '即将过期'
  } else if (quotation.remainingHours < 24) {
    return `剩余 ${quotation.remainingHours} 小时`
  } else {
    const days = Math.floor(quotation.remainingHours / 24)
    return `剩余 ${days} 天`
  }
}

/**
 * 获取企业简称
 * @param fullName 完整企业名称
 * @returns 简化后的企业名称
 */
function getCompanyShortName(fullName: string): string {
  if (!fullName) return '未知企业'

  if (fullName.length > 8) {
    return fullName.substring(0, 8) + '...'
  }
  return fullName
}

/**
 * 智能数字格式化函数
 * @param num 要格式化的数字
 * @returns 格式化后的字符串
 */
function formatLargeNumber(num: number): string {
  const absNum = Math.abs(num)
  const sign = num < 0 ? '-' : ''

  // 小于万的数字直接显示
  if (absNum < 10000) {
    return sign + absNum.toLocaleString()
  }

  // 万级别显示
  if (absNum < *********) { // 1亿以下
    const wan = absNum / 10000
    if (wan >= 100) {
      return sign + Math.round(wan) + '万'
    } else {
      return sign + wan.toFixed(1) + '万'
    }
  }

  // 亿级别显示
  const yi = absNum / *********
  if (yi >= 100) {
    return sign + Math.round(yi) + '亿'
  } else {
    return sign + yi.toFixed(1) + '亿'
  }
}

/**
 * 格式化基差价格，确保显示正负符号
 * @param price 基差价格
 * @returns 带正负符号的格式化价格
 */
function formatBasisPrice(price: number): string {
  const formatted = formatLargeNumber(Math.abs(price))
  return price >= 0 ? `+${formatted}` : `-${formatted}`
}

/**
 * 动态字体大小计算
 * @param text 文本内容
 * @param baseSize 基础字体大小
 * @param minSize 最小字体大小
 * @returns 计算后的字体大小
 */
function calculateDynamicFontSize(text: string, baseSize: number = 48, minSize: number = 28): number {
  const textLength = text.length

  // 根据文字长度动态调整字体大小
  if (textLength <= 6) return baseSize
  if (textLength <= 8) return Math.max(baseSize * 0.9, minSize)
  if (textLength <= 10) return Math.max(baseSize * 0.8, minSize)
  return minSize
}

/**
 * 获取合约名称
 * @param quotation 报价对象
 * @returns 合约名称
 */
function getContractName(quotation: IQuotationResponse): string {
  // 优先从 instrumentRef 中获取真实的合约代码
  if (quotation.instrumentRef?.instrument_id) {
    return quotation.instrumentRef.instrument_id
  }
  return "未指定合约"
}

// ==================== 事件处理 ====================

/**
 * 处理卡片点击事件
 */
function handleCardClick(): void {
  emit('click', props.quotation)
}
</script>

<template>
  <view class="quotation-card" @click="handleCardClick">
    <!-- 报价类型标签 - 放在整个卡片的右上角 -->
    <view class="card-type-tag">
      <wd-tag
        :type="quotation.priceType === 'Fixed' ? 'primary' : 'danger'"
        size="small"
      >
        {{ quotation.priceType === 'Fixed' ? '一口价' : '基差' }}
      </wd-tag>
    </view>

    <!-- 卡片主体内容 -->
    <view class="card-body">
      <!-- 左侧内容区域 (2/3) -->
      <view class="left-content">
        <!-- 标题 -->
        <text class="quotation-title">{{ quotation.title }}</text>

        <!-- 报价人和有效期 -->
        <view class="publisher-info">
          <text class="publisher-name">{{ getCompanyShortName(quotation.user?.nickName || '') }}</text>
          <text class="remaining-time" :class="{ expired: quotation.isExpired }">
            {{ formatRemainingTime(quotation) }}
          </text>
        </view>

        <!-- 标签信息 -->
        <view class="tag-info">
          <wd-tag v-if="quotation.commodity?.name" type="primary" size="small">
            {{ quotation.commodity.name }}
          </wd-tag>
          <wd-tag v-if="quotation.deliveryLocation" type="success" size="small">
            {{ quotation.deliveryLocation }}
          </wd-tag>
          <wd-tag v-if="quotation.brand" type="warning" size="small">
            {{ quotation.brand }}
          </wd-tag>
        </view>
      </view>

      <!-- 右侧价格区域 (1/3) -->
      <view class="right-content">
        <!-- 价格显示区域 -->
        <view class="price-display">
          <template v-if="quotation.priceType === 'Fixed'">
            <!-- 一口价显示 -->
            <text
              class="price-value adaptive-price"
              :style="{
                fontSize: calculateDynamicFontSize(formatLargeNumber(quotation.price)) + 'rpx'
              }"
              :title="quotation.price.toLocaleString()"
            >
              {{ formatLargeNumber(quotation.price) }}
            </text>
          </template>
          <template v-else>
            <!-- 基差显示 -->
            <view class="basis-display">
              <text class="contract-name">{{ getContractName(quotation) }}</text>
              <text
                class="basis-value adaptive-price"
                :style="{
                  fontSize: calculateDynamicFontSize(formatBasisPrice(quotation.price), 42, 26) + 'rpx'
                }"
                :data-positive="quotation.price >= 0"
                :data-negative="quotation.price < 0"
                :title="(quotation.price >= 0 ? '+' : '') + quotation.price.toLocaleString()"
              >
                {{ formatBasisPrice(quotation.price) }}
              </text>
            </view>
          </template>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
// 设计系统变量
$primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$primary-color: #667eea;
$bg-card: rgba(255, 255, 255, 0.95);
$radius-lg: 20rpx;
$shadow-md: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);

.quotation-card {
  background: $bg-card;
  backdrop-filter: blur(10rpx);
  border-radius: $radius-lg;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: $shadow-md;
  transition: all 0.3s ease;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
  border: 1rpx solid rgba(102, 126, 234, 0.1);

  // 卡片右上角的类型标签
  .card-type-tag {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 10;

    :deep(.wd-tag) {
      font-size: 20rpx;
      border-radius: 8rpx 0 8rpx 8rpx;
      padding: 4rpx 12rpx;
      border-top-right-radius: $radius-lg;
      font-weight: 500;

      &[data-type="primary"] {
        background: $primary-gradient !important;
        box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
      }

      &[data-type="danger"] {
        background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%) !important;
        box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);
      }
    }
  }

  &:hover {
    box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
    transform: translateY(-4rpx);
    border-color: rgba(102, 126, 234, 0.2);
  }

  &:active {
    transform: translateY(-2rpx);
    box-shadow: $shadow-md;
  }

  .card-body {
    display: flex;
    align-items: stretch;
    gap: 24rpx;
    width: 100%;

    .left-content {
      flex: 2;
      min-width: 0; /* 关键：允许flex子项收缩 */
      max-width: 66.666%; /* 强制限制最大宽度为2/3 */
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      min-height: 120rpx;
      overflow: hidden; /* 防止内容溢出 */
      box-sizing: border-box;

      .quotation-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #1a1a1a;
        line-height: 1.4;
        margin-bottom: 12rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 100%; /* 确保标题不超过容器宽度 */
      }

      .publisher-info {
        display: flex;
        align-items: flex-start;
        gap: 16rpx;
        margin-bottom: 16rpx;
        min-width: 0; /* 允许收缩 */

        .publisher-name {
          font-size: 26rpx;
          color: #666;
          font-weight: 500;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          flex: 1;
          min-width: 0;
        }

        .remaining-time {
          font-size: 24rpx;
          color: #52c41a;
          padding: 4rpx 8rpx;
          background: rgba(82, 196, 26, 0.1);
          border-radius: 8rpx;
          flex-shrink: 0; /* 不允许收缩 */

          &.expired {
            color: #ff4d4f;
            background: rgba(255, 77, 79, 0.1);
          }
        }
      }

      .tag-info {
        display: flex;
        align-items: center;
        gap: 12rpx;
        flex-wrap: nowrap; /* 改为不换行，防止高度增加 */
        min-width: 0; /* 允许收缩 */
        overflow: hidden; /* 防止标签溢出 */
        width: 100%; /* 确保不超过容器宽度 */

        :deep(.wd-tag) {
          font-size: 22rpx;
          border-radius: 12rpx;
          padding: 4rpx 12rpx;
          flex-shrink: 1; /* 允许标签收缩 */
          min-width: 0;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 120rpx; /* 限制单个标签最大宽度 */
        }
      }
    }

    .right-content {
      flex: 1;
      min-width: 0; /* 允许收缩 */
      max-width: 33.333%; /* 强制限制最大宽度为1/3 */
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      justify-content: center; /* 居中对齐 */
      min-height: 120rpx;
      overflow: hidden; /* 防止内容溢出 */

      .price-display {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center; /* 改为居中对齐 */
        text-align: center; /* 改为居中对齐 */
        padding: 12rpx 0;
        min-width: 0; /* 允许收缩 */
        overflow: hidden; /* 防止内容溢出 */
        height: 80rpx; /* 设置固定高度，确保跨卡片对齐 */

        .price-value {
          font-weight: 900;
          color: #1890ff;
          line-height: 1.0;
          text-shadow: 0 2rpx 4rpx rgba(24, 144, 255, 0.15);
          background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 100%;
          transition: font-size 0.3s ease; /* 字体大小变化动画 */
        }

        .basis-display {
          display: flex;
          flex-direction: column;
          align-items: center; /* 改为水平居中 */
          justify-content: center; /* 保持垂直居中 */
          gap: 6rpx; /* 减少间距，让整体更紧凑 */
          min-width: 0; /* 允许收缩 */
          overflow: hidden; /* 防止内容溢出 */
          width: 100%;
          height: 100%; /* 占满price-display的高度 */

          .contract-name {
            font-size: 26rpx; /* 稍微减小字体 */
            color: #595959;
            font-weight: 700;
            letter-spacing: 2rpx;
            text-transform: uppercase;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 100%;
            text-align: center; /* 文字居中 */
          }

          .basis-value {
            font-weight: 900;
            line-height: 1.0;
            text-shadow: 0 2rpx 4rpx rgba(255, 77, 79, 0.15);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 100%;
            text-align: center; /* 文字居中 */
            transition: font-size 0.3s ease; /* 字体大小变化动画 */

            // 根据正负号显示不同颜色
            &:first-letter {
              font-size: 36rpx;
            }

            // 正数显示红色（涨）
            &[data-positive="true"] {
              color: #ff4d4f;
              background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }

            // 负数显示绿色（跌）
            &[data-negative="true"] {
              color: #52c41a;
              background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }
        }
      }
    }
  }
}

// 自适应价格显示通用样式
.adaptive-price {
  // 确保价格文字在容器内完整显示
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  // 添加hover效果显示完整价格
  &:hover {
    position: relative;
    z-index: 100;

    &::after {
      content: attr(title);
      position: absolute;
      top: -40rpx;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 8rpx 12rpx;
      border-radius: 8rpx;
      font-size: 24rpx;
      white-space: nowrap;
      z-index: 101;
      pointer-events: none;
      opacity: 0;
      animation: fadeIn 0.3s ease forwards;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-5rpx);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

// 响应式字体大小
@media (max-width: 750rpx) {
  .adaptive-price {
    // 小屏幕下进一步缩小字体
    &.price-value {
      font-size: clamp(24rpx, 5vw, 40rpx) !important;
    }

    &.basis-value {
      font-size: clamp(22rpx, 4.5vw, 36rpx) !important;
    }
  }
}
</style>

package dianjia

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
)

// ContractStatus 合同状态枚举 (V3)
type ContractStatus string

const (
	ContractStatusUnexecuted ContractStatus = "Unexecuted" // 未执行（默认状态，可被激活或取消）
	ContractStatusExecuting  ContractStatus = "Executing"  // 执行中（已激活，成为系统可匹配的资源）
	ContractStatusPending    ContractStatus = "Pending"    // 待处理（待系统处理，可被激活或取消）
	ContractStatusCompleted  ContractStatus = "Completed"  // 已完成（remaining_quantity已全部被消耗）
	ContractStatusCancelled  ContractStatus = "Cancelled"  // 已取消（在Executing状态下被完全取消）
)

// ContractPriceType 合同价格类型枚举
type ContractPriceType string

const (
	ContractPriceTypeBasis ContractPriceType = "basis" // 基差合同（点价合同）
	ContractPriceTypeFixed ContractPriceType = "fixed" // 固定价合同（洗基差合同）
)

// Contract 合同表 - 重构后的新结构
type Contract struct {
	global.GVA_MODEL
	// 基本信息
	ContractCode string `json:"contractCode" gorm:"column:contract_code;type:varchar(255);not null;comment:合同业务编码"`

	// 参与方
	SetterID uint           `json:"setterID" gorm:"column:setter_id;not null;comment:被点价方ID"`
	Setter   system.SysUser `json:"setter" gorm:"foreignKey:SetterID"`
	PricerID uint           `json:"pricerID" gorm:"column:pricer_id;not null;comment:点价方ID"`
	Pricer   system.SysUser `json:"pricer" gorm:"foreignKey:PricerID"`

	// 核心条款
	InstrumentRefID uint       `json:"instrumentRefID" gorm:"column:instrument_ref_id;not null;comment:关联的期货合约ID"`
	Instrument      Instrument `json:"instrument" gorm:"foreignKey:InstrumentRefID;references:ID"`
	Remarks         string     `json:"remarks" gorm:"column:remarks;type:text;comment:备注信息"`

	// 新的数量管理字段
	TotalQuantity     int `json:"totalQuantity" gorm:"column:total_quantity;not null;comment:合同总数量"`
	RemainingQuantity int `json:"remainingQuantity" gorm:"column:remaining_quantity;not null;comment:合同剩余可执行数量"`
	FrozenQuantity    int `json:"frozenQuantity" gorm:"column:frozen_quantity;not null;default:0;comment:冻结数量（点价请求时冻结，避免过度点价）"`

	// 新的价格类型管理
	PriceType  ContractPriceType `json:"priceType" gorm:"column:price_type;type:varchar(50);not null;comment:价格类型(basis/fixed)"`
	PriceValue float64           `json:"priceValue" gorm:"column:price_value;type:decimal(10,2);comment:价格/基差值"`

	// 合同状态 (V3)
	Status ContractStatus `json:"status" gorm:"column:status;type:varchar(50);not null;comment:合同状态(Unexecuted/Executing/Completed/Cancelled)"`

	// 生成来源信息
	SourceTradeRequestID *uint `json:"sourceTradeRequestID" gorm:"column:source_trade_request_id;comment:关联的源交易请求ID（生成该合同的交易请求）"`
	IsGenerated          bool  `json:"isGenerated" gorm:"column:is_generated;default:false;comment:是否为系统生成的合同"`

	// 关联表
	SourceTradeRequest *TradeRequest     `json:"sourceTradeRequest" gorm:"foreignKey:SourceTradeRequestID"`
	TradeRequests      []TradeRequest    `json:"tradeRequests" gorm:"many2many:execution_details"`
	ExecutionDetails   []ExecutionDetail `json:"executionDetails" gorm:"foreignKey:ContractID"`
}

// CreateContractRequest 创建合同请求 - 重构后
type CreateContractRequest struct {
	// 基本信息
	ContractCode string `json:"contractCode" binding:"required"`

	// 参与方
	PricerID uint `json:"pricerID" binding:"required"`

	// 核心条款
	InstrumentRefID uint   `json:"instrumentRefID" binding:"required"`
	Remarks         string `json:"remarks"`

	// 数量和价格
	TotalQuantity int               `json:"totalQuantity" binding:"required,min=1"`
	PriceType     ContractPriceType `json:"priceType" binding:"required,oneof=basis fixed"`
	PriceValue    float64           `json:"priceValue" binding:"required"`
}

// UpdateContractRequest 更新合同请求 - 重构后
type UpdateContractRequest struct {
	ID           uint   `json:"id" binding:"required"`
	ContractCode string `json:"contractCode" binding:"required"`

	// 参与方
	PricerID        uint `json:"pricerID" binding:"required"`
	InstrumentRefID uint `json:"instrumentRefID" binding:"required"`

	// 核心条款
	Remarks string `json:"remarks"`

	// 数量和价格
	TotalQuantity int               `json:"totalQuantity" binding:"required,min=1"`
	PriceType     ContractPriceType `json:"priceType" binding:"required,oneof=basis fixed"`
	PriceValue    float64           `json:"priceValue" binding:"required"`
}

// ContractListRequest 合同列表请求
type ContractListRequest struct {
	Page      int    `json:"page" form:"page"`
	PageSize  int    `json:"pageSize" form:"pageSize"`
	Status    string `json:"status" form:"status"`
	UserRole  string `json:"userRole" form:"userRole"` // setter/pricer
	StartDate string `json:"startDate" form:"startDate"`
	EndDate   string `json:"endDate" form:"endDate"`
}

// ContractAsSetterRequest 被点价方合同列表请求
type ContractAsSetterRequest struct {
	Page      int    `json:"page" form:"page"`
	PageSize  int    `json:"pageSize" form:"pageSize"`
	Status    string `json:"status" form:"status"`
	StartDate string `json:"startDate" form:"startDate"`
	EndDate   string `json:"endDate" form:"endDate"`
}

// ContractAsPricerRequest 点价方合同列表请求
type ContractAsPricerRequest struct {
	Page      int    `json:"page" form:"page"`
	PageSize  int    `json:"pageSize" form:"pageSize"`
	Status    string `json:"status" form:"status"`
	StartDate string `json:"startDate" form:"startDate"`
	EndDate   string `json:"endDate" form:"endDate"`
}

// PartialCancelContractRequest 部分取消合同请求 (V3)
type PartialCancelContractRequest struct {
	CancelQuantity int    `json:"cancelQuantity" binding:"required,min=1"`
	Reason         string `json:"reason"`
}

// CancelContractRequest 取消合同请求 (V3)
type CancelContractRequest struct {
	CancelQuantity int `json:"cancelQuantity" binding:"required,min=1"`
}

// ContractResponse 合同响应 - 基于新文档v1.4
type ContractResponse struct {
	Contract
}

func (Contract) TableName() string {
	return "dj_contracts"
}

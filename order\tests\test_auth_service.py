#!/usr/bin/env python3
"""
认证服务测试脚本

测试升级后的认证服务是否能正常连接服务器
"""

import sys
import os
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_auth_service():
    """测试认证服务"""
    try:
        from auth.services.auth_service import AuthService
        from auth.utils.config import ConfigManager
        
        print("=" * 50)
        print("认证服务连接测试")
        print("=" * 50)
        
        # 创建配置管理器和认证服务
        config_manager = ConfigManager()
        auth_service = AuthService(config_manager)
        
        print(f"服务器地址: {auth_service.base_url}")
        print(f"API端点: {auth_service.endpoints}")
        
        # 测试1: 获取验证码
        print("\n1. 测试获取验证码...")
        captcha_data = await auth_service.get_captcha()
        if captcha_data:
            print(f"✅ 验证码获取成功: {captcha_data}")
        else:
            print("❌ 验证码获取失败")
        
        # 测试2: 发送短信验证码
        print("\n2. 测试发送短信验证码...")
        test_phone = "13800138000"  # 测试手机号
        sms_result = await auth_service.send_sms_code(test_phone)
        if sms_result:
            print(f"✅ 短信验证码发送成功")
        else:
            print("❌ 短信验证码发送失败")
        
        # 测试3: 用户名密码登录（使用错误凭据测试响应格式）
        print("\n3. 测试用户名密码登录...")
        auth_response = await auth_service.authenticate_password(
            "test_user", "test_password", "1234", "test_captcha_id"
        )
        print(f"登录响应: success={auth_response.success}, message={auth_response.error_message}")
        
        # 测试4: 手机验证码登录（使用错误凭据测试响应格式）
        print("\n4. 测试手机验证码登录...")
        phone_response = await auth_service.authenticate_phone(test_phone, "123456")
        print(f"手机登录响应: success={phone_response.success}, message={phone_response.error_message}")
        
        # 关闭服务
        await auth_service.close()
        
        print("\n" + "=" * 50)
        print("测试完成")
        print("=" * 50)
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

async def test_login_ui_with_server():
    """测试登录UI与服务器的集成"""
    try:
        print("\n" + "=" * 50)
        print("登录UI与服务器集成测试")
        print("=" * 50)
        
        from vnpy.event import EventEngine
        from auth.managers.login_manager import LoginManager
        
        # 创建事件引擎和登录管理器
        event_engine = EventEngine()
        login_manager = LoginManager(event_engine)
        
        print("✅ 登录管理器创建成功")
        print(f"认证服务信息: {login_manager.auth_service.get_service_info()}")
        
        # 测试获取验证码
        print("\n测试获取验证码...")
        captcha_data = await login_manager.auth_service.get_captcha()
        if captcha_data:
            print(f"✅ 验证码获取成功")
            print(f"验证码ID: {captcha_data.get('captchaId', 'N/A')}")
            print(f"图片路径: {captcha_data.get('picPath', 'N/A')}")
        else:
            print("❌ 验证码获取失败")
        
        print("\n集成测试完成")
        
    except Exception as e:
        print(f"集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("开始认证服务测试...")
    
    # 运行基础服务测试
    asyncio.run(test_auth_service())
    
    # 运行集成测试
    asyncio.run(test_login_ui_with_server())

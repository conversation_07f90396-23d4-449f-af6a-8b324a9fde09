#!/usr/bin/env python
"""
步骤2简单验证测试
只测试合约系统核心功能，不涉及完整引擎
"""
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_core_functionality():
    """测试核心功能"""
    print("=== 步骤2核心功能验证 ===\n")
    
    try:
        # 1. 测试合约管理器导入
        print("1. 测试模块导入...")
        from core.instrument_manager import InstrumentManager, get_instrument
        from utils.instrument_fetcher import InstrumentFetcher
        from utils.cache_manager import CacheManager
        print("[OK] 所有模块导入成功")
        
        # 2. 测试合约获取功能
        print("\n2. 测试合约获取...")
        instruments = get_instrument()
        
        if instruments:
            print(f"[OK] 成功获取 {len(instruments)} 个合约")
            print(f"[OK] 示例合约: {instruments[0]}")
            
            # 验证格式
            if isinstance(instruments[0], tuple) and len(instruments[0]) == 2:
                symbol, exchange = instruments[0]
                print(f"[OK] 格式正确: symbol='{symbol}', exchange='{exchange}'")
                
                # 验证数据有效性
                if symbol and exchange:
                    print("[OK] 合约数据有效")
                else:
                    print(f"[WARN] 合约数据可能无效: symbol={symbol}, exchange={exchange}")
            else:
                print(f"[FAIL] 合约格式错误: {type(instruments[0])}")
                return False
        else:
            print("[FAIL] 未获取到合约信息")
            return False
        
        # 3. 测试缓存功能
        print("\n3. 测试缓存功能...")
        cache_manager = CacheManager()
        cache_info = cache_manager.get_cache_info()
        
        if cache_info.get("instruments_cache_exists"):
            print("[OK] 缓存文件已创建")
            print(f"[OK] 缓存大小: {cache_info.get('instruments_cache_size', 0) / 1024:.1f} KB")
        else:
            print("[WARN] 缓存文件不存在")
        
        # 4. 测试API功能（快速测试）
        print("\n4. 测试API连接...")
        fetcher = InstrumentFetcher()
        try:
            import requests
            response = requests.head(fetcher.api_url, timeout=5)
            if response.status_code == 200:
                print("[OK] API连接正常")
            else:
                print(f"[WARN] API返回状态码: {response.status_code}")
        except Exception as e:
            print(f"[WARN] API连接测试失败: {e}")
        
        # 5. 测试数据库依赖移除
        print("\n5. 验证数据库依赖移除...")
        try:
            # 尝试导入旧模块
            import importlib.util
            spec = importlib.util.find_spec("engine.instrument")
            if spec is None:
                print("[OK] 旧instrument模块已移除")
            else:
                print("[WARN] 旧instrument模块仍然存在")
        except Exception:
            print("[OK] 旧模块检查通过")
        
        # 6. 测试重复调用
        print("\n6. 测试重复调用...")
        instruments2 = get_instrument()
        if len(instruments2) == len(instruments):
            print("[OK] 重复调用结果一致")
        else:
            print(f"[WARN] 重复调用结果不一致: {len(instruments)} vs {len(instruments2)}")
        
        print("\n=== 核心功能验证完成 ===")
        print("[OK] 合约系统核心功能正常")
        print("[OK] API获取和缓存机制工作正常")
        print("[OK] 兼容旧系统接口")
        return True
        
    except Exception as e:
        print(f"[FAIL] 核心功能验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_performance_simple():
    """简单性能测试"""
    print("\n=== 简单性能测试 ===")
    
    try:
        import time
        from core.instrument_manager import get_instrument
        
        # 测试获取速度
        start_time = time.time()
        instruments = get_instrument()
        end_time = time.time()
        
        if instruments:
            duration = end_time - start_time
            print(f"[OK] 获取 {len(instruments)} 个合约耗时: {duration:.3f} 秒")
            
            if duration < 1.0:
                print("[OK] 性能良好（< 1秒）")
            elif duration < 5.0:
                print("[OK] 性能可接受（< 5秒）")
            else:
                print("[WARN] 性能较慢（>= 5秒）")
        else:
            print("[FAIL] 性能测试失败：未获取到数据")
            return False
        
        return True
        
    except Exception as e:
        print(f"[FAIL] 性能测试失败: {e}")
        return False


def test_data_quality():
    """数据质量测试"""
    print("\n=== 数据质量测试 ===")
    
    try:
        from core.instrument_manager import get_instrument_manager
        
        manager = get_instrument_manager()
        if not manager.instruments:
            manager.load_instruments_sync()
        
        instruments = manager.get_instruments()
        
        if not instruments:
            print("[FAIL] 无法获取合约数据")
            return False
        
        print(f"检查 {len(instruments)} 个合约的数据质量...")
        
        # 统计交易所分布
        exchange_count = {}
        valid_count = 0
        
        for inst in instruments:
            if inst.ExchangeID and inst.InstrumentID:
                valid_count += 1
                exchange_count[inst.ExchangeID] = exchange_count.get(inst.ExchangeID, 0) + 1
        
        print(f"[OK] 有效合约: {valid_count}/{len(instruments)} ({valid_count/len(instruments)*100:.1f}%)")
        
        if exchange_count:
            print(f"[OK] 交易所分布:")
            for exchange, count in sorted(exchange_count.items()):
                print(f"  {exchange}: {count} 个合约")
        
        # 检查是否有主要交易所
        major_exchanges = ['SHFE', 'DCE', 'CZCE', 'CFFEX', 'INE']
        found_exchanges = set(exchange_count.keys())
        major_found = found_exchanges.intersection(major_exchanges)
        
        if len(major_found) >= 3:
            print(f"[OK] 包含主要交易所: {', '.join(sorted(major_found))}")
        else:
            print(f"[WARN] 主要交易所覆盖不足: {', '.join(sorted(major_found))}")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] 数据质量测试失败: {e}")
        return False


if __name__ == "__main__":
    print("开始步骤2简单验证测试...\n")
    
    # 运行核心功能测试
    core_success = test_core_functionality()
    
    # 运行性能测试
    perf_success = test_performance_simple()
    
    # 运行数据质量测试
    quality_success = test_data_quality()
    
    if core_success and perf_success and quality_success:
        print("\n[SUCCESS] 步骤2验证完全通过！")
        print("[INFO] 新合约系统（API化与缓存）工作正常")
        print("[INFO] 已成功移除数据库依赖")
        print("[INFO] 可以继续后续开发工作")
        exit(0)
    else:
        print("\n[ERROR] 步骤2验证未完全通过，但核心功能可能正常")
        if core_success:
            print("[INFO] 核心功能正常，可以继续使用")
        exit(1)
"""
Redis适配器 - 封装Redis操作
提供清晰的接口供上层调用，包括数据存储、发布订阅等功能
"""
import json
import redis
from typing import Optional, Dict, Any, List, Union
from datetime import datetime, timedelta


class RedisAdapter:
    """Redis操作适配器"""
    
    def __init__(self, host: str = 'localhost', port: int = 6379, 
                 db: int = 0, password: Optional[str] = None,
                 decode_responses: bool = True, **kwargs):
        """
        初始化Redis适配器
        
        Args:
            host: Redis服务器地址
            port: Redis端口
            db: 数据库编号
            password: 密码
            decode_responses: 是否解码响应
            **kwargs: 其他Redis连接参数
        """
        self.host = host
        self.port = port
        self.db = db
        self.password = password
        self.decode_responses = decode_responses
        
        # Redis连接实例
        self.redis_client: Optional[redis.Redis] = None
        self.is_connected = False
        
        # 连接配置
        self.connection_config = {
            'host': host,
            'port': port,
            'db': db,
            'password': password,
            'decode_responses': decode_responses,
            'socket_connect_timeout': 5,
            'socket_timeout': 5,
            'retry_on_timeout': True,
            **kwargs
        }
    
    def connect(self) -> bool:
        """
        连接到Redis服务器
        
        Returns:
            bool: 连接是否成功
        """
        try:
            self.redis_client = redis.Redis(**self.connection_config)
            
            # 测试连接
            self.redis_client.ping()
            self.is_connected = True
            
            print(f"Redis连接成功: {self.host}:{self.port}, DB:{self.db}")
            return True
            
        except Exception as e:
            print(f"Redis连接失败: {e}")
            self.is_connected = False
            return False
    
    def disconnect(self):
        """断开Redis连接"""
        try:
            if self.redis_client:
                self.redis_client.close()
                self.redis_client = None
            self.is_connected = False
            # Redis连接已断开
        except Exception as e:
            print(f"断开Redis连接时发生错误: {e}")
    
    def is_connection_valid(self) -> bool:
        """检查连接是否有效"""
        try:
            if not self.redis_client:
                return False
            self.redis_client.ping()
            return True
        except:
            self.is_connected = False
            return False
    
    def ensure_connection(self):
        """确保连接有效，如果无效则重新连接"""
        if not self.is_connection_valid():
            print("Redis连接无效，尝试重新连接...")
            self.connect()
    
    # 基础操作
    def set(self, key: str, value: Any, ex: Optional[int] = None) -> bool:
        """
        设置键值对
        
        Args:
            key: 键
            value: 值
            ex: 过期时间（秒）
            
        Returns:
            bool: 操作是否成功
        """
        try:
            self.ensure_connection()
            if isinstance(value, (dict, list)):
                value = json.dumps(value, ensure_ascii=False)
            
            result = self.redis_client.set(key, value, ex=ex)
            return bool(result)
        except Exception as e:
            print(f"Redis SET操作失败 {key}: {e}")
            return False
    
    def setex(self, key: str, time: int, value: Any) -> bool:
        """
        设置键值对并指定过期时间
        
        Args:
            key: 键
            time: 过期时间（秒）
            value: 值
            
        Returns:
            bool: 操作是否成功
        """
        try:
            self.ensure_connection()
            if isinstance(value, (dict, list)):
                value = json.dumps(value, ensure_ascii=False)
            
            result = self.redis_client.setex(key, time, value)
            return bool(result)
        except Exception as e:
            print(f"Redis SETEX操作失败 {key}: {e}")
            return False
    
    def get(self, key: str) -> Optional[str]:
        """
        获取键值
        
        Args:
            key: 键
            
        Returns:
            Optional[str]: 值，如果不存在则返回None
        """
        try:
            self.ensure_connection()
            return self.redis_client.get(key)
        except Exception as e:
            print(f"Redis GET操作失败 {key}: {e}")
            return None
    
    def get_json(self, key: str) -> Optional[Dict]:
        """
        获取JSON格式的值
        
        Args:
            key: 键
            
        Returns:
            Optional[Dict]: JSON对象，如果不存在或解析失败则返回None
        """
        try:
            value = self.get(key)
            if value:
                return json.loads(value)
            return None
        except Exception as e:
            print(f"Redis GET_JSON操作失败 {key}: {e}")
            return None
    
    def delete(self, *keys: str) -> int:
        """
        删除键
        
        Args:
            *keys: 要删除的键
            
        Returns:
            int: 删除的键数量
        """
        try:
            self.ensure_connection()
            return self.redis_client.delete(*keys)
        except Exception as e:
            print(f"Redis DELETE操作失败: {e}")
            return 0
    
    def exists(self, key: str) -> bool:
        """
        检查键是否存在
        
        Args:
            key: 键
            
        Returns:
            bool: 键是否存在
        """
        try:
            self.ensure_connection()
            return bool(self.redis_client.exists(key))
        except Exception as e:
            print(f"Redis EXISTS操作失败 {key}: {e}")
            return False
    
    def expire(self, key: str, time: int) -> bool:
        """
        设置键的过期时间
        
        Args:
            key: 键
            time: 过期时间（秒）
            
        Returns:
            bool: 操作是否成功
        """
        try:
            self.ensure_connection()
            return bool(self.redis_client.expire(key, time))
        except Exception as e:
            print(f"Redis EXPIRE操作失败 {key}: {e}")
            return False
    
    def ttl(self, key: str) -> int:
        """
        获取键的剩余生存时间
        
        Args:
            key: 键
            
        Returns:
            int: 剩余时间（秒），-1表示没有过期时间，-2表示键不存在
        """
        try:
            self.ensure_connection()
            return self.redis_client.ttl(key)
        except Exception as e:
            print(f"Redis TTL操作失败 {key}: {e}")
            return -2
    
    # 发布订阅操作
    def publish(self, channel: str, message: Any) -> int:
        """
        发布消息到频道
        
        Args:
            channel: 频道名
            message: 消息内容
            
        Returns:
            int: 接收消息的订阅者数量
        """
        try:
            self.ensure_connection()
            if isinstance(message, (dict, list)):
                message = json.dumps(message, ensure_ascii=False)
            
            return self.redis_client.publish(channel, message)
        except Exception as e:
            print(f"Redis PUBLISH操作失败 {channel}: {e}")
            return 0
    
    def subscribe_channels(self, *channels: str):
        """
        订阅频道（返回PubSub对象）
        
        Args:
            *channels: 频道名列表
            
        Returns:
            redis.client.PubSub: 发布订阅对象
        """
        try:
            self.ensure_connection()
            pubsub = self.redis_client.pubsub()
            pubsub.subscribe(*channels)
            return pubsub
        except Exception as e:
            print(f"Redis SUBSCRIBE操作失败: {e}")
            return None
    
    # 行情数据专用方法
    def store_tick_data(self, symbol: str, tick_data: Dict[str, Any], 
                       expire_seconds: int = 30 * 24 * 3600) -> bool:
        """
        存储行情数据
        
        Args:
            symbol: 合约代码
            tick_data: 行情数据字典
            expire_seconds: 过期时间（秒），默认30天
            
        Returns:
            bool: 操作是否成功
        """
        key = f"tick:{symbol}"
        return self.setex(key, expire_seconds, tick_data)
    
    def get_tick_data(self, symbol: str) -> Optional[Dict]:
        """
        获取行情数据
        
        Args:
            symbol: 合约代码
            
        Returns:
            Optional[Dict]: 行情数据，如果不存在则返回None
        """
        key = f"tick:{symbol}"
        return self.get_json(key)
    
    def publish_tick_update(self, tick_data: Dict[str, Any]) -> int:
        """
        发布行情更新通知
        
        Args:
            tick_data: 行情数据
            
        Returns:
            int: 接收消息的订阅者数量
        """
        return self.publish("tick_update", tick_data)
    
    def get_all_tick_keys(self) -> List[str]:
        """
        获取所有行情数据键
        
        Returns:
            List[str]: 键列表
        """
        try:
            self.ensure_connection()
            return self.redis_client.keys("tick:*")
        except Exception as e:
            print(f"获取行情键列表失败: {e}")
            return []
    
    def cleanup_expired_ticks(self) -> int:
        """
        清理过期的行情数据
        
        Returns:
            int: 清理的键数量
        """
        try:
            tick_keys = self.get_all_tick_keys()
            expired_keys = []
            
            for key in tick_keys:
                if self.ttl(key) == -2:  # 键不存在
                    expired_keys.append(key)
            
            if expired_keys:
                return self.delete(*expired_keys)
            return 0
            
        except Exception as e:
            print(f"清理过期行情数据失败: {e}")
            return 0
    
    # 批量操作
    def mset(self, mapping: Dict[str, Any]) -> bool:
        """
        批量设置键值对
        
        Args:
            mapping: 键值对字典
            
        Returns:
            bool: 操作是否成功
        """
        try:
            self.ensure_connection()
            # 处理字典和列表值
            processed_mapping = {}
            for key, value in mapping.items():
                if isinstance(value, (dict, list)):
                    processed_mapping[key] = json.dumps(value, ensure_ascii=False)
                else:
                    processed_mapping[key] = value
            
            return bool(self.redis_client.mset(processed_mapping))
        except Exception as e:
            print(f"Redis MSET操作失败: {e}")
            return False
    
    def mget(self, *keys: str) -> List[Optional[str]]:
        """
        批量获取键值
        
        Args:
            *keys: 键列表
            
        Returns:
            List[Optional[str]]: 值列表，不存在的键对应None
        """
        try:
            self.ensure_connection()
            return self.redis_client.mget(*keys)
        except Exception as e:
            print(f"Redis MGET操作失败: {e}")
            return [None] * len(keys)
    
    # 健康检查和统计
    def get_info(self) -> Dict[str, Any]:
        """
        获取Redis服务器信息
        
        Returns:
            Dict[str, Any]: 服务器信息
        """
        try:
            self.ensure_connection()
            info = self.redis_client.info()
            return {
                'connected_clients': info.get('connected_clients', 0),
                'used_memory': info.get('used_memory_human', 'Unknown'),
                'redis_version': info.get('redis_version', 'Unknown'),
                'uptime_in_seconds': info.get('uptime_in_seconds', 0),
                'total_commands_processed': info.get('total_commands_processed', 0)
            }
        except Exception as e:
            print(f"获取Redis信息失败: {e}")
            return {}
    
    def get_connection_status(self) -> Dict[str, Any]:
        """
        获取连接状态信息
        
        Returns:
            Dict[str, Any]: 连接状态信息
        """
        return {
            'is_connected': self.is_connected,
            'host': self.host,
            'port': self.port,
            'db': self.db,
            'connection_valid': self.is_connection_valid()
        }
    
    def __del__(self):
        """析构函数"""
        try:
            self.disconnect()
        except:
            pass


if __name__ == "__main__":
    """测试代码"""
    import time
    
    # 创建Redis适配器
    redis_adapter = RedisAdapter(
        host='localhost',
        port=6379,
        db=0,
        password=None
    )
    
    # 连接测试
    if redis_adapter.connect():
        print("Redis连接成功")
        
        # 基础操作测试
        redis_adapter.set("test_key", "test_value", ex=60)
        value = redis_adapter.get("test_key")
        print(f"获取值: {value}")
        
        # JSON操作测试
        test_data = {"symbol": "IF2508", "price": 4000.0, "time": "2024-01-01 10:00:00"}
        redis_adapter.store_tick_data("IF2508", test_data)
        
        stored_data = redis_adapter.get_tick_data("IF2508")
        print(f"存储的行情数据: {stored_data}")
        
        # 发布消息测试
        subscribers = redis_adapter.publish_tick_update(test_data)
        print(f"发布消息给 {subscribers} 个订阅者")
        
        # 获取连接状态
        status = redis_adapter.get_connection_status()
        print(f"连接状态: {status}")
        
        # 清理测试数据
        redis_adapter.delete("test_key", "tick:IF2508")
        
        redis_adapter.disconnect()
    else:
        print("Redis连接失败")
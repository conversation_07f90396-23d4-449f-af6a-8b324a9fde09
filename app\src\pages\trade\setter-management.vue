<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "交易请求管理"
  }
}
</route>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import type { ITradeRequest, TradeRequestStatus, IManualFeedbackRequest, IRejectTradeRequestRequest } from '@/types/trade-request'
import { getMyTradeRequestsAsSetter, manualFeedback, rejectTradeRequest } from '@/api/traderequest'
import { toast } from '@/utils/toast'
import type { TagType } from 'wot-design-uni/components/wd-tag/types'
defineOptions({
  name: 'SetterManagementPage',
})

const router = useRouter()

// 响应式状态
const tradeRequests = ref<ITradeRequest[]>([])
const loading = ref(false)
const statusFilter = ref<TradeRequestStatus | ''>('')
const feedbackDialogVisible = ref(false)
const currentRequest = ref<ITradeRequest | null>(null)
const feedbackForm = ref<IManualFeedbackRequest>({
  quantity: 0,
  price: 0,
  notes: ''
})

const rejectForm = ref<IRejectTradeRequestRequest>({
  reason: ''
})

const currentAction = ref<'fill' | 'reject'>('fill')

// 状态筛选选项 (V4)
const statusOptions = ref([
  { label: '全部', value: '' },
  { label: '执行中', value: 'Executing' },
  { label: '已完成', value: 'Completed' },
  { label: '已拒绝', value: 'Rejected' },
  { label: '已取消', value: 'Cancelled' },
  { label: '已过期', value: 'Expired' }
])

// 计算待处理的请求数量 (V4)
const pendingCount = computed(() => {
  return tradeRequests.value.filter(req =>
    req.status === 'Executing'
  ).length
})

// 加载交易请求列表
async function loadTradeRequests() {
  if (loading.value) return

  loading.value = true
  try {
    const response = await getMyTradeRequestsAsSetter({
      status: statusFilter.value || undefined,
      page: 1,
      pageSize: 50
    })
    
    if (response.code === 0) {
      tradeRequests.value = response.data.list
    } else {
      toast.error(response.msg || '获取交易请求失败')
    }
  } catch (error) {
    console.error('获取交易请求失败:', error)
    toast.error('网络错误')
  } finally {
    loading.value = false
  }
}

// 处理状态筛选
function handleStatusFilter() {
  loadTradeRequests()
}

// 处理交易请求操作 (V4)
function handleRequestAction(request: ITradeRequest, action: 'fill' | 'reject') {
  currentRequest.value = request
  currentAction.value = action

  if (action === 'fill') {
    feedbackForm.value = {
      quantity: request.requestedQuantity - request.executedQuantity,
      price: request.requestedPrice || 0,
      notes: ''
    }
  } else {
    rejectForm.value = {
      reason: ''
    }
  }

  feedbackDialogVisible.value = true
}

// 提交反馈 (V4)
async function submitFeedback() {
  if (!currentRequest.value) return

  loading.value = true
  try {
    if (currentAction.value === 'fill') {
      // 验证成交表单
      if (!feedbackForm.value.quantity || feedbackForm.value.quantity <= 0) {
        toast.error('请输入有效的成交数量')
        return
      }
      if (!feedbackForm.value.price || feedbackForm.value.price <= 0) {
        toast.error('请输入有效的成交价格')
        return
      }

      const remainingQuantity = currentRequest.value.requestedQuantity - currentRequest.value.executedQuantity
      if (feedbackForm.value.quantity > remainingQuantity) {
        toast.error(`成交数量不能超过剩余数量 ${remainingQuantity}`)
        return
      }

      // 调用成交接口
      const response = await manualFeedback(currentRequest.value.ID, {
        quantity: Number(feedbackForm.value.quantity),
        price: Number(feedbackForm.value.price),
        notes: feedbackForm.value.notes
      })

      if (response.code === 0) {
        toast.success('成交处理成功')
        feedbackDialogVisible.value = false
        loadTradeRequests()
      } else {
        toast.error(response.msg || '成交失败')
      }
    } else {
      // 验证拒绝表单
      if (!rejectForm.value.reason?.trim()) {
        toast.error('拒绝时请填写原因')
        return
      }

      // 调用拒绝接口
      const response = await rejectTradeRequest(currentRequest.value.ID, {
        reason: rejectForm.value.reason
      })

      if (response.code === 0) {
        toast.success('拒绝处理成功')
        feedbackDialogVisible.value = false
        loadTradeRequests()
      } else {
        toast.error(response.msg || '拒绝失败')
      }
    }
  } catch (error) {
    console.error('提交反馈失败:', error)
    toast.error('操作失败，请重试')
  } finally {
    loading.value = false
  }
}

// 获取交易请求状态文本 (V4)
function getStatusText(status: TradeRequestStatus): string {
  const statusMap: Record<TradeRequestStatus, string> = {
    Executing: '执行中',
    Completed: '已完成',
    Rejected: '已拒绝',
    Cancelled: '已取消',
    Expired: '已过期'
  }
  return statusMap[status] || status
}

// 获取状态标签类型 (V4)
function getStatusTagType(status: TradeRequestStatus): TagType {
  const statusTagMap: Record<TradeRequestStatus, TagType> = {
    Executing: 'primary',
    Completed: 'success',
    Rejected: 'danger',
    Cancelled: 'warning',
    Expired: 'warning'
  }
  return statusTagMap[status] || 'warning' 
}

// 获取交易类型文本
function getRequestTypeText(type: string): string {
  return type === 'PointPrice' ? '点价' : '洗基差'
}

// 格式化日期
function formatDate(dateStr: string) {
  const date = new Date(dateStr)
  return `${date.getMonth() + 1}-${date.getDate()} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 生命周期
onMounted(() => {
  loadTradeRequests()
})
</script>

<template>
  <view class="setter-management-page">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">交易请求管理</text>
      <wd-tag v-if="pendingCount > 0" type="warning" size="small">
        {{ pendingCount }} 个待处理
      </wd-tag>
    </view>

    <!-- 筛选条件 -->
    <view class="filter-bar">
      <wd-picker 
        v-model="statusFilter" 
        :columns="statusOptions" 
        placeholder="筛选状态" 
        @confirm="handleStatusFilter" 
      />
    </view>

    <!-- 交易请求列表 -->
    <view class="request-list">
      <view 
        v-for="request in tradeRequests" 
        :key="request.ID" 
        class="request-card"
      >
        <!-- 请求头部 -->
        <view class="request-header">
          <view class="request-info">
            <text class="request-type">{{ getRequestTypeText(request.requestType) }}</text>
            <text class="request-id">#{{ request.ID }}</text>
          </view>
          <wd-tag :type="getStatusTagType(request.status)">
            {{ getStatusText(request.status) }}
          </wd-tag>
        </view>

        <!-- 请求详情 -->
        <view class="request-details">
          <view class="detail-row">
            <text class="label">点价方:</text>
            <text class="value">{{ request.pricer?.nickName || request.pricer?.userName || `用户${request.pricerID}` }}</text>
          </view>
          <view class="detail-row">
            <text class="label">期货合约:</text>
            <text class="value">{{ request.instrument?.instrument_name || `合约${request.instrumentRefID}` }}</text>
          </view>
          <view class="detail-row">
            <text class="label">数量:</text>
            <text class="value">{{ request.executedQuantity }}/{{ request.requestedQuantity }} 手</text>
          </view>
          <view v-if="request.requestedPrice" class="detail-row">
            <text class="label">价格:</text>
            <text class="value">{{ request.requestedPrice }}</text>
          </view>
          <view class="detail-row">
            <text class="label">时间:</text>
            <text class="value">{{ formatDate(request.CreatedAt) }}</text>
          </view>
          <view v-if="request.notes" class="detail-row">
            <text class="label">备注:</text>
            <text class="value">{{ request.notes }}</text>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view
          v-if="request.status === 'Executing'"
          class="request-actions"
        >
          <wd-button 
            type="success" 
            size="small"
            @click="handleRequestAction(request, 'fill')"
          >
            成交
          </wd-button>
          <wd-button 
            type="error" 
            size="small"
            @click="handleRequestAction(request, 'reject')"
          >
            拒绝
          </wd-button>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-if="!tradeRequests.length && !loading" class="empty-state">
      <text>暂无交易请求</text>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-state">
      <wd-loading />
      <text>加载中...</text>
    </view>

    <!-- 反馈对话框 -->
    <wd-popup v-model="feedbackDialogVisible" position="center" :close-on-click-modal="false">
      <view class="feedback-dialog">
        <view class="dialog-header">
          <text class="dialog-title">
            {{ currentAction === 'fill' ? '确认成交' : '确认拒绝' }}
          </text>
        </view>

        <view class="dialog-content">
          <view v-if="currentRequest" class="request-summary">
            <text class="summary-text">
              {{ getRequestTypeText(currentRequest.requestType) }} 
              {{ currentRequest.requestedQuantity }} 手
              <text v-if="currentRequest.requestedPrice">@ {{ currentRequest.requestedPrice }}</text>
            </text>
          </view>

          <view v-if="currentAction === 'fill'" class="fill-form">
            <wd-input 
              v-model="feedbackForm.quantity"
              label="成交数量" 
              type="number" 
              placeholder="请输入成交数量"
              :max="currentRequest ? currentRequest.requestedQuantity - currentRequest.executedQuantity : undefined"
            />
            <wd-input 
              v-model="feedbackForm.price"
              label="成交价格" 
              type="number" 
              placeholder="请输入成交价格"
              class="mt-3"
            />
            <wd-textarea 
              v-model="feedbackForm.notes"
              label="备注" 
              placeholder="成交备注（可选）"
              class="mt-3"
            />
          </view>

          <view v-else class="reject-form">
            <wd-textarea
              v-model="rejectForm.reason"
              label="拒绝原因"
              placeholder="请填写拒绝原因"
              required
            />
          </view>
        </view>

        <view class="dialog-actions">
          <wd-button 
            type="info" 
            @click="feedbackDialogVisible = false"
          >
            取消
          </wd-button>
          <wd-button
            :type="currentAction === 'fill' ? 'success' : 'error'"
            @click="submitFeedback"
          >
            确认{{ currentAction === 'fill' ? '成交' : '拒绝' }}
          </wd-button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<style lang="scss" scoped>
.setter-management-page {
  padding: 20rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;

  .page-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.filter-bar {
  margin-bottom: 30rpx;
}

.request-list {
  .request-card {
    background: white;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

    .request-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;

      .request-info {
        display: flex;
        align-items: center;
        gap: 16rpx;

        .request-type {
          font-size: 32rpx;
          font-weight: bold;
          color: #333;
        }

        .request-id {
          font-size: 24rpx;
          color: #999;
        }
      }
    }

    .request-details {
      margin-bottom: 20rpx;

      .detail-row {
        display: flex;
        margin-bottom: 12rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-size: 26rpx;
          color: #666;
          width: 140rpx;
          flex-shrink: 0;
        }

        .value {
          font-size: 26rpx;
          color: #333;
          flex: 1;
        }
      }
    }

    .request-actions {
      display: flex;
      gap: 20rpx;
      justify-content: flex-end;
      border-top: 1rpx solid #f0f0f0;
      padding-top: 20rpx;
    }
  }
}

.empty-state, .loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx;
  color: #999;
  font-size: 28rpx;
  gap: 30rpx;
}

.feedback-dialog {
  width: 600rpx;
  background: white;
  border-radius: 12rpx;
  overflow: hidden;

  .dialog-header {
    padding: 40rpx 30rpx 20rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .dialog-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .dialog-content {
    padding: 30rpx;

    .request-summary {
      margin-bottom: 30rpx;
      padding: 20rpx;
      background: #f8f9fa;
      border-radius: 8rpx;

      .summary-text {
        font-size: 28rpx;
        color: #333;
      }
    }
  }

  .dialog-actions {
    display: flex;
    gap: 20rpx;
    padding: 20rpx 30rpx 40rpx;
    justify-content: flex-end;
  }
}
</style>
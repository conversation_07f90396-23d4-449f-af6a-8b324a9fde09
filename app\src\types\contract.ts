import type { IUser } from './user'
import type { IInstrumentSelectItem } from './instrument'
import type { 
  ITradeRequest, 
  ICreateTradeRequestRequest,
  ITradeRequestForPricerRequest,
  ITradeRequestForSetterRequest,
  IManualFeedbackRequest
} from './trade-request'
import type { IExecutionDetail } from './execution-detail'

// 合同状态枚举 - V3 重构后
export type ContractStatus = 'Unexecuted' | 'Executing' | 'Pending' | 'Completed' | 'Cancelled'

// 合同价格类型枚举 - 重构后
export type ContractPriceType = 'basis' | 'fixed' // basis: 基差合同(点价), fixed: 固定价合同(洗基差)

// 重新导出类型以保持向后兼容
export type { TradeRequestType, TradeRequestStatus } from './trade-request'
export type { ExecutionType, ExecutionStatus } from './execution-detail'

// 用户角色
export type UserRole = 'setter' | 'pricer'

// 废弃的类型定义 - 保持向后兼容
export type TradeStatus = 
  | 'PendingApproval'    // 待审核
  | 'Rejected'           // 已拒绝
  | 'Approved'           // 已接受
  | 'Executing'          // 执行中
  | 'Filled'             // 已成交
  | 'PartiallyFilled'    // 部分成交    
  | 'ExchangeRejected'   // 执行失败
  | 'Cancelled'          // 已撤销

export type TradeType = 'basis' | 'fixed' // basis: 点价, fixed: 洗基差

// 合同基础接口 - 重构后的新结构，与后端Go结构体对齐
export interface IContract {
  // 基础字段（GVA_MODEL）
  ID: number
  CreatedAt: string
  UpdatedAt: string
  DeletedAt?: string
  
  // 基本信息
  contractCode: string
  
  // 参与方
  setterID: number
  setter?: IUser
  pricerID: number
  pricer?: IUser
  
  // 核心条款
  instrumentRefID: number
  instrument?: IInstrumentSelectItem // 完整的期货合约信息，用于前端展示
  remarks: string
  
  // 数量管理字段
  totalQuantity: number     // 合同总数量
  remainingQuantity: number // 合同剩余可执行数量
  frozenQuantity: number    // 冻结数量（点价请求时冻结，避免过度点价）
  
  // 价格类型管理
  priceType: ContractPriceType  // 价格类型(basis/fixed)
  priceValue: number            // 价格/基差值
  
  // 合同状态 (V3)
  status: ContractStatus        // 合同状态(Unexecuted/Executing/Completed/Cancelled)

  // 生成来源信息
  sourceTradeRequestID?: number // 关联的源交易请求ID（生成该合同的交易请求）
  isGenerated: boolean          // 是否为系统生成的合同

  // 关联表
  sourceTradeRequest?: ITradeRequest  // 源交易请求
  tradeRequests?: ITradeRequest[]     // 交易请求
  executionDetails?: IExecutionDetail[] // 执行明细
}

// 重新导出接口以保持向后兼容
export type { ITradeRequest } from './trade-request'
export type { IExecutionDetail, IExecutionSummary } from './execution-detail'

// 交易记录接口 - 保持向后兼容
export interface ITrade {
  // 基础字段
  ID: number
  CreatedAt: string
  UpdatedAt: string
  DeletedAt?: string
  
  // 基本信息
  contractID: number
  contract?: IContract
  
  // 核心点价数据
  tradeType: TradeType
  requestedQuantity: number
  requestedPrice: number
  indexPriceSnapshot: number
  
  // 状态与流程
  status: TradeStatus
  
  // 执行结果
  executedQuantity: number
  avgExecutedPrice: number
}

// 合同响应（含额外信息）- 基于新文档v1.4
export interface IContractResponse extends IContract {
  // 无需额外字段，新架构中所有信息都在基础Contract模型中
}

// 创建合同请求 - 重构后
export interface ICreateContractRequest {
  // 基本信息
  contractCode: string
  
  // 参与方
  pricerID: number
  
  // 核心条款
  instrumentRefID: number
  instrument?: IInstrumentSelectItem // 前端辅助字段，不发送给后端
  remarks: string
  
  // 数量和价格
  totalQuantity: number          // 合同总数量
  priceType: ContractPriceType   // 价格类型(basis/fixed)
  priceValue: number             // 价格/基差值
}

// 更新合同请求 - 重构后
export interface IUpdateContractRequest extends ICreateContractRequest {
  id: number
}

// 重新导出请求类型以保持向后兼容
export type { 
  ICreateTradeRequestRequest,
} from './trade-request'

export type {
  ICreateExecutionDetailRequest,
  IUpdateExecutionDetailRequest,
  IExecutionDetailListRequest,
  IExecutionDetailResponse,
  IExecutionDetailListResponse
} from './execution-detail'

// 旧的交易请求接口 - 保持向后兼容
export interface ICreateTradeRequest {
  contractID: number
  tradeType: TradeType
  requestedQuantity: number
  requestedPrice: number
}

// 交易状态变更请求
export interface ITradeStatusRequest {
  id: number
  status: TradeStatus
}

// 合同列表请求 - 与Go模型对齐
export interface IContractListRequest {
  page?: number
  pageSize?: number
  status?: string
  userRole?: UserRole | string  // setter/pricer
  startDate?: string           // 新增：按创建时间筛选的开始日期，格式 YYYY-MM-DD
  endDate?: string             // 新增：按创建时间筛选的结束日期，格式 YYYY-MM-DD
}

// 被点价方合同列表请求 - 新增
export interface IContractAsSetterRequest {
  page?: number      // 页码
  pageSize?: number  // 每页数量
  status?: string    // 合同状态，多个用逗号分隔
  startDate?: string // 按创建时间筛选的开始日期，格式 YYYY-MM-DD
  endDate?: string   // 按创建时间筛选的结束日期，格式 YYYY-MM-DD
}

// 点价方合同列表请求 - 新增
export interface IContractAsPricerRequest {
  page?: number      // 页码
  pageSize?: number  // 每页数量
  status?: string    // 合同状态，多个用逗号分隔
  startDate?: string // 按创建时间筛选的开始日期，格式 YYYY-MM-DD
  endDate?: string   // 按创建时间筛选的结束日期，格式 YYYY-MM-DD
}

// 合同列表响应
export interface IContractListResponse {
  list: IContractResponse[]
  total: number
  page: number
  pageSize: number
}

// 合同详情请求参数
export interface IContractDetailParams {
  contractId: number
  userRole?: UserRole | string
}

// 交易列表请求
export interface ITradeListRequest {
  contractId?: number
  page?: number
  pageSize?: number
}

// 交易列表响应
export interface ITradeListResponse {
  list: ITrade[]
  total: number
  page: number
  pageSize: number
}

// 价格计算响应
export interface IPriceCalculationResponse {
  indexPrice: number      // 指数价格
  basisPrice: number      // 基差价格
  calculatedPrice: number // 计算得出的一口价
}

// 注意：IResData 类型已在全局 typings.d.ts 中定义，此处不再重复定义
// 分页响应类型继续保留，因为后端返回格式需要
export interface IPageResult<T = any> {
  list: T[]
  total: number
  page: number
  pageSize: number
}

// ============ 新增：合同冻结和取消相关类型 ============

// 部分取消合同请求
export interface IPartialCancelContractRequest {
  cancelQuantity: number  // 取消数量
  reason?: string         // 取消原因
}

// 合同取消记录
export interface IContractCancelRecord {
  ID: number
  CreatedAt: string
  UpdatedAt: string
  DeletedAt?: string

  // 关联信息
  contractID: number
  contract?: IContract
  userID: number
  user?: IUser

  // 取消信息
  cancelQuantity: number                    // 取消数量
  reason: string                           // 取消原因

  // 合同状态信息（记录取消时的合同状态）
  beforeCancelRemainingQuantity: number    // 取消前剩余数量
  afterCancelRemainingQuantity: number     // 取消后剩余数量
  contractStatusAfterCancel: ContractStatus // 取消后合同状态
}

// 合同状态信息
export interface IContractStatusInfo {
  status: ContractStatus           // 合同状态
  isGenerated: boolean            // 是否为系统生成
  canOperate: boolean             // 是否可操作
  frozenQuantity: number          // 冻结数量
  availableQuantity: number       // 可用数量（剩余数量 - 冻结数量）
  sourceTradeRequest?: {          // 源交易请求信息（如果存在）
    id: number
    status: string
  } | null
}

// ============ V3 新增：合同状态管理相关类型 ============

// 取消合同请求 (V3)
export interface ICancelContractRequest {
  cancelQuantity: number  // 取消数量
  reason?: string
}
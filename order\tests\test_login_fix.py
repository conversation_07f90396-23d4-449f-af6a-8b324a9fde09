#!/usr/bin/env python3
"""
登录修复测试脚本

测试日期溢出和线程问题的修复
"""

import sys
import os
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_date_overflow_fix():
    """测试日期溢出修复"""
    try:
        from auth.models import AuthResponse
        from datetime import datetime
        
        print("=" * 50)
        print("测试日期溢出修复")
        print("=" * 50)
        
        # 测试1: 正常的expires_in值
        print("1. 测试正常的expires_in值...")
        auth_response = AuthResponse(
            success=True,
            access_token="test_token",
            expires_in=3600  # 1小时
        )
        token_data = auth_response.to_token_data()
        print(f"✅ 正常值测试通过: expires_at = {token_data.expires_at}")
        
        # 测试2: 过大的expires_in值（毫秒时间戳）
        print("\n2. 测试过大的expires_in值（毫秒时间戳）...")
        large_timestamp = int(datetime.now().timestamp() * 1000) + 3600000  # 当前时间+1小时的毫秒时间戳
        auth_response = AuthResponse(
            success=True,
            access_token="test_token",
            expires_in=large_timestamp
        )
        token_data = auth_response.to_token_data()
        print(f"✅ 大值测试通过: expires_at = {token_data.expires_at}")
        
        # 测试3: 极大的expires_in值
        print("\n3. 测试极大的expires_in值...")
        auth_response = AuthResponse(
            success=True,
            access_token="test_token",
            expires_in=999999999999999  # 极大值
        )
        token_data = auth_response.to_token_data()
        print(f"✅ 极大值测试通过: expires_at = {token_data.expires_at}")
        
        # 测试4: 负数expires_in值
        print("\n4. 测试负数expires_in值...")
        auth_response = AuthResponse(
            success=True,
            access_token="test_token",
            expires_in=-1000
        )
        token_data = auth_response.to_token_data()
        print(f"✅ 负数测试通过: expires_at = {token_data.expires_at}")
        
        print("\n✅ 所有日期溢出测试通过！")
        
    except Exception as e:
        print(f"❌ 日期溢出测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

async def test_login_with_real_server():
    """测试真实服务器登录"""
    try:
        from vnpy.event import EventEngine
        from auth.managers.login_manager import LoginManager
        
        print("\n" + "=" * 50)
        print("测试真实服务器登录")
        print("=" * 50)
        
        # 创建事件引擎和登录管理器
        event_engine = EventEngine()
        login_manager = LoginManager(event_engine)
        
        # 测试手机登录（使用测试手机号和验证码）
        print("测试手机验证码登录...")
        test_phone = "18678863949"
        test_code = "123456"  # 测试验证码
        
        result = await login_manager.login_with_phone(test_phone, test_code, False)
        
        if result.success:
            print(f"✅ 登录成功!")
            print(f"Token: {result.token_data.access_token[:20]}...")
            print(f"过期时间: {result.token_data.expires_at}")
            print(f"用户信息: {result.token_data.user_info}")
        else:
            print(f"ℹ️ 登录失败（预期）: {result.message}")
            print("这是正常的，因为使用的是测试验证码")
        
        print("\n✅ 登录测试完成，没有出现日期溢出错误！")
        
    except Exception as e:
        print(f"❌ 登录测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_thread_cleanup():
    """测试线程清理"""
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import QTimer
        from vnpy.event import EventEngine
        from ui.login.login_window import LoginWindow
        
        print("\n" + "=" * 50)
        print("测试线程清理")
        print("=" * 50)
        
        app = QApplication(sys.argv)
        
        # 创建登录窗口
        event_engine = EventEngine()
        login_window = LoginWindow(event_engine)
        
        # 测试多次创建和清理线程
        print("测试多次线程创建和清理...")
        for i in range(5):
            print(f"第 {i+1} 次测试...")
            
            # 模拟登录操作
            login_window.cleanup_worker_thread()
            
            # 检查线程状态
            if login_window.worker_thread is None:
                print(f"  ✅ 线程清理成功")
            else:
                print(f"  ❌ 线程清理失败")
        
        # 关闭窗口
        login_window.close()
        app.quit()
        
        print("✅ 线程清理测试完成！")
        
    except Exception as e:
        print(f"❌ 线程清理测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("开始修复验证测试...")
    
    # 测试日期溢出修复
    asyncio.run(test_date_overflow_fix())
    
    # 测试真实服务器登录
    asyncio.run(test_login_with_real_server())
    
    # 测试线程清理
    test_thread_cleanup()
    
    print("\n" + "=" * 50)
    print("所有修复验证测试完成！")
    print("=" * 50)

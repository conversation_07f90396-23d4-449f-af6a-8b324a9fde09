package dianjia

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/dianjia"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ContractApi struct{}

// CreateContract 创建合同
// @Tags Contract
// @Summary 创建合同
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body dianjia.CreateContractRequest true "创建合同请求"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /contract/create [post]
func (contractApi *ContractApi) CreateContract(c *gin.Context) {
	var req dianjia.CreateContractRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	err = service.ServiceGroupApp.DianjiaServiceGroup.ContractService.CreateContract(req, userID)
	if err != nil {
		global.GVA_LOG.Error("创建合同失败!", zap.Error(err))
		response.FailWithMessage("创建合同失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("创建合同成功", c)
}

// UpdateContract 更新合同
// @Tags Contract
// @Summary 更新合同
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param contractId path uint false "合同ID（新版API使用）"
// @Param data body dianjia.UpdateContractRequest true "更新合同请求"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /api/v1/dianjia/contract/{contractId} [put]
func (contractApi *ContractApi) UpdateContract(c *gin.Context) {
	var req dianjia.UpdateContractRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 从路径参数获取合同ID（新版API）
	contractIDStr := c.Param("contractId")
	if contractIDStr != "" {
		contractID, err := strconv.ParseUint(contractIDStr, 10, 32)
		if err != nil {
			response.FailWithMessage("无效的合同ID", c)
			return
		}
		req.ID = uint(contractID)
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	err = service.ServiceGroupApp.DianjiaServiceGroup.ContractService.UpdateContract(req, userID)
	if err != nil {
		global.GVA_LOG.Error("更新合同失败!", zap.Error(err))
		response.FailWithMessage("更新合同失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("更新合同成功", c)
}

// DeleteContract 删除合同
// @Tags Contract
// @Summary 删除合同
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param contractId path uint true "合同ID"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /api/v1/dianjia/contract/{contractId} [delete]
func (contractApi *ContractApi) DeleteContract(c *gin.Context) {
	// 优先从路径参数获取合同ID，兼容旧版本的query参数方式
	contractIDStr := c.Param("contractId")
	if contractIDStr == "" {
		contractIDStr = c.Query("contractId")
	}

	contractID, err := strconv.ParseUint(contractIDStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的合同ID", c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	err = service.ServiceGroupApp.DianjiaServiceGroup.ContractService.DeleteContract(uint(contractID), userID)
	if err != nil {
		global.GVA_LOG.Error("删除合同失败!", zap.Error(err))
		response.FailWithMessage("删除合同失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("删除合同成功", c)
}

// GetContractDetail 获取合同详情
// @Tags Contract
// @Summary 获取合同详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param contractId path uint true "合同ID"
// @Param userRole query string false "用户角色"
// @Success 200 {object} response.Response{data=dianjia.ContractResponse,msg=string} "获取成功"
// @Router /api/v1/dianjia/contract/{contractId} [get]
func (contractApi *ContractApi) GetContractDetail(c *gin.Context) {
	// 优先从路径参数获取合同ID，兼容旧版本的query参数方式
	contractIDStr := c.Param("contractId")
	if contractIDStr == "" {
		contractIDStr = c.Query("contractId")
	}

	contractID, err := strconv.ParseUint(contractIDStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的合同ID", c)
		return
	}

	// 获取当前用户ID进行权限验证
	userID := utils.GetUserID(c)

	contract, err := service.ServiceGroupApp.DianjiaServiceGroup.ContractService.GetContractDetailWithPermission(uint(contractID), userID)
	if err != nil {
		global.GVA_LOG.Error("获取合同详情失败!", zap.Error(err))
		response.FailWithMessage("获取合同详情失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(contract, "获取成功", c)
}

// GetContractsAsSetter 获取当前用户作为被点价方的合同列表
// @Tags Contract
// @Summary 获取当前用户作为被点价方的合同列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Param status query string false "合同状态，多个用逗号分隔"
// @Param startDate query string false "按创建时间筛选的开始日期，格式 YYYY-MM-DD"
// @Param endDate query string false "按创建时间筛选的结束日期，格式 YYYY-MM-DD"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /api/v1/dianjia/contracts/as-setter [get]
func (contractApi *ContractApi) GetContractsAsSetter(c *gin.Context) {
	var req dianjia.ContractAsSetterRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	list, total, err := service.ServiceGroupApp.DianjiaServiceGroup.ContractService.GetContractsAsSetter(req, userID)
	if err != nil {
		global.GVA_LOG.Error("获取合同列表失败!", zap.Error(err))
		response.FailWithMessage("获取合同列表失败", c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}

// GetContractsAsPricer 获取当前用户作为点价方的合同列表
// @Tags Contract
// @Summary 获取当前用户作为点价方的合同列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Param status query string false "合同状态，多个用逗号分隔"
// @Param startDate query string false "按创建时间筛选的开始日期，格式 YYYY-MM-DD"
// @Param endDate query string false "按创建时间筛选的结束日期，格式 YYYY-MM-DD"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /api/v1/dianjia/contracts/as-pricer [get]
func (contractApi *ContractApi) GetContractsAsPricer(c *gin.Context) {
	var req dianjia.ContractAsPricerRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	list, total, err := service.ServiceGroupApp.DianjiaServiceGroup.ContractService.GetContractsAsPricer(req, userID)
	if err != nil {
		global.GVA_LOG.Error("获取合同列表失败!", zap.Error(err))
		response.FailWithMessage("获取合同列表失败", c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}

// GetContractCancelRecords 获取合同取消记录
// @Tags Contract
// @Summary 获取合同取消记录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param contractId path uint true "合同ID"
// @Success 200 {object} response.Response{data=[]dianjia.ContractCancelRecord} "获取成功"
// @Router /api/v1/dianjia/contract/{contractId}/cancel-records [get]
func (contractApi *ContractApi) GetContractCancelRecords(c *gin.Context) {
	contractIDStr := c.Param("contractId")
	contractID, err := strconv.ParseUint(contractIDStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的合同ID", c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	records, err := service.ServiceGroupApp.DianjiaServiceGroup.ContractService.GetContractCancelRecords(uint(contractID), userID)
	if err != nil {
		global.GVA_LOG.Error("获取取消记录失败!", zap.Error(err))
		response.FailWithMessage("获取取消记录失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(records, "获取成功", c)
}

// ActivateContract 激活合同 (V3 新增)
// @Tags Contract
// @Summary 激活合同
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param contractId path uint true "合同ID"
// @Success 200 {object} response.Response{msg=string} "激活成功"
// @Router /api/v1/dianjia/contract/{contractId}/activate [post]
func (contractApi *ContractApi) ActivateContract(c *gin.Context) {
	contractIDStr := c.Param("contractId")
	contractID, err := strconv.ParseUint(contractIDStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的合同ID", c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	err = service.ServiceGroupApp.DianjiaServiceGroup.ContractService.ActivateContract(uint(contractID), userID)
	if err != nil {
		global.GVA_LOG.Error("激活合同失败!", zap.Error(err))
		response.FailWithMessage("激活合同失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("激活合同成功", c)
}

// DeactivateContract 挂起合同 (V3 新增)
// @Tags Contract
// @Summary 挂起合同
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param contractId path uint true "合同ID"
// @Success 200 {object} response.Response{msg=string} "挂起成功"
// @Router /api/v1/dianjia/contract/{contractId}/deactivate [post]
func (contractApi *ContractApi) DeactivateContract(c *gin.Context) {
	contractIDStr := c.Param("contractId")
	contractID, err := strconv.ParseUint(contractIDStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的合同ID", c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	err = service.ServiceGroupApp.DianjiaServiceGroup.ContractService.DeactivateContract(uint(contractID), userID)
	if err != nil {
		global.GVA_LOG.Error("挂起合同失败!", zap.Error(err))
		response.FailWithMessage("挂起合同失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("挂起合同成功", c)
}

// CancelContract 取消合同 (V3 新增)
// @Tags Contract
// @Summary 取消合同
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param contractId path uint true "合同ID"
// @Param data body dianjia.CancelContractRequest true "取消合同请求"
// @Success 200 {object} response.Response{msg=string} "取消成功"
// @Router /api/v1/dianjia/contract/{contractId}/cancel [post]
func (contractApi *ContractApi) CancelContract(c *gin.Context) {
	contractIDStr := c.Param("contractId")
	contractID, err := strconv.ParseUint(contractIDStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的合同ID", c)
		return
	}

	var req dianjia.CancelContractRequest
	err = c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	err = service.ServiceGroupApp.DianjiaServiceGroup.ContractService.CancelContract(uint(contractID), req.CancelQuantity, userID)
	if err != nil {
		global.GVA_LOG.Error("取消合同失败!", zap.Error(err))
		response.FailWithMessage("取消合同失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("取消合同成功", c)
}

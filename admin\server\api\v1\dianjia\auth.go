package dianjia

import (
	"time"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	systemRes "github.com/flipped-aurora/gin-vue-admin/server/model/system/response"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AuthApi struct{}

// SendLoginCode 发送登录验证码
// @Tags     User
// @Summary  发送手机登录验证码
// @Produce  application/json
// @Param    data  body      SendLoginCodeRequest                   true  "手机号"
// @Success  200   {object}  response.Response{msg=string}          "发送成功"
// @Router   /user/sendLoginCode [post]
func (a *AuthApi) SendLoginCode(c *gin.Context) {
	var req SendLoginCodeRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	
	// 验证手机号格式
	if !utils.VerifyMobileFormat(req.Phone) {
		response.FailWithMessage("手机号格式错误", c)
		return
	}
	
	err = service.ServiceGroupApp.DianjiaServiceGroup.AuthService.SendLoginCode(req.Phone)
	if err != nil {
		global.GVA_LOG.Error("发送验证码失败", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	
	response.OkWithMessage("验证码发送成功", c)
}

// LoginByPhone 手机号登录/注册
// @Tags     User
// @Summary  手机号验证码登录
// @Produce  application/json
// @Param    data  body      LoginByPhoneRequest                                       true  "手机号和验证码"
// @Success  200   {object}  response.Response{data=systemRes.LoginResponse,msg=string}  "登录成功"
// @Router   /user/loginByPhone [post]
func (a *AuthApi) LoginByPhone(c *gin.Context) {
	var req LoginByPhoneRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	
	// 验证手机号格式
	if !utils.VerifyMobileFormat(req.Phone) {
		response.FailWithMessage("手机号格式错误", c)
		return
	}
	
	// 验证码格式检查
	if len(req.Code) != 6 {
		response.FailWithMessage("验证码格式错误", c)
		return
	}
	
	user, err := service.ServiceGroupApp.DianjiaServiceGroup.AuthService.LoginByPhone(req.Phone, req.Code)
	if err != nil {
		global.GVA_LOG.Error("手机号登录失败", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	
	// 生成JWT Token，复用系统现有的TokenNext方法
	a.TokenNext(c, *user)
}

// TokenNext 登录成功后签发JWT（复用系统逻辑）
func (a *AuthApi) TokenNext(c *gin.Context, user system.SysUser) {
	token, claims, err := utils.LoginToken(&user)
	if err != nil {
		global.GVA_LOG.Error("获取token失败!", zap.Error(err))
		response.FailWithMessage("获取token失败", c)
		return
	}
	
	if !global.GVA_CONFIG.System.UseMultipoint {
		utils.SetToken(c, token, int(claims.RegisteredClaims.ExpiresAt.Unix()-time.Now().Unix()))
		response.OkWithDetailed(systemRes.LoginResponse{
			User:      user,
			Token:     token,
			ExpiresAt: claims.RegisteredClaims.ExpiresAt.Unix() * 1000,
		}, "登录成功", c)
		return
	}
	
	// 处理多点登录限制（如果启用）
	jwtStr, err := service.ServiceGroupApp.SystemServiceGroup.JwtService.GetRedisJWT(user.Username)
	if err != nil || jwtStr == "" {
		// 第一次登录或Redis中没有记录
		if err := utils.SetRedisJWT(token, user.Username); err != nil {
			global.GVA_LOG.Error("设置登录状态失败!", zap.Error(err))
			response.FailWithMessage("设置登录状态失败", c)
			return
		}
	} else {
		// 已有登录记录，将旧token加入黑名单
		var blackJWT system.JwtBlacklist
		blackJWT.Jwt = jwtStr
		if err := service.ServiceGroupApp.SystemServiceGroup.JwtService.JsonInBlacklist(blackJWT); err != nil {
			response.FailWithMessage("jwt作废失败", c)
			return
		}
		if err := utils.SetRedisJWT(token, user.Username); err != nil {
			response.FailWithMessage("设置登录状态失败", c)
			return
		}
	}
	
	utils.SetToken(c, token, int(claims.RegisteredClaims.ExpiresAt.Unix()-time.Now().Unix()))
	response.OkWithDetailed(systemRes.LoginResponse{
		User:      user,
		Token:     token,
		ExpiresAt: claims.RegisteredClaims.ExpiresAt.Unix() * 1000,
	}, "登录成功", c)
}

// GetProfile 获取当前用户信息
// @Tags     User
// @Summary  获取用户信息
// @Security ApiKeyAuth
// @Produce  application/json
// @Success  200  {object}  response.Response{data=system.SysUser,msg=string}  "获取成功"
// @Router   /user/getProfile [get]
func (a *AuthApi) GetProfile(c *gin.Context) {
	uuid := utils.GetUserUuid(c)
	user, err := service.ServiceGroupApp.DianjiaServiceGroup.AuthService.GetUserProfile(uuid)
	if err != nil {
		global.GVA_LOG.Error("获取用户信息失败", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	
	response.OkWithDetailed(gin.H{"userInfo": user}, "获取成功", c)
}

// UpdateProfile 更新当前用户信息
// @Tags     User
// @Summary  更新用户信息
// @Security ApiKeyAuth
// @Produce  application/json
// @Param    data  body      UpdateProfileRequest               true  "用户信息"
// @Success  200   {object}  response.Response{msg=string}      "更新成功"
// @Router   /user/updateProfile [put]
func (a *AuthApi) UpdateProfile(c *gin.Context) {
	var req UpdateProfileRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	
	uuid := utils.GetUserUuid(c)
	
	updateData := system.SysUser{
		NickName:       req.NickName,
		HeaderImg:      req.HeaderImg,
		CompanyName:    req.CompanyName,
		CompanyOrgId:   req.CompanyOrgId,
		CompanyAddress: req.CompanyAddress,
	}
	
	err = service.ServiceGroupApp.DianjiaServiceGroup.AuthService.UpdateUserProfile(uuid, updateData)
	if err != nil {
		global.GVA_LOG.Error("更新用户信息失败", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	
	response.OkWithMessage("更新成功", c)
}

// LoginByUsernamePassword 用户名密码登录
// @Tags     User
// @Summary  用户名密码登录
// @Produce  application/json
// @Param    data  body      LoginByUsernamePasswordRequest                            true  "用户名、密码和验证码"
// @Success  200   {object}  response.Response{data=systemRes.LoginResponse,msg=string}  "登录成功"
// @Router   /user/login [post]
func (a *AuthApi) LoginByUsernamePassword(c *gin.Context) {
	var req LoginByUsernamePasswordRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	
	// 验证图形验证码（如果需要的话）
	// 这里可以添加验证码验证逻辑
	
	user, err := service.ServiceGroupApp.DianjiaServiceGroup.AuthService.LoginByUsernamePassword(req.Username, req.Password)
	if err != nil {
		global.GVA_LOG.Error("用户名密码登录失败", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	
	// 生成JWT Token
	a.TokenNext(c, *user)
}

// LoginByWechat 微信登录/注册
// @Tags     User
// @Summary  微信登录
// @Produce  application/json
// @Param    data  body      LoginByWechatRequest                                      true  "微信code"
// @Success  200   {object}  response.Response{data=systemRes.LoginResponse,msg=string}  "登录成功"
// @Router   /user/loginByWechat [post]
func (a *AuthApi) LoginByWechat(c *gin.Context) {
	var req LoginByWechatRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	
	user, err := service.ServiceGroupApp.DianjiaServiceGroup.AuthService.LoginByWechat(req.Code)
	if err != nil {
		global.GVA_LOG.Error("微信登录失败", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	
	// 生成JWT Token
	a.TokenNext(c, *user)
}

// 请求结构体定义

type SendLoginCodeRequest struct {
	Phone string `json:"phone" binding:"required"` // 手机号
}

type LoginByPhoneRequest struct {
	Phone string `json:"phone" binding:"required"` // 手机号
	Code  string `json:"code" binding:"required"`  // 验证码
}

type LoginByUsernamePasswordRequest struct {
	Username string `json:"username" binding:"required"` // 用户名
	Password string `json:"password" binding:"required"` // 密码
	Captcha  string `json:"captcha"`                     // 图形验证码（可选）
	CaptchaId string `json:"captchaId"`                  // 验证码ID（可选）
}

type LoginByWechatRequest struct {
	Code string `json:"code" binding:"required"` // 微信授权码
}

type UpdateProfileRequest struct {
	NickName       string `json:"nickName"`       // 昵称
	HeaderImg      string `json:"headerImg"`      // 头像
	CompanyName    string `json:"companyName"`    // 企业名称
	CompanyOrgId   string `json:"companyOrgId"`   // 企业组织编码ID
	CompanyAddress string `json:"companyAddress"` // 企业地址
}
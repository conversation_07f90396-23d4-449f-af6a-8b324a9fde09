#!/usr/bin/env python3
"""
调试登录样式
"""

import sys
import logging
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

from PySide6.QtWidgets import QApplication
from vnpy.event import EventEngine

# 导入认证和UI模块
from auth import LoginManager
from ui import LoginWindow

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )

def debug_login_styles():
    """调试登录样式"""
    logger = logging.getLogger(__name__)
    logger.info("开始调试登录样式")
    
    # 创建QApplication
    app = QApplication(sys.argv)
    app.setApplicationName("Order登录样式调试")
    
    # 创建事件引擎
    event_engine = EventEngine()
    event_engine.start()
    
    try:
        # 创建登录管理器
        login_manager = LoginManager(event_engine)
        
        # 创建登录窗口
        login_window = LoginWindow(event_engine, login_manager)
        
        # 输出当前应用的样式
        current_style = login_window.styleSheet()
        logger.info(f"当前样式长度: {len(current_style)} 字符")
        
        # 输出样式的前1000个字符
        logger.info("当前应用的样式预览:")
        logger.info(current_style[:1000])
        
        # 检查关键样式元素
        if "qlineargradient" in current_style:
            logger.info("✓ 渐变样式已应用")
        else:
            logger.warning("✗ 渐变样式未找到")
            
        if "border-radius: 16px" in current_style:
            logger.info("✓ 现代化圆角已应用")
        else:
            logger.warning("✗ 现代化圆角未找到")
            
        if "#667eea" in current_style:
            logger.info("✓ 新的主色调已应用")
        else:
            logger.warning("✗ 新的主色调未找到")
        
        # 显示登录窗口
        login_window.show_centered()
        
        # 连接信号
        def on_login_success(result):
            logger.info(f"登录成功：{result.user_info}")
            app.quit()
        
        def on_login_cancelled():
            logger.info("用户取消登录")
            app.quit()
        
        login_window.login_success.connect(on_login_success)
        login_window.login_cancelled.connect(on_login_cancelled)
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        logger.error(f"调试过程中发生错误：{str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    finally:
        # 停止事件引擎
        event_engine.stop()
        logger.info("调试结束")

def main():
    """主函数"""
    setup_logging()
    return debug_login_styles()

if __name__ == "__main__":
    sys.exit(main())

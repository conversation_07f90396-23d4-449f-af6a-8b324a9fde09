package dianjia

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
)

// ContractCancelRecord 合同取消记录表
type ContractCancelRecord struct {
	global.GVA_MODEL
	// 关联信息
	ContractID uint     `json:"contractID" gorm:"column:contract_id;not null;comment:合同ID"`
	Contract   Contract `json:"contract" gorm:"foreignKey:ContractID"`
	UserID     uint     `json:"userID" gorm:"column:user_id;not null;comment:操作用户ID"`
	User       system.SysUser `json:"user" gorm:"foreignKey:UserID"`

	// 取消信息
	CancelQuantity int    `json:"cancelQuantity" gorm:"column:cancel_quantity;not null;comment:取消数量"`
	Reason         string `json:"reason" gorm:"column:reason;type:text;comment:取消原因"`

	// 合同状态信息（记录取消时的合同状态）
	BeforeCancelRemainingQuantity int             `json:"beforeCancelRemainingQuantity" gorm:"column:before_cancel_remaining_quantity;not null;comment:取消前剩余数量"`
	AfterCancelRemainingQuantity  int             `json:"afterCancelRemainingQuantity" gorm:"column:after_cancel_remaining_quantity;not null;comment:取消后剩余数量"`
	ContractStatusAfterCancel     ContractStatus  `json:"contractStatusAfterCancel" gorm:"column:contract_status_after_cancel;type:varchar(50);not null;comment:取消后合同状态"`
}

func (ContractCancelRecord) TableName() string {
	return "dj_contract_cancel_records"
}

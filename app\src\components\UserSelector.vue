<script lang="ts" setup>
import type { IUserSelectable } from '@/types'
import { ref, computed, watch } from 'vue'
import { getSelectableList, getSelectableProfile, type IPageResult } from '@/api/user'
import { toast } from '@/utils/toast'

// 组件属性
interface Props {
  modelValue?: number
  label?: string
  placeholder?: string
  customClass?: string
  customLabelClass?: string
  customValueClass?: string
}

// 组件事件
interface Emits {
  (e: 'update:modelValue', value: number): void
  (e: 'change', user: IUserSelectable | null): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: 0,
  label: '用户选择',
  placeholder: '请选择用户',
  customClass: '',
  customLabelClass: '',
  customValueClass: '',
})

const emit = defineEmits<Emits>()

// 响应式数据
const showModal = ref(false)
const searchKeyword = ref('')
const selectedUser = ref<IUserSelectable | null>(null)
const availableUsers = ref<IUserSelectable[]>([])
const isLoading = ref(false)

// 计算属性
const currentUserId = computed({
  get: () => props.modelValue,
  set: (value: number) => {
    emit('update:modelValue', value)
  }
})

// 方法
async function loadAvailableUsers() {
  try {
    isLoading.value = true
    const res = await getSelectableList({ 
      search: searchKeyword.value || undefined,
      page: 1,
      pageSize: 200 // 测试阶段：一次性获取所有用户
    })
    availableUsers.value = res.data.list
  } catch (error) {
    console.error('获取用户列表失败:', error)
    toast.error('获取用户列表失败')
    availableUsers.value = []
  } finally {
    isLoading.value = false
  }
}

function showUserPicker() {
  loadAvailableUsers()
  showModal.value = true
}

function searchUsers() {
  // 搜索用户时重新加载列表
  loadAvailableUsers()
}

function confirmUserSelection(user: IUserSelectable) {
  selectedUser.value = user
  currentUserId.value = user.ID
  showModal.value = false
  emit('change', user)
}

function removeUser() {
  selectedUser.value = null
  currentUserId.value = 0
  emit('change', null)
}

// 初始化时根据 modelValue 设置选中用户
async function initSelectedUser() {
  if (props.modelValue && props.modelValue > 0) {
    try {
      const res = await getSelectableProfile(props.modelValue)
      selectedUser.value = res.data
    } catch (error) {
      console.error('获取用户信息失败:', error)
      selectedUser.value = null
    }
  }
}

// 监听 modelValue 变化
watch(() => props.modelValue, async (newValue) => {
  if (newValue && newValue > 0 && (!selectedUser.value || selectedUser.value.ID !== newValue)) {
    await initSelectedUser()
  } else if (!newValue || newValue === 0) {
    selectedUser.value = null
  }
}, { immediate: true })
</script>

<template>
  <view :class="['user-selector-compact', customClass]">
    <view class="user-content">
      <view v-if="!selectedUser" class="empty-user-compact">
        {{ placeholder }}
      </view>
      <view v-else class="selected-user-compact">
        <text class="user-name">
          {{ selectedUser.nickName }}
        </text>
        <text class="user-phone">
          {{ selectedUser.phone }}
        </text>
      </view>
    </view>
    <wd-button v-if="!selectedUser" type="primary" size="small" custom-class="dj-btn-primary" @click="showUserPicker">
      选择
    </wd-button>
    <wd-button v-else type="error" size="small" custom-class="dj-btn-danger" @click="removeUser">
      移除
    </wd-button>

    <!-- 用户选择弹窗 -->
    <wd-popup v-model="showModal" position="bottom" custom-style="height: 70%" custom-class="dj-popup">
      <view class="user-picker">
        <view class="picker-header">
          <text class="picker-title">
            {{ label }}
          </text>
        </view>

        <view class="picker-content">
          <wd-search v-model="searchKeyword" placeholder="搜索用户" custom-class="dj-search" @search="searchUsers" />

          <view class="user-list">
            <view v-if="isLoading" class="loading-container">
              <wd-loading type="ring" color="#667eea" />
              <text class="loading-text">加载中...</text>
            </view>
            <view v-else-if="availableUsers.length === 0" class="empty-container">
              <text class="empty-text">暂无用户数据</text>
            </view>
            <view v-else>
              <view v-for="user in availableUsers" :key="user.ID" class="user-item" @click="confirmUserSelection(user)">
                <view class="user-info">
                  <text class="user-name">
                    {{ user.nickName }}
                  </text>
                  <text class="user-phone">
                    {{ user.phone }}
                  </text>
                </view>
                <wd-radio :value="selectedUser?.ID === user.ID" custom-class="dj-radio" />
              </view>
            </view>
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<style lang="scss" scoped>
// 基础变量
$primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$primary-color: #667eea;
$secondary-color: #764ba2;
$text-primary: #303133;
$text-secondary: #606266;
$text-light: #909399;
$font-size-large: 32rpx;
$font-size-medium: 28rpx;
$font-size-title: 36rpx;
$box-shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
$transition-base: all 0.3s ease;

// 用户选择器
.user-selector-compact {
  display: flex;
  align-items: center;
  gap: 20rpx;
  background-color: #f9fafc;
  padding: 24rpx;
  border-radius: 8rpx;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4rpx;
    background: linear-gradient(180deg, $secondary-color, $primary-color);
  }

  .user-content {
    flex: 1;

    .empty-user-compact {
      color: $text-light;
      font-size: $font-size-medium;
      font-style: italic;
    }

    .selected-user-compact {
      display: flex;
      align-items: center;
      gap: 15rpx;
      background-color: rgba(102, 126, 234, 0.05);
      padding: 12rpx 16rpx;
      border-radius: 8rpx;

      .user-name {
        font-size: $font-size-medium;
        color: $text-primary;
      }

      .user-phone {
        font-size: $font-size-medium;
        color: $text-secondary;
      }
    }
  }
}

// 弹窗选择器
.user-picker {
  height: 100%;
  display: flex;
  flex-direction: column;

  .picker-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    background: $primary-gradient;

    .picker-title {
      font-size: $font-size-title;
      font-weight: bold;
      color: #ffffff;
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
    }
  }

  .picker-content {
    flex: 1;
    padding: 20rpx;
    overflow-y: auto;
    background-color: #f9fafc;

    .user-list {
      margin-top: 20rpx;

      .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 60rpx 0;
        
        .loading-text {
          margin-top: 20rpx;
          font-size: $font-size-medium;
          color: $text-secondary;
        }
      }

      .empty-container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 60rpx 0;
        
        .empty-text {
          font-size: $font-size-medium;
          color: $text-light;
        }
      }

      .user-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 25rpx;
        background: white;
        border-radius: 8rpx;
        margin-bottom: 15rpx;
        box-shadow: $box-shadow-sm;
        position: relative;
        overflow: hidden;
        transition: $transition-base;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 4rpx;
          background: linear-gradient(180deg, $primary-color, $secondary-color);
        }

        &:hover {
          background-color: #f9fafc;
          box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.15);
        }

        .user-info {
          .user-name {
            font-size: $font-size-large;
            color: $text-primary;
            font-weight: 500;
            margin-right: 20rpx;
          }

          .user-phone {
            font-size: $font-size-medium;
            color: $text-secondary;
          }
        }
      }
    }
  }
}

// 深度选择器样式
:deep() {
  .dj-btn-primary {
    background: $primary-gradient;
    border: none;
    color: white;
    font-weight: 500;
    box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
    transition: $transition-base;

    &:hover {
      box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
      transform: translateY(-1rpx);
    }
  }

  .dj-btn-danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    border: none;
    color: white;
    font-weight: 500;
    box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
    transition: $transition-base;

    &:hover {
      box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.4);
      transform: translateY(-1rpx);
    }
  }

  .dj-search {
    background: white;
    border-radius: 40rpx;
    padding: 0 16rpx;
    box-shadow: $box-shadow-sm;
    transition: $transition-base;

    &:focus-within {
      box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.15);
    }
  }

  .dj-radio {
    .wd-radio__label {
      color: $text-primary;
      font-size: $font-size-medium;
      font-weight: 500;
      padding-left: 12rpx;
    }

    .wd-radio__shape {
      border-color: #c0c4cc;
      transition: $transition-base;

      &.is-checked {
        background-color: $primary-color;
        border-color: $primary-color;
        box-shadow: 0 0 4rpx rgba(102, 126, 234, 0.3);
      }
    }
  }

  .dj-popup {
    border-radius: 20rpx 20rpx 0 0;
    overflow: hidden;
  }
}
</style>

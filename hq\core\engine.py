"""
核心引擎 - 重构为依赖注入架构的业务流程编排器
不再直接依赖具体实现，而是通过构造函数接收适配器实例
"""
import json
import logging
import threading
from datetime import datetime
from typing import Optional, List, Callable, Dict, Any

from vnpy.event import EventEngine, Event
from vnpy.trader.constant import Exchange
from vnpy.trader.object import TickData, LogData

from adapters.vnpy_adapter import VnpyAdapter
from adapters.redis_adapter import RedisAdapter
from core.server_manager import ServerManager
from core.instrument_manager import InstrumentManager, get_instrument
from infrastructure.logging import get_logger
from infrastructure.config import get_config


class Engine:
    """
    HQ系统核心引擎 - 采用依赖注入架构
    负责协调各个组件，不直接依赖具体实现
    """
    
    def __init__(self, 
                 vnpy_adapter: VnpyAdapter,
                 redis_adapter: RedisAdapter,
                 server_manager: ServerManager,
                 instrument_manager: InstrumentManager,
                 config: Optional[Dict[str, Any]] = None):
        """
        初始化核心引擎
        
        Args:
            vnpy_adapter: VNPY适配器实例
            redis_adapter: Redis适配器实例
            server_manager: 服务器管理器实例
            instrument_manager: 合约管理器实例
            config_manager: 配置管理器实例
        """
        self.vnpy_adapter = vnpy_adapter
        self.redis_adapter = redis_adapter
        self.server_manager = server_manager
        self.instrument_manager = instrument_manager
        self.config = config or get_config()
        self.logger = get_logger("engine")
        self.is_running = False
        self.is_connected = False
        self.subscribed_instruments: List[tuple] = []
        self.current_broker_info: Optional[Dict[str, Any]] = None
        self.connection_config: Optional[Dict[str, Any]] = None
        self.tick_callbacks: List[Callable[[TickData], None]] = []
        self.connection_callbacks: List[Callable[[bool], None]] = []
        
        # 定义缓存更新事件类型
        self.EVENT_UPDATE_CACHE = "eUpdateCache"
        
        self._setup_adapter_callbacks()
        self._setup_cache_event_handlers()
        self.logger.info("核心引擎初始化完成 - 依赖注入架构")
    
    def _setup_adapter_callbacks(self):
        """设置适配器回调函数"""
        self.vnpy_adapter.set_tick_callback(self._on_tick_data)
        self.vnpy_adapter.set_log_callback(self._on_log_data)
        self.vnpy_adapter.set_connection_callback(self._on_connection_status)
    
    def _setup_cache_event_handlers(self):
        """设置缓存事件处理器"""
        self.vnpy_adapter.engine.register(self.EVENT_UPDATE_CACHE, self._process_cache_update_event)
    
    def _on_tick_data(self, tick: TickData):
        """处理行情数据"""
        try:
            if tick.symbol == "IF2503":
                now = datetime.now()
                self.logger.info(f"接收到IF2503行情：{tick}")
            
            def format_val(value):
                if isinstance(value, datetime):
                    return value.isoformat()
                elif isinstance(value, Exchange):
                    return value.value
                return str(value)

            data_dict = {k: format_val(v) for k, v in tick.__dict__.items()}
            
            success = self.redis_adapter.store_tick_data(tick.symbol, data_dict)
            if success:
                self.redis_adapter.publish_tick_update(tick.symbol)
            
            for callback in self.tick_callbacks:
                try:
                    callback(tick)
                except Exception as e:
                    self.logger.error(f"行情回调执行失败: {e}")
                    
        except Exception as e:
            self.logger.error(f"处理行情数据失败: {e}")
    
    def _on_log_data(self, log: LogData):
        """处理日志数据"""
        if log.level == logging.ERROR:
            self.logger.error(f"CTP: {log.msg}")
        elif log.level == logging.WARNING:
            self.logger.warning(f"CTP: {log.msg}")
        else:
            self.logger.info(f"CTP: {log.msg}")
    
    def _on_connection_status(self, connected: bool):
        """处理连接状态变化"""
        self.is_connected = connected
        self.logger.info(f"CTP连接状态变更: {connected}")
        for callback in self.connection_callbacks:
            try:
                callback(connected)
            except Exception as e:
                self.logger.error(f"连接状态回调执行失败: {e}")
    
    def _process_cache_update_event(self, event: Event):
        """处理缓存更新事件"""
        def update_cache():
            try:
                self.logger.info("开始后台更新合约缓存...")
                self.instrument_manager.update_cache_sync()
                self.logger.info("合约缓存更新完成")
            except Exception as e:
                self.logger.error(f"缓存更新失败: {e}")
        
        # 在新线程中执行缓存更新
        thread = threading.Thread(target=update_cache, daemon=True)
        thread.start()
    
    def add_tick_callback(self, callback: Callable[[TickData], None]):
        """添加行情数据回调函数"""
        self.tick_callbacks.append(callback)
        self.logger.info("已添加行情数据回调函数")
    
    def add_connection_callback(self, callback: Callable[[bool], None]):
        """添加连接状态回调函数"""
        self.connection_callbacks.append(callback)
        self.logger.info("已添加连接状态回调函数")
    
    def initialize(self) -> bool:
        """初始化引擎组件"""
        try:
            self.logger.info("开始初始化核心引擎组件...")
            if not self.redis_adapter.connect():
                self.logger.error("Redis连接失败")
                return False
            
            if not self.instrument_manager.instruments:
                success, needs_update = self.instrument_manager.load_instruments_sync()
                if not success:
                    self.logger.error("合约信息加载失败")
                    return False
                
                # 检查是否需要后台更新缓存
                if needs_update:
                    event = Event(self.EVENT_UPDATE_CACHE)
                    self.vnpy_adapter.engine.put(event)
                    self.logger.info("已推送缓存更新事件")
            
            self.logger.info(f"已加载 {len(self.instrument_manager.instruments)} 个合约")
            
            address_config = self.config.get("address", {})
            like_broker_id = address_config.get("like_broker_id")
            exclude_broker_id_raw = address_config.get("exclude_broker_id")
            exclude_broker_ids = []
            if isinstance(exclude_broker_id_raw, int):
                exclude_broker_ids = [str(exclude_broker_id_raw)]
            elif isinstance(exclude_broker_id_raw, str):
                exclude_broker_ids = exclude_broker_id_raw.split()
            elif isinstance(exclude_broker_id_raw, list):
                exclude_broker_ids = [str(item) for item in exclude_broker_id_raw]

            broker_ids = [like_broker_id] if like_broker_id else None
            
            self.current_broker_info = self.server_manager.select_best_broker(
                broker_ids=broker_ids,
                exclude_broker_ids=exclude_broker_ids
            )
            
            if not self.current_broker_info:
                self.logger.error("未找到可用的期货公司服务器")
                return False
            
            self.logger.info(f"选择期货公司: {self.current_broker_info['broker_name']} ({self.current_broker_info['broker_id']})")
            
            ctp_config = self.config.get("ctp", {})
            self.connection_config = {
                'addresses': self.current_broker_info['addresses'],
                'user_id': ctp_config.get('user_id', '00000'),
                'password': ctp_config.get('password', '000000'),
                'broker_id': self.current_broker_info['broker_id']
            }
            
            self.logger.info("核心引擎初始化完成")
            return True
        except Exception as e:
            self.logger.error(f"核心引擎初始化失败: {e}")
            return False
    
    def start(self) -> bool:
        """启动引擎"""
        try:
            if self.is_running:
                self.logger.warning("引擎已在运行中")
                return True
            
            self.logger.info("启动核心引擎...")
            if not self.connection_config:
                self.logger.error("连接配置未准备好，请先调用initialize()")
                return False
            
            success = self.vnpy_adapter.connect(
                addresses=self.connection_config['addresses'],
                user_id=self.connection_config['user_id'],
                password=self.connection_config['password'],
                broker_id=self.connection_config['broker_id']
            )
            
            if not success:
                self.logger.error("CTP连接失败")
                return False
            
            instruments = get_instrument()
            self.subscribed_instruments = instruments
            success_count = self.vnpy_adapter.subscribe_batch(instruments)
            self.logger.info(f"成功订阅 {success_count}/{len(instruments)} 个合约")
            self.is_running = True
            self.logger.info("核心引擎启动成功")
            return True
        except Exception as e:
            self.logger.error(f"启动核心引擎失败: {e}")
            return False
    
    def stop(self):
        """停止引擎"""
        try:
            self.logger.info("停止核心引擎...")
            self.vnpy_adapter.close()
            self.redis_adapter.disconnect()
            self.is_running = False
            self.is_connected = False
            self.logger.info("核心引擎已停止")
        except Exception as e:
            self.logger.error(f"停止核心引擎失败: {e}")
    
    def subscribe_instrument(self, symbol: str, exchange: Exchange) -> bool:
        """订阅单个合约"""
        try:
            success = self.vnpy_adapter.subscribe(symbol, exchange)
            if success:
                self.subscribed_instruments.append((symbol, exchange.value))
                self.logger.info(f"成功订阅合约: {symbol}")
            return success
        except Exception as e:
            self.logger.error(f"订阅合约失败 {symbol}: {e}")
            return False
    
    def unsubscribe_instrument(self, symbol: str):
        """取消订阅合约"""
        try:
            self.vnpy_adapter.unsubscribe(symbol)
            self.subscribed_instruments = [(s, e) for s, e in self.subscribed_instruments if s != symbol]
            self.logger.info(f"取消订阅合约: {symbol}")
        except Exception as e:
            self.logger.error(f"取消订阅合约失败 {symbol}: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取引擎状态信息"""
        return {
            'is_running': self.is_running,
            'is_connected': self.is_connected,
            'subscribed_count': len(self.subscribed_instruments),
            'current_broker': self.current_broker_info['broker_name'] if self.current_broker_info else None,
            'broker_id': self.current_broker_info['broker_id'] if self.current_broker_info else None,
            'vnpy_status': self.vnpy_adapter.get_connection_status(),
            'redis_status': self.redis_adapter.get_connection_status(),
            'instrument_count': len(self.instrument_manager.instruments) if self.instrument_manager.instruments else 0
        }
    
    def get_subscribed_instruments(self) -> List[tuple]:
        """获取已订阅的合约列表"""
        return self.subscribed_instruments.copy()
    
    def get_tick_data(self, symbol: str) -> Optional[Dict]:
        """获取合约的最新行情数据"""
        return self.redis_adapter.get_tick_data(symbol)
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        health_status = {
            'engine': self.is_running,
            'ctp_connection': self.is_connected,
            'redis_connection': self.redis_adapter.is_connection_valid(),
            'subscriptions': len(self.subscribed_instruments),
            'instruments_loaded': len(self.instrument_manager.instruments) > 0,
            'timestamp': datetime.now().isoformat()
        }
        
        overall_healthy = all(health_status.values())
        health_status['overall_healthy'] = overall_healthy
        
        if not overall_healthy:
            self.logger.warning(f"健康检查发现问题: {health_status}")
        
        return health_status
    
    def restart_connection(self) -> bool:
        """重启连接（用于故障恢复）"""
        try:
            self.logger.info("开始重启连接...")
            self.vnpy_adapter.close()
            if self.initialize():
                return self.start()
            return False
        except Exception as e:
            self.logger.error(f"重启连接失败: {e}")
            return False


def create_engine() -> Engine:
    """创建标准配置的引擎实例"""
    config = get_config()
    redis_config = config.get("redis", {})
    event_engine = EventEngine()
    vnpy_adapter = VnpyAdapter(event_engine)
    redis_adapter = RedisAdapter(**redis_config)
    server_manager = ServerManager()
    instrument_manager = InstrumentManager()
    
    return Engine(
        vnpy_adapter=vnpy_adapter,
        redis_adapter=redis_adapter,
        server_manager=server_manager,
        instrument_manager=instrument_manager,
        config=config
    )


if __name__ == "__main__":
    import time
    from infrastructure.config import init_config
    init_config()
    
    def test_engine():
        engine = create_engine()
        
        def on_tick(tick):
            print(f"收到行情: {tick.symbol} {tick.last_price}")
        
        def on_connection(connected):
            print(f"连接状态: {'已连接' if connected else '已断开'}")
        
        engine.add_tick_callback(on_tick)
        engine.add_connection_callback(on_connection)
        
        if engine.initialize():
            print("引擎初始化成功")
            if engine.start():
                print("引擎启动成功")
                try:
                    for i in range(30):
                        status = engine.get_status()
                        print(f"引擎状态: {status}")
                        health = engine.health_check()
                        print(f"健康状态: {health}")
                        time.sleep(2)
                except KeyboardInterrupt:
                    print("停止测试")
                engine.stop()
                print("引擎已停止")
            else:
                print("引擎启动失败")
        else:
            print("引擎初始化失败")
    
    test_engine()
#!/usr/bin/env python3
"""
测试样式加载
"""

import sys
import logging
from PySide6.QtWidgets import QApplication, QDialog, QVBoxLayout, QLabel, QPushButton, QLineEdit
from PySide6.QtCore import Qt

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_styles():
    """测试样式加载"""
    try:
        # 导入样式
        from ui.styles.login_styles import LoginStyles
        from ui.styles.common_styles import CommonStyles
        
        logger.info("样式模块导入成功")
        
        # 创建样式实例
        common_styles = CommonStyles()
        login_styles = LoginStyles()
        
        logger.info("样式实例创建成功")
        
        # 获取样式内容
        complete_style = login_styles.get_complete_login_style()
        logger.info(f"完整样式长度: {len(complete_style)} 字符")
        
        # 打印样式的前500个字符
        logger.info("样式内容预览:")
        logger.info(complete_style[:500])
        
        # 检查颜色配置
        logger.info("颜色配置:")
        for key, value in common_styles.colors.items():
            logger.info(f"  {key}: {value}")
        
        # 检查字体配置
        logger.info("字体配置:")
        for key, value in common_styles.fonts.items():
            logger.info(f"  {key}: {value}")
            
        return True
        
    except Exception as e:
        logger.error(f"样式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_style_application():
    """测试样式应用"""
    app = QApplication(sys.argv)
    
    try:
        from ui.styles.login_styles import LoginStyles
        
        # 创建测试窗口
        dialog = QDialog()
        dialog.setWindowTitle("样式测试")
        dialog.setFixedSize(400, 300)
        
        layout = QVBoxLayout(dialog)
        
        # 添加测试组件
        title = QLabel("样式测试窗口")
        title.setObjectName("titleLabel")
        
        input_field = QLineEdit()
        input_field.setPlaceholderText("测试输入框")
        input_field.setObjectName("usernameEdit")
        
        primary_btn = QPushButton("主要按钮")
        primary_btn.setObjectName("primaryButton")
        
        secondary_btn = QPushButton("次要按钮")
        secondary_btn.setObjectName("secondaryButton")
        
        layout.addWidget(title)
        layout.addWidget(input_field)
        layout.addWidget(primary_btn)
        layout.addWidget(secondary_btn)
        
        # 应用样式
        login_styles = LoginStyles()
        style_sheet = login_styles.get_complete_login_style()
        
        logger.info("应用样式...")
        dialog.setStyleSheet(style_sheet)
        
        # 显示窗口
        dialog.show()
        
        logger.info("样式测试窗口已显示")
        
        # 运行应用
        return app.exec()
        
    except Exception as e:
        logger.error(f"样式应用测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    logger.info("开始样式测试...")
    
    # 测试样式加载
    if test_styles():
        logger.info("样式加载测试通过")
        
        # 测试样式应用
        logger.info("开始样式应用测试...")
        sys.exit(test_style_application())
    else:
        logger.error("样式加载测试失败")
        sys.exit(1)

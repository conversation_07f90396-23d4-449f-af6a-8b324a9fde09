package ws_hub

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"go.uber.org/zap"
)

const (
	writeWait      = 10 * time.Second
	pongWait       = 60 * time.Second
	pingPeriod     = (pongWait * 9) / 10
	maxMessageSize = 1024
)

var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
}

// MessageEnvelope 定义了App客户端与服务器之间消息的基础结构
type MessageEnvelope struct {
	Event     string      `json:"event"`
	Payload   interface{} `json:"payload,omitempty"`
	Seq       *int64      `json:"seq,omitempty"`
	Timestamp int64       `json:"timestamp"`
}

// AppClient 代表一个App端的WebSocket连接
type AppClient struct {
	hub                 *AppHub
	conn                *websocket.Conn
	send                chan []byte
	userID              uint
	subscriptions       map[string]bool
	marketSubscriptions map[string]bool // 行情订阅关系
	mu                  sync.RWMutex
}

// AppHub 负责管理所有App客户端连接和消息广播
type AppHub struct {
	clients             map[*AppClient]bool
	broadcast           chan []byte
	register            chan *AppClient
	unregister          chan *AppClient
	marketSubscriptions map[string]map[*AppClient]bool // 行情订阅关系: symbol -> clients
	mu                  sync.RWMutex                    // 保护marketSubscriptions的读写
}

// NewAppHub 创建新的App端WebSocket Hub
func NewAppHub() *AppHub {
	hub := &AppHub{
		broadcast:           make(chan []byte),
		register:            make(chan *AppClient),
		unregister:          make(chan *AppClient),
		clients:             make(map[*AppClient]bool),
		marketSubscriptions: make(map[string]map[*AppClient]bool),
	}

	// 启动Redis监听器
	go hub.startRedisListener()

	return hub
}

// Run 启动App Hub的主循环
func (h *AppHub) Run() {
	for {
		select {
		case client := <-h.register:
			h.clients[client] = true
			global.GVA_LOG.Info("App client connected", zap.Uint("userID", client.userID))
		case client := <-h.unregister:
			if _, ok := h.clients[client]; ok {
				delete(h.clients, client)
				close(client.send)

				// 清理该客户端的所有行情订阅
				h.cleanupClientMarketSubscriptions(client)

				global.GVA_LOG.Info("App client disconnected", zap.Uint("userID", client.userID))
			}
		case message := <-h.broadcast:
			for client := range h.clients {
				select {
				case client.send <- message:
				default:
					close(client.send)
					delete(h.clients, client)
				}
			}
		}
	}
}

// ServeWsForApp 处理App端的WebSocket连接请求
func ServeWsForApp(hub *AppHub, c *gin.Context) {
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		global.GVA_LOG.Error("Failed to upgrade App connection", zap.Error(err))
		return
	}

	client := &AppClient{
		hub:                 hub,
		conn:                conn,
		send:                make(chan []byte, 256),
		subscriptions:       make(map[string]bool),
		marketSubscriptions: make(map[string]bool),
	}
	client.hub.register <- client

	go client.writePump()
	go client.readPump()
}

func (c *AppClient) readPump() {
	defer func() {
		c.hub.unregister <- c
		c.conn.Close()
	}()

	c.conn.SetReadLimit(maxMessageSize)
	c.conn.SetReadDeadline(time.Now().Add(pongWait))
	c.conn.SetPongHandler(func(string) error {
		c.conn.SetReadDeadline(time.Now().Add(pongWait))
		return nil
	})

	for {
		_, message, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				global.GVA_LOG.Warn("App WebSocket unexpected close error", zap.Error(err))
			}
			break
		}
		c.handleMessageForApp(message)
	}
}

func (c *AppClient) writePump() {
	ticker := time.NewTicker(pingPeriod)
	defer func() {
		ticker.Stop()
		c.conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.send:
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if !ok {
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := c.conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)

			if err := w.Close(); err != nil {
				return
			}

		case <-ticker.C:
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

func (c *AppClient) handleMessageForApp(message []byte) {
	var msg MessageEnvelope
	if err := json.Unmarshal(message, &msg); err != nil {
		global.GVA_LOG.Error("Failed to unmarshal App message", zap.Error(err))
		return
	}

	log.Printf("App Received Event: %s, Payload: %v", msg.Event, msg.Payload)

	switch msg.Event {
	case "ping":
		c.sendResponse("pong", msg.Payload, nil)
	case "auth":
		// 集成真实的JWT验证逻辑
		if payload, ok := msg.Payload.(map[string]interface{}); ok {
			if tokenStr, ok := payload["token"].(string); ok && tokenStr != "" {
				// 创建JWT实例并验证token
				j := utils.NewJWT()
				claims, err := j.ParseToken(tokenStr)
				if err != nil {
					// Token验证失败
					global.GVA_LOG.Error("WebSocket JWT验证失败", zap.Error(err))
					c.sendResponse("auth_response", gin.H{
						"success": false,
						"message": "Token验证失败: " + err.Error(),
					}, nil)
					return
				}

				// Token验证成功，设置用户ID
				c.userID = claims.BaseClaims.ID
				global.GVA_LOG.Info("WebSocket用户认证成功",
					zap.Uint("userID", c.userID),
					zap.String("username", claims.BaseClaims.Username),
					zap.String("nickname", claims.BaseClaims.NickName))

				c.sendResponse("auth_response", gin.H{
					"success": true,
					"user": gin.H{
						"id":       claims.BaseClaims.ID,
						"username": claims.BaseClaims.Username,
						"nickname": claims.BaseClaims.NickName,
					},
				}, nil)
			} else {
				// Token为空或格式错误
				c.sendResponse("auth_response", gin.H{
					"success": false,
					"message": "Token不能为空",
				}, nil)
			}
		} else {
			// Payload格式错误
			c.sendResponse("auth_response", gin.H{
				"success": false,
				"message": "认证数据格式错误",
			}, nil)
		}
	case "subscribe":
		if payload, ok := msg.Payload.(map[string]interface{}); ok {
			if channel, ok := payload["channel"].(string); ok {
				c.mu.Lock()
				c.subscriptions[channel] = true
				c.mu.Unlock()
			}
		}
	case "unsubscribe":
		if payload, ok := msg.Payload.(map[string]interface{}); ok {
			if channel, ok := payload["channel"].(string); ok {
				c.mu.Lock()
				delete(c.subscriptions, channel)
				c.mu.Unlock()
			}
		}
	case "subscribe_market":
		c.handleMarketSubscribe(msg.Payload)
	case "unsubscribe_market":
		c.handleMarketUnsubscribe(msg.Payload)
	}
}

func (c *AppClient) sendResponse(event string, payload interface{}, seq *int64) {
	resp := MessageEnvelope{
		Event:     event,
		Payload:   payload,
		Seq:       seq,
		Timestamp: time.Now().UnixMilli(),
	}
	msgBytes, _ := json.Marshal(resp)
	c.send <- msgBytes
}

// BroadcastToApp 向所有App客户端广播消息
func (h *AppHub) BroadcastToApp(event string, payload interface{}) {
	msg := MessageEnvelope{
		Event:     event,
		Payload:   payload,
		Timestamp: time.Now().UnixMilli(),
	}
	msgBytes, _ := json.Marshal(msg)
	h.broadcast <- msgBytes
}

// GetAppClientCount 获取当前App客户端连接数
func (h *AppHub) GetAppClientCount() int {
	return len(h.clients)
}

// handleMarketSubscribe 处理行情订阅请求
func (c *AppClient) handleMarketSubscribe(payload interface{}) {
	payloadMap, ok := payload.(map[string]interface{})
	if !ok {
		global.GVA_LOG.Error("Invalid market subscribe payload format")
		c.sendResponse("market_subscribe_response", gin.H{
			"success": false,
			"message": "Invalid payload format",
		}, nil)
		return
	}

	symbol, ok := payloadMap["symbol"].(string)
	if !ok || symbol == "" {
		global.GVA_LOG.Error("Missing or invalid symbol in market subscribe")
		c.sendResponse("market_subscribe_response", gin.H{
			"success": false,
			"message": "Missing or invalid symbol",
		}, nil)
		return
	}

	exchange, ok := payloadMap["exchange"].(string)
	if !ok || exchange == "" {
		global.GVA_LOG.Error("Missing or invalid exchange in market subscribe")
		c.sendResponse("market_subscribe_response", gin.H{
			"success": false,
			"message": "Missing or invalid exchange",
		}, nil)
		return
	}

	// 检查是否已经订阅
	c.mu.Lock()
	if c.marketSubscriptions[symbol] {
		c.mu.Unlock()
		global.GVA_LOG.Info("Client already subscribed to market",
			zap.Uint("userID", c.userID),
			zap.String("symbol", symbol))
		return
	}
	c.marketSubscriptions[symbol] = true
	c.mu.Unlock()

	// 添加到Hub的订阅关系中
	c.hub.mu.Lock()
	if c.hub.marketSubscriptions[symbol] == nil {
		c.hub.marketSubscriptions[symbol] = make(map[*AppClient]bool)
	}
	c.hub.marketSubscriptions[symbol][c] = true
	c.hub.mu.Unlock()

	global.GVA_LOG.Info("Client subscribed to market",
		zap.Uint("userID", c.userID),
		zap.String("symbol", symbol),
		zap.String("exchange", exchange))

	// 立即从Redis获取当前行情并推送
	go c.sendCurrentMarketData(symbol)
}

// handleMarketUnsubscribe 处理行情取消订阅请求
func (c *AppClient) handleMarketUnsubscribe(payload interface{}) {
	payloadMap, ok := payload.(map[string]interface{})
	if !ok {
		global.GVA_LOG.Error("Invalid market unsubscribe payload format")
		return
	}

	symbol, ok := payloadMap["symbol"].(string)
	if !ok || symbol == "" {
		global.GVA_LOG.Error("Missing or invalid symbol in market unsubscribe")
		return
	}

	// 检查是否已订阅
	c.mu.Lock()
	if !c.marketSubscriptions[symbol] {
		c.mu.Unlock()
		global.GVA_LOG.Info("Client not subscribed to market",
			zap.Uint("userID", c.userID),
			zap.String("symbol", symbol))
		return
	}
	delete(c.marketSubscriptions, symbol)
	c.mu.Unlock()

	// 从Hub的订阅关系中移除
	c.hub.mu.Lock()
	if clients, exists := c.hub.marketSubscriptions[symbol]; exists {
		delete(clients, c)
		// 如果没有客户端订阅该合约了，删除整个条目
		if len(clients) == 0 {
			delete(c.hub.marketSubscriptions, symbol)
		}
	}
	c.hub.mu.Unlock()

	global.GVA_LOG.Info("Client unsubscribed from market",
		zap.Uint("userID", c.userID),
		zap.String("symbol", symbol))
}

// sendCurrentMarketData 从Redis获取当前行情并发送给客户端
func (c *AppClient) sendCurrentMarketData(symbol string) {
	// 检查Redis是否已初始化
	if global.GVA_REDIS == nil {
		global.GVA_LOG.Warn("Redis not initialized, cannot send current market data",
			zap.String("symbol", symbol))
		return
	}

	ctx := context.Background()
	key := fmt.Sprintf("tick:%s", symbol)

	// 使用GET获取JSON格式的行情数据
	result, err := global.GVA_REDIS.Get(ctx, key).Result()
	if err != nil {
		global.GVA_LOG.Error("Failed to get market data from Redis",
			zap.String("symbol", symbol),
			zap.Error(err))
		return
	}

	if result == "" {
		global.GVA_LOG.Warn("No market data found in Redis", zap.String("symbol", symbol))
		return
	}

	// 解析JSON数据
	var marketData map[string]interface{}
	if err := json.Unmarshal([]byte(result), &marketData); err != nil {
		global.GVA_LOG.Error("Failed to parse market data JSON",
			zap.String("symbol", symbol),
			zap.Error(err))
		return
	}

	// 确保包含symbol字段
	marketData["symbol"] = symbol

	// 发送行情更新消息
	global.GVA_LOG.Info("Sending current market data to client",
		zap.String("symbol", symbol),
		zap.Uint("userID", c.userID),
		zap.Any("marketData", marketData))

	c.sendResponse("market_update", marketData, nil)

	global.GVA_LOG.Info("Sent current market data",
		zap.String("symbol", symbol),
		zap.Uint("userID", c.userID))
}

// cleanupClientMarketSubscriptions 清理客户端的所有行情订阅
func (h *AppHub) cleanupClientMarketSubscriptions(client *AppClient) {
	h.mu.Lock()
	defer h.mu.Unlock()

	// 遍历所有行情订阅，移除该客户端
	for symbol, clients := range h.marketSubscriptions {
		if _, exists := clients[client]; exists {
			delete(clients, client)
			// 如果没有客户端订阅该合约了，删除整个条目
			if len(clients) == 0 {
				delete(h.marketSubscriptions, symbol)
			}
		}
	}
}

// startRedisListener 启动Redis监听器，监听tick_update频道
func (h *AppHub) startRedisListener() {
	// 检查Redis是否可用
	if global.GVA_REDIS == nil {
		global.GVA_LOG.Error("Redis not initialized, cannot start market data listener")
		return
	}

	ctx := context.Background()
	pubsub := global.GVA_REDIS.Subscribe(ctx, "tick_update")
	defer pubsub.Close()

	global.GVA_LOG.Info("Redis market data listener started")

	// 监听消息
	ch := pubsub.Channel()
	for msg := range ch {
		h.handleTickUpdate(msg.Payload)
	}
}

// handleTickUpdate 处理Redis中的tick更新消息
func (h *AppHub) handleTickUpdate(symbol string) {
	// 解析消息格式直接是symbol
	if symbol == "" {
		global.GVA_LOG.Error("Empty symbol in tick update", zap.String("symbol", symbol))
		return
	}

	// 获取该合约的所有订阅客户端
	h.mu.RLock()
	clients, exists := h.marketSubscriptions[symbol]
	if !exists || len(clients) == 0 {
		h.mu.RUnlock()
		return // 没有客户端订阅该合约
	}

	// 复制客户端列表以避免长时间持有锁
	clientList := make([]*AppClient, 0, len(clients))
	for client := range clients {
		clientList = append(clientList, client)
	}
	h.mu.RUnlock()

	// 检查Redis是否可用
	if global.GVA_REDIS == nil {
		global.GVA_LOG.Warn("Redis not available, cannot get updated market data",
			zap.String("symbol", symbol))
		return
	}

	// 从Redis获取最新行情数据
	ctx := context.Background()
	key := fmt.Sprintf("tick:%s", symbol)
	result, err := global.GVA_REDIS.Get(ctx, key).Result()
	if err != nil {
		global.GVA_LOG.Error("Failed to get updated market data from Redis",
			zap.String("symbol", symbol),
			zap.Error(err))
		return
	}

	if result == "" {
		global.GVA_LOG.Warn("No updated market data found in Redis", zap.String("symbol", symbol))
		return
	}

	// 解析JSON数据
	var marketData map[string]interface{}
	if err := json.Unmarshal([]byte(result), &marketData); err != nil {
		global.GVA_LOG.Error("Failed to parse updated market data JSON",
			zap.String("symbol", symbol),
			zap.Error(err))
		return
	}

	// 确保包含symbol字段
	marketData["symbol"] = symbol

	// 向所有订阅的客户端推送更新
	for _, client := range clientList {
		select {
		case <-time.After(100 * time.Millisecond): // 防止阻塞
			global.GVA_LOG.Warn("Client send timeout",
				zap.Uint("userID", client.userID),
				zap.String("symbol", symbol))
		default:
			client.sendResponse("market_update", marketData, nil)
		}
	}
}



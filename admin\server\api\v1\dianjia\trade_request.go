package dianjia

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/dianjia"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type TradeRequestApi struct{}

// CreateTradeRequest 创建交易请求
// @Tags TradeRequest
// @Summary 创建交易请求
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body dianjia.CreateTradeRequestRequest true "创建交易请求"
// @Success 200 {object} response.Response{data=dianjia.TradeRequest,msg=string} "创建成功"
// @Router /traderequests [post]
func (api *TradeRequestApi) CreateTradeRequest(c *gin.Context) {
	var req dianjia.CreateTradeRequestRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID (点价方ID)
	pricerID := utils.GetUserID(c)

	// 创建交易请求
	tradeRequest, err := service.ServiceGroupApp.DianjiaServiceGroup.TradeRequestService.CreateTradeRequest(&req, pricerID)
	if err != nil {
		global.GVA_LOG.Error("创建交易请求失败!", zap.Error(err))
		response.FailWithMessage("创建失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(tradeRequest, "创建成功", c)
}

// GetTradeRequest 获取单个交易请求
// @Tags TradeRequest
// @Summary 获取单个交易请求
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "交易请求ID"
// @Success 200 {object} response.Response{data=dianjia.TradeRequest,msg=string} "获取成功"
// @Router /traderequests/{id} [get]
func (api *TradeRequestApi) GetTradeRequest(c *gin.Context) {
	idParam := c.Param("id")
	id, err := strconv.ParseUint(idParam, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的ID参数", c)
		return
	}

	tradeRequest, err := service.ServiceGroupApp.DianjiaServiceGroup.TradeRequestService.GetTradeRequest(uint(id))
	if err != nil {
		global.GVA_LOG.Error("获取交易请求失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(tradeRequest, "获取成功", c)
}

// CancelTradeRequest 取消交易请求
// @Tags TradeRequest
// @Summary 取消交易请求
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "交易请求ID"
// @Success 200 {object} response.Response{msg=string} "取消成功"
// @Router /traderequests/{id}/cancel [post]
func (api *TradeRequestApi) CancelTradeRequest(c *gin.Context) {
	idParam := c.Param("id")
	id, err := strconv.ParseUint(idParam, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的ID参数", c)
		return
	}

	userID := utils.GetUserID(c)

	err = service.ServiceGroupApp.DianjiaServiceGroup.TradeRequestService.CancelTradeRequest(uint(id), userID)
	if err != nil {
		global.GVA_LOG.Error("取消交易请求失败!", zap.Error(err))
		response.FailWithMessage("取消失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("取消成功", c)
}

// GetTradeRequestsForPricer 获取点价方的交易请求列表
// @Tags TradeRequest
// @Summary 获取点价方的交易请求列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query dianjia.TradeRequestForPricerRequest true "查询参数"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /traderequests/for-pricer [get]
func (api *TradeRequestApi) GetTradeRequestsForPricer(c *gin.Context) {
	var req dianjia.TradeRequestForPricerRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 获取当前用户ID作为点价方ID
	pricerID := utils.GetUserID(c)

	tradeRequests, total, err := service.ServiceGroupApp.DianjiaServiceGroup.TradeRequestService.GetTradeRequestsForPricer(pricerID, &req)
	if err != nil {
		global.GVA_LOG.Error("获取点价方交易请求列表失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     tradeRequests,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}

// GetTradeRequestsForSetter 获取被点价方的交易请求列表
// @Tags TradeRequest
// @Summary 获取被点价方的交易请求列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query dianjia.TradeRequestForSetterRequest true "查询参数"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /traderequests/for-setter [get]
func (api *TradeRequestApi) GetTradeRequestsForSetter(c *gin.Context) {
	var req dianjia.TradeRequestForSetterRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 获取当前用户ID作为被点价方ID
	setterID := utils.GetUserID(c)

	tradeRequests, total, err := service.ServiceGroupApp.DianjiaServiceGroup.TradeRequestService.GetTradeRequestsForSetter(setterID, &req)
	if err != nil {
		global.GVA_LOG.Error("获取被点价方交易请求列表失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     tradeRequests,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}

// ManualFeedback 人工反馈交易结果
// @Tags TradeRequest
// @Summary 人工反馈交易结果
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "交易请求ID"
// @Param data body dianjia.ManualFeedbackRequest true "反馈数据"
// @Success 200 {object} response.Response{msg=string} "操作成功"
// @Router /traderequests/{id}/feedback [post]
func (api *TradeRequestApi) ManualFeedback(c *gin.Context) {
	idParam := c.Param("id")
	id, err := strconv.ParseUint(idParam, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的ID参数", c)
		return
	}

	var req dianjia.ManualFeedbackRequest
	err = c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID (被点价方)
	setterID := utils.GetUserID(c)

	err = service.ServiceGroupApp.DianjiaServiceGroup.TradeRequestService.ManualFeedback(uint(id), setterID, &req)
	if err != nil {
		global.GVA_LOG.Error("人工反馈失败!", zap.Error(err))
		response.FailWithMessage("操作失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("成交成功", c)
}

// RejectTradeRequest 拒绝交易请求 (V4新增)
// @Tags TradeRequest
// @Summary 拒绝交易请求
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "交易请求ID"
// @Param data body dianjia.RejectTradeRequestRequest true "拒绝理由"
// @Success 200 {object} response.Response{msg=string} "拒绝成功"
// @Router /traderequests/{id}/reject [post]
func (api *TradeRequestApi) RejectTradeRequest(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.FailWithMessage("无效的交易请求ID", c)
		return
	}

	var req dianjia.RejectTradeRequestRequest
	err = c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID (被点价方ID)
	setterID := utils.GetUserID(c)

	// 拒绝交易请求
	err = service.ServiceGroupApp.DianjiaServiceGroup.TradeRequestService.RejectTradeRequest(uint(id), setterID, &req)
	if err != nil {
		global.GVA_LOG.Error("拒绝交易请求失败!", zap.Error(err))
		response.FailWithMessage("拒绝失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("拒绝成功", c)
}

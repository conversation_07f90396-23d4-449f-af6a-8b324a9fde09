package dianjia

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
)

// TradeRequestType 交易请求类型枚举
type TradeRequestType string

const (
	TradeRequestTypePointPrice TradeRequestType = "PointPrice" // 点价操作
	TradeRequestTypeBasisWash  TradeRequestType = "BasisWash"  // 洗基差操作
)

// TradeRequestStatus 交易请求状态枚举 (V4 - 简化状态机)
type TradeRequestStatus string

const (
	TradeRequestStatusExecuting TradeRequestStatus = "Executing" // 执行中
	TradeRequestStatusCompleted TradeRequestStatus = "Completed" // 已完成
	TradeRequestStatusRejected  TradeRequestStatus = "Rejected"  // 已拒绝
	TradeRequestStatusCancelled TradeRequestStatus = "Cancelled" // 已取消
	TradeRequestStatusExpired   TradeRequestStatus = "Expired"   // 已过期
)

// ExecutionMode 执行模式枚举
type ExecutionMode string

const (
	ExecutionModeAutomatic ExecutionMode = "AUTOMATIC" // 自动执行
	ExecutionModeManual    ExecutionMode = "MANUAL"    // 人工执行
	ExecutionModeSimulated ExecutionMode = "SIMULATED" // 模拟执行
)

// TradeRequest 交易请求表 (V4)
type TradeRequest struct {
	global.GVA_MODEL

	// 用户信息
	PricerID uint           `json:"pricerID" gorm:"column:pricer_id;not null;comment:点价方ID(发起者)"`
	Pricer   system.SysUser `json:"pricer" gorm:"foreignKey:PricerID"`

	SetterID uint           `json:"setterID" gorm:"column:setter_id;not null;comment:被点价方ID(对手方)"`
	Setter   system.SysUser `json:"setter" gorm:"foreignKey:SetterID"`

	// 合约信息
	InstrumentRefID uint       `json:"instrumentRefID" gorm:"column:instrument_ref_id;not null;comment:关联的期货合约ID"`
	Instrument      Instrument `json:"instrument" gorm:"foreignKey:InstrumentRefID;references:ID"`

	// 交易请求信息
	RequestType       TradeRequestType   `json:"requestType" gorm:"column:request_type;type:varchar(50);not null;comment:请求类型(PointPrice/BasisWash)"`
	RequestedQuantity int                `json:"requestedQuantity" gorm:"column:requested_quantity;not null;comment:用户请求的总数量"`
	RequestedPrice    float64            `json:"requestedPrice" gorm:"column:requested_price;type:decimal(10,2);comment:用户请求的价格(点价时)"`
	ExecutedQuantity  int                `json:"executedQuantity" gorm:"column:executed_quantity;not null;default:0;comment:已执行的数量"`
	ExecutedPrice     float64            `json:"executedPrice" gorm:"column:executed_price;type:decimal(10,2);default:0;comment:加权平均执行价格"`
	Status            TradeRequestStatus `json:"status" gorm:"column:status;type:varchar(50);not null;comment:请求状态"`

	// V4 新增字段
	ExecutionMode   ExecutionMode `json:"executionMode" gorm:"column:execution_mode;type:varchar(20);not null;comment:执行模式(AUTOMATIC/MANUAL/SIMULATED)"`
	RejectionReason string        `json:"rejectionReason" gorm:"column:rejection_reason;type:text;comment:拒绝理由"`
	ExpiresAt       *time.Time    `json:"expiresAt" gorm:"column:expires_at;not null;comment:请求的过期时间"`
	Notes           string        `json:"notes" gorm:"column:notes;type:text;comment:备注信息"`

	// 关联表
	ExecutionDetails []ExecutionDetail `json:"executionDetails" gorm:"foreignKey:TradeRequestID"`
}

// CreateTradeRequestRequest 创建交易请求的请求结构 (V4)
type CreateTradeRequestRequest struct {
	SetterID          uint             `json:"setterID" binding:"required"`
	InstrumentRefID   uint             `json:"instrumentRefID" binding:"required"`
	RequestType       TradeRequestType `json:"requestType" binding:"required,oneof=PointPrice BasisWash"`
	RequestedQuantity int              `json:"requestedQuantity" binding:"required,min=1"`
	RequestedPrice    float64          `json:"requestedPrice"` // 点价时必填
	ExecutionMode     ExecutionMode    `json:"executionMode" binding:"required,oneof=AUTOMATIC MANUAL SIMULATED"`
	ExpiresAt         time.Time        `json:"expiresAt" binding:"required"`
}

// TradeRequestForPricerRequest 点价方交易请求列表查询
type TradeRequestForPricerRequest struct {
	Page            int                `json:"page" form:"page"`
	PageSize        int                `json:"pageSize" form:"pageSize"`
	InstrumentRefID uint               `json:"instrumentRefID" form:"instrumentRefID"`
	RequestType     TradeRequestType   `json:"requestType" form:"requestType"`
	Status          TradeRequestStatus `json:"status" form:"status"`
	StartDate       string             `json:"startDate" form:"startDate"`
	EndDate         string             `json:"endDate" form:"endDate"`
}

// TradeRequestForSetterRequest 被点价方交易请求列表查询
type TradeRequestForSetterRequest struct {
	Page            int                `json:"page" form:"page"`
	PageSize        int                `json:"pageSize" form:"pageSize"`
	InstrumentRefID uint               `json:"instrumentRefID" form:"instrumentRefID"`
	RequestType     TradeRequestType   `json:"requestType" form:"requestType"`
	Status          TradeRequestStatus `json:"status" form:"status"`
	StartDate       string             `json:"startDate" form:"startDate"`
	EndDate         string             `json:"endDate" form:"endDate"`
}

// ManualFeedbackRequest 人工反馈请求结构 (V4)
type ManualFeedbackRequest struct {
	Quantity int     `json:"quantity" binding:"required,min=1"` // 成交数量
	Price    float64 `json:"price" binding:"required"`          // 成交价格
	Notes    string  `json:"notes"`                             // 备注
}

// RejectTradeRequestRequest 拒绝交易请求的请求结构 (V4)
type RejectTradeRequestRequest struct {
	Reason string `json:"reason" binding:"required"` // 拒绝理由
}

func (TradeRequest) TableName() string {
	return "dj_trade_requests"
}

import { httpGet, httpPost, httpPut, httpDelete } from '@/http/http'
import type {
  IInstrument,
  IInstrumentRequest,
  IInstrumentListRequest,
  IInstrumentListResponse,
  IInstrumentSelectRequest,
  IInstrumentSelectItem,
  IInstrumentsByExchange
} from '@/types'

// 为了保持兼容性，重新导出类型
export type {
  IInstrument,
  IInstrumentRequest,
  IInstrumentListRequest,
  IInstrumentListResponse,
  IInstrumentSelectRequest,
  IInstrumentSelectItem,
  IInstrumentsByExchange,
}

/**
 * 分页获取期货合约列表
 */
export function getInstrumentList(params: IInstrumentListRequest) {
  return httpGet<IInstrumentListResponse>('/instrument/getInstrumentList', params)
}

/**
 * 根据ID获取期货合约详情
 */
export function getInstrumentById(id: number) {
  return httpGet<IInstrument>('/instrument/findInstrument', { ID: id })
}

/**
 * 创建期货合约
 */
export function createInstrument(data: IInstrumentRequest) {
  return httpPost<IInstrument>('/instrument/createInstrument', data)
}

/**
 * 更新期货合约
 */
export function updateInstrument(data: IInstrumentRequest) {
  return httpPut<boolean>('/instrument/updateInstrument', data)
}

/**
 * 删除期货合约
 */
export function deleteInstrument(id: number) {
  return httpDelete<boolean>('/instrument/deleteInstrument', { ID: id })
}

/**
 * 批量删除期货合约
 */
export function deleteInstrumentByIds(ids: number[]) {
  return httpDelete<boolean>('/instrument/deleteInstrumentByIds', { IDs: ids })
}

/**
 * 获取期货合约选择器列表
 */
export function getInstrumentSelectList(params?: IInstrumentSelectRequest) {
  return httpGet<IInstrumentSelectItem[]>('/instrument/getInstrumentSelectList', params)
}

/**
 * 获取按交易所分组的期货合约列表
 */
export function getInstrumentsByExchange() {
  return httpGet<IInstrumentsByExchange>('/instrument/getInstrumentsByExchange')
}
# WebSocket App 使用说明

## 认证功能概述

WebSocket App端支持JWT认证功能，用户登录后可通过JWT Token进行WebSocket连接的身份认证，确保连接安全性。

### 功能特性

- ✅ **JWT Token验证**：集成系统统一的JWT验证逻辑
- ✅ **用户身份记录**：验证成功后记录客户端的用户信息
- ✅ **自动认证**：连接建立后自动进行身份认证
- ✅ **错误处理**：提供完整的错误信息和状态反馈
- ✅ **实时状态**：前端可实时获取认证状态

### 使用方法

#### 1. 自动认证
WebSocket连接建立后，系统会自动检查用户登录状态并发起认证，无需手动操作。

#### 2. 手动认证
如需手动认证，可调用：
```typescript
const socketStore = useSocketStore()
socketStore.authenticate(userToken)
```

#### 3. 消息格式

##### 认证请求
```json
{
  "event": "auth",
  "payload": { "token": "jwt_token_string" }
}
```

##### 认证成功响应
```json
{
  "event": "auth_response",
  "payload": {
    "success": true,
    "user": { "id": 1, "username": "admin", "nickname": "管理员" }
  }
}
```

##### 认证失败响应
```json
{
  "event": "auth_response",
  "payload": {
    "success": false,
    "message": "Token验证失败: token已过期"
  }
}
```

#### 4. 错误处理

常见错误类型及处理方式：

| 错误类型 | 错误信息 | 处理方式 |
|---------|---------|---------|
| Token为空 | "Token不能为空" | 检查用户登录状态 |
| Token过期 | "Token验证失败: token已过期" | 重新登录获取新token |
| Token无效 | "Token验证失败: 无效签名" | 检查token来源和有效性 |
| 格式错误 | "认证数据格式错误" | 检查消息格式 |

### 状态管理

#### 认证状态
- `isConnected`：WebSocket连接状态
- `isAuthenticated`：用户认证状态

#### 自动处理
- 连接建立时自动认证
- 重连后自动重新认证
- 认证失败时显示错误提示

### 注意事项

1. **自动认证**：系统会在连接建立时自动进行认证
2. **Token有效性**：确保用户已登录且token有效
3. **重连处理**：重连后会自动重新认证
4. **错误提示**：认证失败时会显示具体错误信息
5. **状态同步**：认证状态与用户登录状态保持同步

### 更新记录

- **2025-07-26**：实现JWT认证功能，支持自动认证和错误处理
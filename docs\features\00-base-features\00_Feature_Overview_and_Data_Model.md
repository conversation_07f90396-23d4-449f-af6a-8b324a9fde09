# 功能特性文档撰写规范 (Feature Documentation Standard)

## 1. 综述

本系统实现了统一的用户认证与资料管理，支持手机号/验证码、微信一键登录、用户名/密码/图形验证码三种主流方式，所有方式均自动注册并指向同一用户主体（单一用户记录）。系统以`sys_users`表的ID/UUID为核心标识，所有登录凭证（手机号、微信OpenID/UnionID、用户名等）均作为“钥匙”指向同一用户，避免数据冗余和身份混淆。用户可在登录后完善个人及企业信息，企业资料（如企业名称、组织编码、地址）已纳入用户模型，满足企业级应用需求。该方案支持用户凭证绑定与合并，确保多渠道登录体验一致，便于后台统一管理和权限控制。

### 1.1. `00_Feature_Overview_and_Data_Model.md`

这是对基础模板改造的**入口文件**，必须包含以下内容：

- **功能模块简介 (Summary)**:
  - 实现统一用户认证，支持手机号、微信、用户名密码多种登录方式，自动注册，单一用户主体，凭证绑定与合并。
  - 用户模型扩展企业信息，满足企业客户需求。
  - 以ID/UUID为唯一主键，所有业务数据与此关联，便于后台统一管理。
- **数据定义 (Data Definition)**:
  - 详尽列出与此功能相关的所有数据库表结构。
  - 清楚地标明每个字段的名称、类型、约束和业务描述。
  - 如果存在复杂的状态机（如订单状态、交易状态），应明确画出或列出状态流转的路径。

### 1.2. `01_User_Authentication_and_Profile.md`

- **用户通过手机号码登录（没有注册的自动注册），涉及到验证码的发送，验证等等。**
- **对用户系统字段的扩充， 现在应用针对的是企业客户，需要有企业的资料，名称，组织编码id， 地址， 等。**

---


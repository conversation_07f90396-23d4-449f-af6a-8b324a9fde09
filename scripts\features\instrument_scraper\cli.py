import typer
import json
from typing_extensions import Annotated

from . import synchronizer

app = typer.Typer(name="instrument", help="Commands for managing instrument data.")

@app.command("sync")
def sync_instruments(
    dry_run: Annotated[bool, typer.Option(help="Fetch data and print results without saving to the database.")] = False,
    output_file: Annotated[str, typer.Option(help="Path to save the output as a JSON file.")] = None,
):
    """
    Fetches, transforms, and synchronizes instrument data with the database.
    """
    typer.echo("Starting instrument synchronization...")

    params = {"types": "futures"}
    instrument_data = synchronizer.fetch_instruments(params)

    if not instrument_data:
        typer.echo(typer.style("Failed to fetch any data from the API. Exiting.", fg=typer.colors.RED))
        raise typer.Exit(code=1)

    transformed_instruments = synchronizer.transform_data(instrument_data)
    typer.echo(f"Successfully fetched and transformed {len(transformed_instruments)} instruments.")

    if output_file:
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(transformed_instruments, f, indent=2, ensure_ascii=False)
            typer.echo(typer.style(f"Successfully saved data to {output_file}", fg=typer.colors.GREEN))
            return
        except IOError as e:
            typer.echo(typer.style(f"Error writing to file {output_file}: {e}", fg=typer.colors.RED))
            raise typer.Exit(code=1)

    if dry_run:
        typer.echo(typer.style("\n[Dry Run] Displaying first 5 instruments:", fg=typer.colors.YELLOW))
        for instrument in transformed_instruments[:5]:
            print(json.dumps(instrument, indent=2, ensure_ascii=False))
        typer.echo(typer.style("\n[Dry Run] No changes were made to the database.", fg=typer.colors.YELLOW))
        return

    try:
        added, updated, deleted = synchronizer.sync_to_database(transformed_instruments)

        typer.echo(typer.style("\nSynchronization Report:", bold=True))
        typer.echo(f"- New instruments added: {typer.style(str(added), fg=typer.colors.GREEN)}")
        typer.echo(f"- Existing instruments updated: {typer.style(str(updated), fg=typer.colors.CYAN)}")
        typer.echo(f"- Stale instruments deleted: {typer.style(str(deleted), fg=typer.colors.RED)}")
        typer.echo(typer.style("\nDatabase synchronization complete!", fg=typer.colors.GREEN))

    except Exception as e:
        typer.echo(typer.style(f"An error occurred during synchronization: {e}", fg=typer.colors.RED))
        raise typer.Exit(code=1)

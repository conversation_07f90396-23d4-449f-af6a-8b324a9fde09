"""
认证模块数据模型定义

定义了认证相关的所有数据结构，包括：
- 登录凭据
- Token数据
- 认证响应
- 登录结果
- 错误码定义
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional, Dict, Any
from enum import Enum


@dataclass
class LoginCredentials:
    """登录凭据数据类"""
    login_type: str  # "password" or "phone"
    username: Optional[str] = None
    password: Optional[str] = None
    phone: Optional[str] = None
    sms_code: Optional[str] = None
    captcha: Optional[str] = None  # 图片验证码
    captcha_id: Optional[str] = None  # 验证码ID
    remember_me: bool = False
    auto_login: bool = False
    
    def __post_init__(self):
        """数据验证"""
        if self.login_type not in ["password", "phone"]:
            raise ValueError("login_type must be 'password' or 'phone'")

    def validate(self) -> Optional[str]:
        """验证登录凭据

        Returns:
            Optional[str]: 错误消息，None表示验证通过
        """
        if self.login_type == "password":
            if not self.username or not self.username.strip():
                return "请输入用户名"
            if not self.password:
                return "请输入密码"
            if not self.captcha or not self.captcha.strip():
                return "请输入验证码"
        elif self.login_type == "phone":
            if not self.phone or not self.phone.strip():
                return "请输入手机号"
            if not self.sms_code or not self.sms_code.strip():
                return "请输入验证码"

        return None


@dataclass
class TokenData:
    """Token数据类"""
    access_token: str
    refresh_token: str
    expires_at: datetime
    user_info: Dict[str, Any]
    created_at: datetime
    
    def is_expired(self, buffer_minutes: int = 5) -> bool:
        """检查Token是否过期（含缓冲时间）"""
        from datetime import timedelta
        buffer_time = timedelta(minutes=buffer_minutes)
        return datetime.now() >= (self.expires_at - buffer_time)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "access_token": self.access_token,
            "refresh_token": self.refresh_token,
            "expires_at": self.expires_at.isoformat(),
            "user_info": self.user_info,
            "created_at": self.created_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TokenData':
        """从字典创建TokenData实例"""
        return cls(
            access_token=data["access_token"],
            refresh_token=data["refresh_token"],
            expires_at=datetime.fromisoformat(data["expires_at"]),
            user_info=data["user_info"],
            created_at=datetime.fromisoformat(data["created_at"])
        )


@dataclass
class AuthResponse:
    """认证响应数据类"""
    success: bool
    access_token: Optional[str] = None
    refresh_token: Optional[str] = None
    expires_in: Optional[int] = None  # 秒数
    user_info: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    error_code: Optional[str] = None
    
    def to_token_data(self) -> Optional[TokenData]:
        """转换为TokenData（仅在成功时）"""
        if not self.success or not self.access_token:
            return None

        from datetime import timedelta

        # 处理过大的expires_in值，防止日期溢出
        expires_in_seconds = self.expires_in or 3600

        # 如果expires_in过大（可能是毫秒时间戳），进行转换
        if expires_in_seconds > 86400 * 365 * 10:  # 超过10年的秒数
            # 可能是毫秒时间戳，转换为秒
            if expires_in_seconds > 1000000000000:  # 大于2001年的毫秒时间戳
                # 这是一个绝对时间戳（毫秒），转换为相对秒数
                expires_timestamp = expires_in_seconds / 1000  # 转换为秒
                current_timestamp = datetime.now().timestamp()
                expires_in_seconds = max(3600, int(expires_timestamp - current_timestamp))
            else:
                # 可能是秒时间戳，转换为相对秒数
                current_timestamp = datetime.now().timestamp()
                expires_in_seconds = max(3600, int(expires_in_seconds - current_timestamp))

        # 限制最大过期时间为30天
        expires_in_seconds = min(expires_in_seconds, 86400 * 30)
        # 确保最小过期时间为1小时
        expires_in_seconds = max(expires_in_seconds, 3600)

        try:
            expires_at = datetime.now() + timedelta(seconds=expires_in_seconds)
        except (OverflowError, ValueError) as e:
            # 如果仍然溢出，使用默认的1小时过期时间
            expires_at = datetime.now() + timedelta(hours=1)

        return TokenData(
            access_token=self.access_token,
            refresh_token=self.refresh_token or "",
            expires_at=expires_at,
            user_info=self.user_info or {},
            created_at=datetime.now()
        )


@dataclass
class LoginResult:
    """登录结果数据类"""
    success: bool
    message: str
    user_info: Optional[Dict[str, Any]] = None
    token_data: Optional[TokenData] = None
    error_code: Optional[str] = None
    
    @classmethod
    def success_result(cls, message: str, user_info: Dict[str, Any], 
                      token_data: TokenData) -> 'LoginResult':
        """创建成功结果"""
        return cls(
            success=True,
            message=message,
            user_info=user_info,
            token_data=token_data
        )
    
    @classmethod
    def failure_result(cls, message: str, error_code: Optional[str] = None) -> 'LoginResult':
        """创建失败结果"""
        return cls(
            success=False,
            message=message,
            error_code=error_code
        )


class LoginErrorCode(Enum):
    """登录错误码枚举"""
    # 认证错误
    INVALID_CREDENTIALS = "INVALID_CREDENTIALS"      # 用户名密码错误
    ACCOUNT_LOCKED = "ACCOUNT_LOCKED"                # 账户被锁定
    ACCOUNT_DISABLED = "ACCOUNT_DISABLED"            # 账户被禁用
    
    # Token错误
    TOKEN_EXPIRED = "TOKEN_EXPIRED"                  # Token过期
    TOKEN_INVALID = "TOKEN_INVALID"                  # Token无效
    TOKEN_REFRESH_FAILED = "TOKEN_REFRESH_FAILED"    # Token刷新失败
    
    # 短信验证码错误
    SMS_CODE_INVALID = "SMS_CODE_INVALID"            # 验证码错误
    SMS_CODE_EXPIRED = "SMS_CODE_EXPIRED"            # 验证码过期
    SMS_SEND_FAILED = "SMS_SEND_FAILED"              # 短信发送失败
    SMS_SEND_TOO_FREQUENT = "SMS_SEND_TOO_FREQUENT"  # 发送过于频繁
    
    # 手机号错误
    PHONE_NOT_REGISTERED = "PHONE_NOT_REGISTERED"    # 手机号未注册
    PHONE_FORMAT_INVALID = "PHONE_FORMAT_INVALID"    # 手机号格式错误
    
    # 网络错误
    NETWORK_ERROR = "NETWORK_ERROR"                  # 网络错误
    SERVER_ERROR = "SERVER_ERROR"                    # 服务器错误
    TIMEOUT_ERROR = "TIMEOUT_ERROR"                  # 超时错误
    
    # 参数错误
    INVALID_PARAMETERS = "INVALID_PARAMETERS"        # 参数错误
    MISSING_PARAMETERS = "MISSING_PARAMETERS"        # 缺少参数
    
    # 其他错误
    UNKNOWN_ERROR = "UNKNOWN_ERROR"                  # 未知错误
    
    @classmethod
    def get_message(cls, error_code: str) -> str:
        """获取错误码对应的中文消息"""
        messages = {
            cls.INVALID_CREDENTIALS.value: "用户名或密码错误",
            cls.ACCOUNT_LOCKED.value: "账户已被锁定",
            cls.ACCOUNT_DISABLED.value: "账户已被禁用",
            cls.TOKEN_EXPIRED.value: "登录已过期，请重新登录",
            cls.TOKEN_INVALID.value: "登录信息无效",
            cls.TOKEN_REFRESH_FAILED.value: "登录信息刷新失败",
            cls.SMS_CODE_INVALID.value: "验证码错误",
            cls.SMS_CODE_EXPIRED.value: "验证码已过期",
            cls.SMS_SEND_FAILED.value: "验证码发送失败",
            cls.SMS_SEND_TOO_FREQUENT.value: "验证码发送过于频繁，请稍后再试",
            cls.PHONE_NOT_REGISTERED.value: "手机号未注册",
            cls.PHONE_FORMAT_INVALID.value: "手机号格式不正确",
            cls.NETWORK_ERROR.value: "网络连接失败",
            cls.SERVER_ERROR.value: "服务器错误",
            cls.TIMEOUT_ERROR.value: "请求超时",
            cls.INVALID_PARAMETERS.value: "参数错误",
            cls.MISSING_PARAMETERS.value: "缺少必要参数",
            cls.UNKNOWN_ERROR.value: "未知错误"
        }
        return messages.get(error_code, "未知错误")


@dataclass
class UserInfo:
    """用户信息数据类 - 与Go SysUser模型对齐"""
    # 必填字段（无默认值）
    ID: int
    CreatedAt: str
    UpdatedAt: str
    uuid: str
    userName: str
    nickName: str
    headerImg: str
    authorityId: int
    enable: int
    phone: str

    # 可选字段（有默认值）
    DeletedAt: Optional[str] = None
    email: Optional[str] = None
    wechatOpenid: Optional[str] = None
    wechatUnionId: Optional[str] = None
    companyName: Optional[str] = None
    companyOrgId: Optional[str] = None
    companyAddress: Optional[str] = None
    authority: Optional[Dict[str, Any]] = None
    authorities: Optional[list] = None
    originSetting: Optional[Dict[str, Any]] = None

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserInfo':
        """从字典创建UserInfo实例"""
        return cls(
            ID=data.get("ID", 0),
            CreatedAt=data.get("CreatedAt", ""),
            UpdatedAt=data.get("UpdatedAt", ""),
            DeletedAt=data.get("DeletedAt"),
            uuid=data.get("uuid", ""),
            userName=data.get("userName", ""),
            nickName=data.get("nickName", ""),
            headerImg=data.get("headerImg", ""),
            authorityId=data.get("authorityId", 0),
            enable=data.get("enable", 1),
            phone=data.get("phone", ""),
            email=data.get("email"),
            wechatOpenid=data.get("wechatOpenid"),
            wechatUnionId=data.get("wechatUnionId"),
            companyName=data.get("companyName"),
            companyOrgId=data.get("companyOrgId"),
            companyAddress=data.get("companyAddress"),
            authority=data.get("authority"),
            authorities=data.get("authorities", []),
            originSetting=data.get("originSetting")
        )

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            "ID": self.ID,
            "CreatedAt": self.CreatedAt,
            "UpdatedAt": self.UpdatedAt,
            "uuid": self.uuid,
            "userName": self.userName,
            "nickName": self.nickName,
            "headerImg": self.headerImg,
            "authorityId": self.authorityId,
            "enable": self.enable,
            "phone": self.phone,
        }

        # 添加可选字段（非None时）
        if self.DeletedAt is not None:
            result["DeletedAt"] = self.DeletedAt
        if self.email is not None:
            result["email"] = self.email
        if self.wechatOpenid is not None:
            result["wechatOpenid"] = self.wechatOpenid
        if self.wechatUnionId is not None:
            result["wechatUnionId"] = self.wechatUnionId
        if self.companyName is not None:
            result["companyName"] = self.companyName
        if self.companyOrgId is not None:
            result["companyOrgId"] = self.companyOrgId
        if self.companyAddress is not None:
            result["companyAddress"] = self.companyAddress
        if self.authority is not None:
            result["authority"] = self.authority
        if self.authorities is not None:
            result["authorities"] = self.authorities
        if self.originSetting is not None:
            result["originSetting"] = self.originSetting

        return result

    def get_account_id(self) -> str:
        """获取账户ID，优先使用userName，备选uuid"""
        return self.userName or self.uuid or str(self.ID)

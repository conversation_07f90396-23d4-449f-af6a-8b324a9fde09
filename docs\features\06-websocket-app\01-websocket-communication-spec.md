# 功能规格文档：App端 WebSocket 实时通信

> **版本**: 1.1.0
> **负责人**: Gemini
> **状态**: 已发布

---

## 1. 概述

本规格定义了 **移动端 App (uni-app)** 与业务服务器之间基于 WebSocket 的全双工实时通信协议。该协议旨在满足行情推送、订单状态变更通知、交易审核结果下发等高实时性要求的功能，并确保通信的稳定、安全与高效。

### 1.1. 适用范围 (Scope)

本协议适用于需要从服务端 **订阅** 并接收实时数据的场景，例如行情推送、交易状态变更通知等。它采用基于频道的发布/订阅模型。

**注意**: 本协议不适用于 **下单端 (Trading Client)** 与服务端的通信。下单端的通信协议请参阅 **[功能文档 08 - 下单端与服务端WebSocket通信](../08-order-client-websocket-spec/01_Feature_Overview_and_Data_Model.md)**。


### 1.1. 核心流程

1.  **建立连接**: 客户端启动后，立即与服务器建立一个持久的 WebSocket 连接。
2.  **心跳维持**: 连接建立后，客户端与服务器通过 `ping`/`pong` 消息维持长连接。
3.  **身份认证**: 用户登录成功后，客户端通过 WebSocket 发送 `auth` 事件，将当前连接与用户身份绑定。
4.  **频道订阅**: 客户端根据业务需求（如进入某个交易页面），发送 `subscribe` 事件订阅特定数据频道。
5.  **消息接收**: 服务器根据用户的认证状态和订阅的频道，向客户端推送实时业务消息。
6.  **断线重连**: 客户端具备完善的断线重连和心跳超时检测机制。

## 2. 连接管理

- **协议**: 生产环境必须使用 `wss://` (WebSocket Secure) 协议。
- **生命周期**: 
  - **未认证状态**: App启动后建立连接，此时只能收发 `ping`/`pong` 消息。
  - **已认证状态**: 客户端发送 `auth` 事件并验证通过后，连接升级为已认证状态，可以进行业务数据交互。

## 3. 消息协议

所有在客户端与服务器之间传输的消息都应遵循统一的 JSON 格式。

### 3.1. 消息信封 (Envelope)

| 字段 | 类型 | 是否必须 | 描述 |
| :--- | :--- | :--- | :--- |
| `event` | `string` | 是 | 消息的事件类型，用于路由和处理。 |
| `payload` | `object` | 否 | 消息的业务数据。结构随 `event` 类型变化。 |
| `seq` | `number` | 否 | 消息序列号，由发送方生成，用于请求-响应模式的匹配。 |
| `timestamp` | `number` | 是 | 消息发送时的Unix毫秒时间戳。 |

**示例:**
```json
{
  "event": "subscribe",
  "payload": {
    "channel": "contract:HT20240723-001"
  },
  "seq": 1689929954001,
  "timestamp": 1689929954123
}
```

### 3.2. 客户端 -> 服务器 (Upstream Events)

| Event | Payload | 描述 |
| :--- | :--- | :--- |
| `ping` | `{ "timestamp": number }` | **心跳请求**。客户端每25秒发送，`payload`中需携带当前时间戳用于RTT计算。 |
| `auth` | `{ "token": "jwt-token-string" }` | **身份认证**。用户登录后，客户端发送此事件以绑定用户身份。 |
| `subscribe` | `{ "channel": "string" }` | **订阅频道**。客户端请求订阅一个数据频道。 |
| `unsubscribe` | `{ "channel": "string" }` | **取消订阅**。客户端请求取消订阅一个数据频道。 |

### 3.3. 服务器 -> 客户端 (Downstream Events)

| Event | Payload | 描述 |
| :--- | :--- | :--- |
| `pong` | `{ "timestamp": number }` | **心跳响应**。服务器对`ping`的响应，需原样返回`ping`中的时间戳。 |
| `auth_response` | `{ "success": boolean, "message": string }` | **认证结果**。服务器对客户端 `auth` 事件的响应。 |
| `notification` | `{ "type": "success" \| "error", "title": string, "content": string }` | **通用通知**。如下发交易成功、失败等消息。 |
| `market_data` | `{ "channel": string, "data": object }` | **行情数据**。服务器推送的实时行情数据，`data` 结构见具体业务定义。 |
| `trade_update` | `ITrade` | **交易更新**。当一笔交易的状态发生变化时（如被审核），服务器推送此消息。 |
| `error` | `{ "code": number, "message": string }` | **错误消息**。服务器在处理异常时下发的消息。 |

## 4. 核心交互流程

### 4.1. 认证与订阅流程

```mermaid
sequenceDiagram
    participant Client
    participant Server

    Client->>Server: 建立 WebSocket 连接
    Server-->>Client: 连接成功

    loop 心跳循环 (每25s)
        Client->>Server: event: "ping"
        Server-->>Client: event: "pong"
    end

    Note over Client: 用户通过HTTP API登录成功，获取Token

    Client->>Server: event: "auth", payload: { token: "..." }
    Server-->>Client: event: "auth_response", payload: { success: true }

    Note over Client: 进入合同详情页
    Client->>Server: event: "subscribe", payload: { channel: "contract:HT20240723-001" }

    loop 实时数据推送
        Server-->>Client: event: "market_data", payload: { ... }
        Server-->>Client: event: "trade_update", payload: { ... }
    end
```

### 4.2. 频道定义

频道 (Channel) 是数据隔离和分发的单位，格式为 `type:identifier`。

- **合同频道**: `contract:<contractId>`
  - **订阅**: 客户端进入特定合同的交易页面时订阅。
  - **推送内容**: 该合同关联的实时行情 (`market_data`)、该合同下所有交易的状态更新 (`trade_update`)。
- **用户私有频道**: `user:<userId>`
  - **订阅**: 用户认证成功后，服务器自动为其订阅。
  - **推送内容**: 通用通知 (`notification`)、全局性的账户信息变更等。

## 5. 稳定与安全

- **心跳机制**: 客户端每 **25秒** 发送一次 `ping`，若在 **10秒** 内未收到 `pong` 响应，则认为连接丢失，应立即尝试重连。
- **断线重连**: 客户端必须实现指数退避的重连策略（如首次1s，后续2s, 4s, 8s...），避免在服务器故障时发起DDoS攻击。
- **Token处理**: `auth` 事件中使用的 `token` 必须是短期有效的，并通过安全的HTTPS接口获取。

## 6. 开发实现与注意事项

### 6.1. 增强型心跳机制 (RTT计算)

为了监控网络连接质量，心跳机制集成了往返时间（Round-Trip Time, RTT）的计算功能。

- **客户端实现**: 
  1. 在发送 `ping` 事件时，必须在 `payload` 中包含当前客户端的Unix毫秒时间戳，如: `payload: { timestamp: Date.now() }`。
  2. 在接收到 `pong` 事件后，用当前时间减去 `pong` 的 `payload` 中返回的时间戳，即可得到RTT值。
  3. RTT值可以用于日志记录、连接质量监控或UI展示。

- **服务端实现**:
  1. 在收到 `ping` 事件后，服务器**必须**将收到的 `payload` 原封不动地返回给客户端的 `pong` 事件中。
  2. 服务器自身不处理 `payload` 的内容，仅作为透传载体。

### 6.2. 订阅管理

- 客户端应在进入需要实时数据的页面（如交易页）时发送 `subscribe`，在离开页面时发送 `unsubscribe`，以减少不必要的流量和服务器负载。
- 服务器应在客户端断开连接时，自动清理其所有的频道订阅信息。

### 6.3. 错误处理

- 客户端应能处理 `error` 事件，并根据错误码向用户显示友好的提示信息。
- 服务器在处理任何可能失败的操作时（如认证、订阅），都应通过 `error` 事件或在对应的响应事件中明确告知客户端失败原因。


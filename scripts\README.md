# NewDianJia 脚本库开发指南

## 1. 项目概览 (Project Overview)

本代码库是 NewDianJia 项目的专用脚本集合，旨在自动化、简化和统一各类维护、部署、数据处理和监控任务。所有脚本都应在此集中管理，以确保一致性和可追溯性。

**核心原则:**
- **自动化:** 减少手动操作，提高效率和准确性。
- **标准化:** 所有脚本遵循统一的结构、风格和执行方式。
- **安全性:** 避免硬编码敏感信息，对高风险操作进行充分验证。
- **可维护性:** 代码清晰、文档齐全、易于理解和扩展。

---

## 2. 快速开始 (Quick Start)

本库统一使用 `uv` 进行 Python 环境管理和脚本执行，以实现快速、一致的开发体验。

**环境设置:**

1.  **创建虚拟环境:**
    ```bash
    uv venv
    ```
    这将在项目根目录下创建一个名为 `.venv` 的虚拟环境。

2.  **安装依赖:**
    ```bash
    uv pip install -r requirements.txt
    # 或者从 pyproject.toml 安装
    uv pip install -e .
    ```

**运行脚本:**

所有脚本都应通过主入口 `main.py` 来调用，以保证参数处理和环境加载的一致性。

```bash
# 查看所有可用脚本命令
uv run python main.py --help

# 运行特定脚本 (示例)
uv run python main.py data-sync --source db1 --target db2
```

---

## 3. 功能组织架构 (Architecture)

为了保持代码的清晰和模块化，本库采用按功能划分的目录结构。

```
scripts/
├── .venv/              # uv 虚拟环境
├── .python-version     # Python 版本定义
├── @README.md          # 开发指南 (本文档)
├── README.md           # 基础说明 (原始文件)
├── main.py             # **脚本主入口**
├── pyproject.toml      # 项目配置与依赖管理
├── requirements.txt    # 依赖文件 (可选，建议使用 pyproject.toml)
│
├── modules/            # **核心共享模块**
│   ├── __init__.py
│   ├── logging_config.py # 日志配置
│   └── utils/            # 通用工具函数
│       ├── __init__.py
│       └── file_handler.py
│
└── features/           # **按功能组织的具体脚本**
    ├── __init__.py
    ├── data_migration/
    │   ├── __init__.py
    │   └── migrate_users.py
    └── system_monitor/
        ├── __init__.py
        └── check_disk_space.py
```

**核心目录说明:**

-   **`main.py`**: **唯一的脚本执行入口**。使用 `argparse` 或 `typer` 等库来管理子命令，每个子命令对应一个功能脚本。这样做可以统一参数解析、日志初始化和错误处理。
-   **`modules/`**: 存放跨功能共享的代码。例如，数据库连接、API客户端、日志配置、通用工具函数等。
-   **`features/`**: 存放具体的业务脚本。每个子目录代表一个独立的功能模块（如 `data_migration`），其中包含实现该功能的 Python 脚本。

---

## 4. 开发规范 (Development Standards)

所有贡献者都必须遵循以下规范。

### 4.1 编码风格 (Coding Style)

-   遵循 **PEP 8** 规范。
-   使用 **Ruff** 进行代码格式化和 Lint 检查，确保风格一致。
-   所有代码文件必须使用 **UTF-8** 编码。
-   为所有函数、类和模块编写清晰的 **Docstrings**。

### 4.2 依赖管理 (Dependency Management)

-   所有项目依赖必须在 `pyproject.toml` 中声明。
-   严禁使用 `pip install` 直接在环境中安装未声明的包。
-   定期运行 `uv pip freeze > requirements.txt` 来更新锁定文件（如果需要）。

### 4.3 脚本设计原则 (Script Design Principles)

-   **单一职责:** 每个脚本文件应专注于完成一个具体的任务。
-   **幂等性 (Idempotency):** 脚本应设计为可重复执行。多次运行脚本应产生相同的结果，不会引发错误或意外副作用。
-   **清晰的日志:** 使用 `logging` 模块而非 `print()` 来输出信息。日志应包含不同级别（DEBUG, INFO, WARNING, ERROR），并能清晰地反映脚本的执行状态。
-   **健壮的错误处理:** 在可能失败的操作（如文件读写、网络请求）周围使用 `try...except` 块，并记录详细的错误信息。
-   **无交互执行:** 脚本应设计为在无人值守的情况下运行。所有必需的参数都应通过命令行参数或配置文件提供。

### 4.4 配置管理 (Configuration Management)

-   **严禁硬编码敏感信息** (如 API 密钥、密码、服务器地址)。
-   使用 **环境变量** (`.env` 文件) 或安全的配置文件来加载这些信息。`.env` 文件必须加入 `.gitignore`。

---

## 5. 新增脚本指南 (Guide to Adding New Scripts)

1.  **创建功能目录:** 在 `features/` 目录下创建一个新的子目录，以功能命名（如 `user_management`）。
2.  **编写脚本逻辑:** 在新目录中创建 `.py` 文件，并实现核心业务逻辑。
3.  **抽象共享模块:** 如果脚本中的某些功能（如数据库连接）可以被其他脚本复用，请将其抽象到 `modules/` 目录下。
4.  **注册到主入口:** 在 `main.py` 中添加一个新的子命令，将其链接到你新创建的脚本逻辑。
5.  **声明依赖:** 如果引入了新的第三方库，请将其添加到 `pyproject.toml`。
6.  **编写文档:** 在脚本的 Docstring 中简要说明其功能、用途和参数。
7.  **测试:** 在安全的环境中充分测试脚本，确保其行为符合预期。

---

## 6. 可用脚本说明 (Available Scripts)

本章节列出了当前库中可用的具体脚本及其使用方法。

### 6.1 合约信息同步 (`instrument sync`)

-   **功能:** 从 OpenCTP API 获取最新的金融合约数据，并与本地数据库进行全量同步（增、删、改）。
-   **模块路径:** `features/instrument_scraper/`

**执行命令:**

```bash
# 执行完整的数据库同步
uv run python main.py instrument sync
```

**可用选项:**

-   `--dry-run`: **模拟运行**。此选项会从 API 获取并处理数据，然后打印出将要执行的数据库操作的统计报告（将新增、更新、删除多少条记录），但**不会**对数据库进行任何实际的写入或修改。这对于在执行实际操作前进行验证非常有用。

    ```bash
    uv run python main.py instrument sync --dry-run
    ```

-   `--output-file <FILENAME>`: **输出到文件**。此选项会将从 API 获取并转换后的数据保存为一个 JSON 文件，而**不会**连接或修改数据库。这对于调试数据转换逻辑或离线分析数据非常方便。

    ```bash
    uv run python main.py instrument sync --output-file instruments.json
    ```


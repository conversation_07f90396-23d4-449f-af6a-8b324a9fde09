
# 02 - 风险组件配置与管理

## 1. 功能设计 (Functional Design)

本功能为被点价方（Setter）提供一个统一的界面，用于管理所有交易的执行规则。核心设计思想是**分层配置**和**优先级策略**。

### 1.1. 配置层级与优先级

系统在决定一笔已审核通过的交易 (`trade`) 的执行模式时，会按照以下顺序查找有效的风险配置规则：

1.  **合同层级 (Contract Level)**: 最高优先级。
    -   **查询条件**: `config_level = 'CONTRACT'` AND `contract_id = trade.contract_id` AND `is_active = TRUE`
    -   **应用场景**: 为某个特定的、高风险或有特殊要求的合同单独设置执行规则。例如，“这份与XX公司的合同，所有点价必须人工审核后手动回报”。

2.  **点价方层级 (Pricer Level)**: 中等优先级。
    -   **查询条件**: `config_level = 'PRICER'` AND `pricer_id = trade.pricer_id` AND `is_active = TRUE`
    -   **应用场景**: 根据交易对手的风险等级或合作模式进行配置。例如，“所有来自A公司的点价都采用模拟成交”，或“来自B公司的点价需要自动下单”。

3.  **全局层级 (Global Level)**: 最低优先级。
    -   **查询条件**: `config_level = 'GLOBAL'` AND `is_active = TRUE`
    -   **应用场景**: 作为默认的“兜底”规则。例如，“所有未被特殊指定的交易，默认都采用模拟成交”。

**规则匹配逻辑**: 系统会严格按照 **合同 -> 点价方 -> 全局** 的顺序查找。一旦在某个层级找到匹配的、激活的规则，则立即采用该规则的 `execution_mode`，后续层级的规则将被忽略。

### 1.2. 执行模式 (Execution Modes)

-   **`AUTOMATIC` (自动执行)**: 交易指令将被发送到 **[功能08 - 下单端与服务端WebSocket通信](../08-order-client-websocket-spec/01_Feature_Overview_and_Data_Model.md)** 中定义的本地交易终端，由终端自动向期货市场下单。
-   **`MANUAL` (人工反馈)**: 交易进入 `PendingManualFill` 状态，需要被点价方或其授权的操作员在系统中手动录入执行结果（成交价、成交量、失败原因等）。
-   **`SIMULATED` (模拟成交)**: 系统自动使用点价时快照的指数价 (`index_price_snapshot`) 作为成交价，即时完成交易。这通常用于内部测试、系统演示或与客户的模拟盘交易。

--- 

## 2. 接口定义 (API Definition) 路径需要根据实际情况更改

| 功能描述 | HTTP 方法 | 路径 (Endpoint) | 请求体说明 | 响应体说明 |
| :--- | :--- | :--- | :--- | :--- |
| **获取风险配置列表** | `GET` | `/api/v1/risk-configs` | `?level=CONTRACT&contractId=123` | `List<RiskConfig>` |
| **创建风险配置** | `POST` | `/api/v1/risk-configs` | `{setter_id, config_level, execution_mode, ...}` | `{success: true}` |
| **更新风险配置** | `PUT` | `/api/v1/risk-configs/{id}` | `{execution_mode, is_active, ...}` | `{success: true}` |
| **删除风险配置** | `DELETE` | `/api/v1/risk-configs/{id}` | (无) | `{success: true}` |

--- 

## 3. 相关页面 (Related Pages)

- **后台管理页面**: `admin/web/src/views/risk-management/configs.vue`
  - 提供一个完整的CRUD界面，允许被点价方管理其名下所有的风险配置规则。
  - 界面需要清晰地展示规则的层级、关联对象（合同/点价方）、执行模式和状态。

--- 

## 4. 测试用例 (Test Cases)

| 用例ID | 场景描述 | 测试步骤 | 预期结果 |
| :--- | :--- | :--- | :--- |
| `TC-RISK-001` | **合同级规则优先** | 1. 为合同A设置 `MANUAL` 模式。<br>2. 为点价方B设置 `AUTOMATIC` 模式。<br>3. 点价方B对合同A发起点价并被审核通过。 | 交易进入 `PendingManualFill` 状态，因为合同级规则优先。 |
| `TC-RISK-002` | **点价方级规则次之** | 1. 未给合同A设置规则。<br>2. 为点价方B设置 `AUTOMATIC` 模式。<br>3. 设置全局规则为 `SIMULATED`。<br>4. 点价方B对合同A发起点价并被审核通过。 | 交易进入 `Executing` 状态，因为匹配到了点价方级规则。 |
| `TC-RISK-003` | **全局规则兜底** | 1. 未给合同A和点价方B设置任何规则。<br>2. 设置全局规则为 `SIMULATED`。<br>3. 点价方B对合同A发起点价并被审核通过。 | 交易瞬间进入 `Filled` 状态，因为匹配到全局模拟成交规则。 |
| `TC-RISK-004` | **规则禁用** | 1. 为合同A设置 `MANUAL` 模式，但 `is_active=FALSE`。<br>2. 设置全局规则为 `SIMULATED`。<br>3. 对合同A的点价被审核通过。 | 交易进入 `Filled` 状态，因为合同A的规则未激活，系统采用全局规则。 |

--- 

## 5. 注意事项 (Notes/Caveats)

- **权限控制**: 只有被点价方（或其授权的管理员）才能创建、修改或删除自己 `setter_id` 下的风险配置。
- **界面友好性**: 在后台管理界面，当 `config_level` 为 `CONTRACT` 或 `PRICER` 时，应提供搜索和选择器来帮助用户方便地关联到具体的合同或点价方，而不是手动输入ID。

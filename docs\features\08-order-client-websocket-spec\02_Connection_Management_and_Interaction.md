# 02 - 连接管理与核心交互

## 1. 功能设计 (Functional Design)

### 1.1. 连接唯一性与冲突处理

- **核心约束**: 同一 `account_id` 只允许一个 `ACTIVE` 状态的 WebSocket 连接。
- **流程**:
  1. 客户端发起 `CONNECT_REQ`。
  2. 服务器根据 `account_id` 查询 Redis。
  3. **无冲突**: 建立新连接，注册到 Redis，状态为 `ACTIVE`，返回 `CONNECT_RES`。
  4. **有冲突 (存在 `ACTIVE` 连接)**: 
     - 服务器不立即断开旧连接。
     - 向**新连接**的客户端发送 `CONFLICT_NOTIFY` 消息，包含旧连接的信息。
     - 新连接在 Redis 中的状态记为 `PENDING_DISCONNECT`。
  5. **用户确认强制断开**:
     - 新客户端弹窗提示用户“账号已在别处登录，是否强制下线？”
     - 用户确认后，新客户端向服务器发送 `FORCE_DISCONNECT_REQ`，携带需要被断开的旧 `conn_id`。
     - 服务器验证通过后，断开旧连接，将旧连接状态更新为 `FORCE_CLOSED`，并将新连接的状态更新为 `ACTIVE`。

### 1.2. 心跳与时间同步

- **心跳**: 客户端每 **30秒** 发送一次 `HEARTBEAT_REQ`。服务器收到后，更新 Redis 中的 `last_active_time` 并回复 `HEARTBEAT_RES`。
- **超时断开**: 服务器侧定时任务检测，若 `last_active_time` 距当前时间超过 **45秒**，则认为连接丢失，主动断开并清理 Redis 记录。
- **时间同步**: 
  - `HEARTBEAT_REQ` 包含客户端时间戳，`HEARTBEAT_RES` 包含服务器时间戳。
  - 客户端可根据响应计算并校正与服务器的时间差，以确保后续所有时间相关计算的准确性。

### 1.3. 订单提交流程 (闭环)

- **流程**:
  1. **指令下发**: 服务器通过 WebSocket 向目标客户端发送 `ORDER_REQ`。
  2. **送达确认 (ACK)**: 客户端收到 `ORDER_REQ` 后，**必须立即**回复 `ACK_REQ`，告知服务器“指令已收到”。
  3. **超时重发**: 服务器若在 **5秒** 内未收到 `ACK_REQ`，将重发 `ORDER_REQ` (最多3次)。客户端需通过 `order_id` 对请求进行幂等处理，避免重复执行。
  4. **执行与反馈**: 客户端执行交易操作后，将最终结果通过 `ORDER_RSP` 反馈给服务器。
  5. **流程结束**: 服务器收到 `ORDER_RSP`，更新订单状态，完成闭环。

### 1.4. 断线重连

- 客户端必须实现自动重连机制。
- 建议采用指数退避策略（如首次1s，后续2s, 4s...），避免在服务器故障时发起DDoS攻击。
- 重连成功后，客户端需重新执行完整的 `CONNECT_REQ` 认证流程。

## 2. 接口定义 (API Definition)

| 功能描述 | WebSocket `msg_type` | 发起方 | 接收方 | 描述 |
| :--- | :--- | :--- | :--- | :--- |
| **连接与认证** | `CONNECT_REQ` | Client | Server | 客户端请求建立连接并认证 |
| | `CONNECT_RES` | Server | Client | 服务器响应连接成功 |
| | `CONFLICT_NOTIFY` | Server | Client | 服务器通知连接冲突 |
| | `ERROR_RES` | Server | Client | 服务器返回通用错误 |
| **心跳** | `HEARTBEAT_REQ` | Client | Server | 客户端发送心跳 |
| | `HEARTBEAT_RES` | Server | Client | 服务器响应心跳 |
| **订单执行** | `ORDER_REQ` | Server | Client | 服务器下发订单指令 |
| | `ACK_REQ` | Client | Server | 客户端确认收到订单指令 |
| | `ORDER_RSP` | Client | Server | 客户端反馈订单执行结果 |
| **冲突处理** | `FORCE_DISCONNECT_REQ` | Client | Server | 客户端请求强制断开旧连接 |
| | `FORCE_DISCONNECT_RES` | Server | Client | 服务器响应强制断开操作结果 |

## 3. 相关页面 (Related Pages)

- 本功能为后端和下单端桌面应用的核心通信机制，不直接关联前端页面。

## 4. 测试用例 (Test Cases)

| 用例ID | 场景描述 | 测试步骤 | 预期结果 |
| :--- | :--- | :--- | :--- |
| `TC-CONN-001` | **正常连接** | 1. 客户端A使用账户P登录。 | 连接成功，Redis中记录状态为 `ACTIVE`。 |
| `TC-CONN-002` | **连接冲突与强制下线** | 1. 客户端A已在线。<br>2. 客户端B使用同一账户P登录。<br>3. 客户端B确认强制下线。 | 1. 客户端B收到 `CONFLICT_NOTIFY`。<br>2. 客户端A被强制下线。<br>3. 客户端B连接成功，状态变为 `ACTIVE`。 |
| `TC-ORD-001` | **正常下单流程** | 1. 服务器发送 `ORDER_REQ`。<br>2. 客户端执行并返回 `ORDER_RSP`。 | 1. 服务器能收到 `ACK_REQ` 和 `ORDER_RSP`。<br>2. 订单状态最终更新成功。 |
| `TC-ORD-002` | **ACK超时重发** | 1. 服务器发送 `ORDER_REQ`。<br>2. 模拟客户端网络延迟，不回复 `ACK_REQ`。 | 1. 服务器在5秒后重发 `ORDER_REQ`。<br>2. 客户端能正确处理重复的请求，只执行一次。 |
| `TC-HB-001` | **心跳超时** | 1. 客户端连接后，停止发送心跳。 | 服务器在45秒后主动断开连接。 |

## 5. 注意事项 (Notes/Caveats)

- **幂等性**: 客户端必须对收到的 `ORDER_REQ` 做幂等处理，防止因网络重发导致重复下单。
- **安全性**: 生产环境必须使用 `wss://` 协议。`CONNECT_REQ` 中的 `token` 必须是短期有效的，用于防范重放攻击。
- **状态一致性**: 客户端在断线重连后，应主动向服务器请求同步当前未完成的订单状态，以确保数据一致性。

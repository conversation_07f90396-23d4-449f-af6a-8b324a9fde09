#!/usr/bin/env python3
"""
验证码刷新功能测试脚本

专门测试验证码刷新是否会导致系统卡死
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QVBoxLayout, QWidget, QPushButton, QLabel
from PySide6.QtCore import QTimer

# 设置日志
logging.basicConfig(level=logging.INFO)

def test_captcha_refresh():
    """测试验证码刷新功能"""
    try:
        # 创建应用
        app = QApplication(sys.argv)
        
        # 导入必要的模块
        from vnpy.event import EventEngine
        from ui.login.login_window import LoginWindow
        
        # 创建事件引擎
        event_engine = EventEngine()
        
        # 创建登录窗口
        login_window = LoginWindow(event_engine)
        
        # 显示窗口
        login_window.show_centered()
        
        # 创建测试窗口
        test_widget = QWidget()
        test_widget.setWindowTitle("验证码刷新测试")
        test_widget.resize(300, 200)
        
        layout = QVBoxLayout(test_widget)
        
        info_label = QLabel("点击下面的按钮测试验证码刷新功能\n观察是否会导致系统卡死")
        layout.addWidget(info_label)
        
        # 手动刷新按钮
        manual_refresh_btn = QPushButton("手动刷新验证码")
        manual_refresh_btn.clicked.connect(login_window.on_refresh_captcha_requested)
        layout.addWidget(manual_refresh_btn)
        
        # 自动刷新按钮
        auto_refresh_btn = QPushButton("开始自动刷新测试（每2秒）")
        auto_timer = QTimer()
        auto_timer.timeout.connect(login_window.on_refresh_captcha_requested)
        
        def toggle_auto_refresh():
            if auto_timer.isActive():
                auto_timer.stop()
                auto_refresh_btn.setText("开始自动刷新测试（每2秒）")
            else:
                auto_timer.start(2000)  # 每2秒刷新一次
                auto_refresh_btn.setText("停止自动刷新测试")
        
        auto_refresh_btn.clicked.connect(toggle_auto_refresh)
        layout.addWidget(auto_refresh_btn)
        
        # 显示测试窗口
        test_widget.show()
        
        # 设置窗口关闭时退出应用
        def on_window_closed():
            auto_timer.stop()
            app.quit()
        
        login_window.finished.connect(on_window_closed)
        test_widget.closeEvent = lambda event: on_window_closed()
        
        print("=" * 50)
        print("验证码刷新测试启动")
        print("=" * 50)
        print("1. 点击'手动刷新验证码'按钮测试单次刷新")
        print("2. 点击'开始自动刷新测试'按钮测试连续刷新")
        print("3. 观察系统是否会卡死或出现线程错误")
        print("4. 关闭任意窗口退出测试")
        print("=" * 50)
        
        # 运行应用
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_captcha_refresh()

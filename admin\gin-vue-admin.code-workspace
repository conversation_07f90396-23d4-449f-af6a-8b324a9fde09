{"folders": [{"path": "server", "name": "backend"}, {"path": "web", "name": "frontend"}, {"path": ".", "name": "root"}], "settings": {"go.toolsEnvVars": {"GOPROXY": "https://goproxy.cn,direct", "GONOPROXY": "none;"}}, "launch": {"version": "0.2.0", "configurations": [{"type": "go", "request": "launch", "name": "Backend", "cwd": "${workspaceFolder:backend}", "program": "${workspaceFolder:backend}/"}, {"type": "node", "request": "launch", "cwd": "${workspaceFolder:frontend}", "name": "Frontend", "runtimeExecutable": "npm", "runtimeArgs": ["run-script", "serve"]}], "compounds": [{"name": "Both (Backend & Frontend)", "configurations": ["Backend", "Frontend"], "stopAll": true}]}}
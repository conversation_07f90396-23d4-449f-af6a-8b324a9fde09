"""
WebSocket数据模型

定义WebSocket通信中使用的数据结构和消息类型
重构版本：移除订阅相关模型，增强心跳消息，统一消息格式
"""

import json
import time
from dataclasses import dataclass, field
from enum import Enum
from typing import Optional, Dict, Any, List
from datetime import datetime


class ConnectionState(Enum):
    """WebSocket连接状态枚举"""
    DISCONNECTED = "disconnected"      # 未连接
    CONNECTING = "connecting"          # 连接中
    CONNECTED = "connected"            # 已连接但未认证
    AUTHENTICATING = "authenticating"  # 认证中
    AUTHENTICATED = "authenticated"    # 已连接且已认证
    ERROR = "error"                    # 错误状态
    RECONNECTING = "reconnecting"      # 重连中


@dataclass
class ConnectionInfo:
    """连接信息"""
    client_id: Optional[str] = None
    server_url: Optional[str] = None
    connect_time: Optional[datetime] = None
    last_heartbeat: Optional[datetime] = None
    state: ConnectionState = ConnectionState.DISCONNECTED

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "client_id": self.client_id,
            "server_url": self.server_url,
            "connect_time": self.connect_time.isoformat() if self.connect_time else None,
            "last_heartbeat": self.last_heartbeat.isoformat() if self.last_heartbeat else None,
            "state": self.state.value
        }


@dataclass
class WSMessageEnvelope:
    """WebSocket消息基类，对应服务端OrderMessageEnvelope
    
    统一消息格式：{事件名, 数据(object), 时间戳}
    """
    event: str                                    # 事件类型
    payload: Optional[Dict[str, Any]] = None     # 消息载荷（数据对象）
    timestamp: int = field(default_factory=lambda: int(time.time() * 1000))  # 时间戳（毫秒）
    source: str = "order_client"                 # 消息来源
    seq: Optional[int] = None                    # 序列号（可选）
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = {
            "event": self.event,
            "timestamp": self.timestamp,
            "source": self.source
        }
        if self.payload is not None:
            data["payload"] = self.payload
        if self.seq is not None:
            data["seq"] = self.seq
        return data
    
    @classmethod
    def from_json(cls, json_str: str) -> 'WSMessageEnvelope':
        """从JSON字符串创建消息"""
        data = json.loads(json_str)
        return cls(
            event=data["event"],
            payload=data.get("payload"),
            timestamp=data.get("timestamp", int(time.time() * 1000)),
            source=data.get("source", "unknown"),
            seq=data.get("seq")
        )
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WSMessageEnvelope':
        """从字典创建消息"""
        return cls(
            event=data["event"],
            payload=data.get("payload"),
            timestamp=data.get("timestamp", int(time.time() * 1000)),
            source=data.get("source", "unknown"),
            seq=data.get("seq")
        )


@dataclass
class AuthMessage(WSMessageEnvelope):
    """认证消息"""
    def __init__(self, token: str):
        super().__init__(
            event="auth",
            payload={"token": token}
        )


@dataclass  
class PingMessage(WSMessageEnvelope):
    """心跳Ping消息 - 增强版本，包含客户端时间戳"""
    def __init__(self, client_timestamp: Optional[int] = None):
        client_ts = client_timestamp or int(time.time() * 1000)
        super().__init__(
            event="ping",
            payload={"client_timestamp": client_ts},
            timestamp=client_ts
        )


@dataclass
class PongMessage(WSMessageEnvelope):
    """心跳Pong响应消息 - 新增，处理服务端响应"""
    def __init__(self, client_timestamp: int, server_timestamp: Optional[int] = None):
        server_ts = server_timestamp or int(time.time() * 1000)
        super().__init__(
            event="pong",
            payload={
                "client_timestamp": client_timestamp,
                "server_timestamp": server_ts
            },
            timestamp=server_ts
        )


@dataclass
class WSConfig:
    """WebSocket配置"""
    server_url: str = "ws://localhost:8888/ws/order"
    connect_timeout: int = 10
    heartbeat_interval: int = 5 
    heartbeat_timeout: int = 10
    reconnect_enabled: bool = True
    max_reconnect_attempts: int = 10
    reconnect_initial_delay: int = 1
    reconnect_max_delay: int = 60
    reconnect_backoff_factor: float = 2.0
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WSConfig':
        """从字典创建配置"""
        return cls(**{k: v for k, v in data.items() if hasattr(cls, k)})
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "server_url": self.server_url,
            "connect_timeout": self.connect_timeout,
            "heartbeat_interval": self.heartbeat_interval,
            "heartbeat_timeout": self.heartbeat_timeout,
            "reconnect_enabled": self.reconnect_enabled,
            "max_reconnect_attempts": self.max_reconnect_attempts,
            "reconnect_initial_delay": self.reconnect_initial_delay,
            "reconnect_max_delay": self.reconnect_max_delay,
            "reconnect_backoff_factor": self.reconnect_backoff_factor,
        }


@dataclass
class WSStats:
    """WebSocket统计信息"""
    messages_sent: int = 0
    messages_received: int = 0
    bytes_sent: int = 0
    bytes_received: int = 0
    reconnect_count: int = 0
    last_error: Optional[str] = None
    uptime_seconds: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "messages_sent": self.messages_sent,
            "messages_received": self.messages_received,
            "bytes_sent": self.bytes_sent,
            "bytes_received": self.bytes_received,
            "reconnect_count": self.reconnect_count,
            "last_error": self.last_error,
            "uptime_seconds": self.uptime_seconds,
        }


# 业务消息模型

@dataclass
class OrderUpdateMessage(WSMessageEnvelope):
    """订单更新消息"""
    def __init__(self, order_data: Dict[str, Any]):
        super().__init__(
            event="order_update",
            payload=order_data
        )


@dataclass
class MarketDataMessage(WSMessageEnvelope):
    """市场数据消息"""
    def __init__(self, market_data: Dict[str, Any]):
        super().__init__(
            event="market_data",
            payload=market_data
        )


@dataclass
class ErrorMessage(WSMessageEnvelope):
    """错误消息"""
    def __init__(self, error_type: str, error_message: str, details: Optional[Dict[str, Any]] = None):
        payload = {
            "error_type": error_type,
            "message": error_message
        }
        if details:
            payload.update(details)
            
        super().__init__(
            event="error",
            payload=payload
        )


@dataclass
class NotificationMessage(WSMessageEnvelope):
    """通知消息"""
    def __init__(self, notification_type: str, title: str, content: str, data: Optional[Dict[str, Any]] = None):
        payload = {
            "type": notification_type,
            "title": title,
            "content": content
        }
        if data:
            payload["data"] = data
            
        super().__init__(
            event="notification",
            payload=payload
        )
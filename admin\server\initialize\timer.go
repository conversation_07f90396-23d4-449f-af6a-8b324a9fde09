package initialize

import (
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/task"
	"github.com/flipped-aurora/gin-vue-admin/server/service"

	"github.com/robfig/cron/v3"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"go.uber.org/zap"
)

func Timer() {
	go func() {
		var option []cron.Option
		option = append(option, cron.WithSeconds())
		// 清理DB定时任务
		_, err := global.GVA_Timer.AddTaskByFunc("ClearDB", "@daily", func() {
			err := task.ClearTable(global.GVA_DB) // 定时任务方法定在task文件包中
			if err != nil {
				fmt.Println("timer error:", err)
			}
		}, "定时清理数据库【日志，黑名单】内容", option...)
		if err != nil {
			fmt.Println("add timer error:", err)
		}

		// 报价过期检查定时任务 - 每小时执行一次
		_, err = global.GVA_Timer.AddTaskByFunc("ExpireQuotations", "0 0 * * * *", func() {
			count, err := service.ServiceGroupApp.DianjiaServiceGroup.QuotationService.ExpireQuotations()
			if err != nil {
				global.GVA_LOG.Error("执行报价过期任务失败", zap.Error(err))
			} else {
				global.GVA_LOG.Info("报价过期任务执行完成", zap.Int("过期数量", count))
			}
		}, "定时检查并处理过期报价", option...)
		if err != nil {
			global.GVA_LOG.Error("添加报价过期任务失败", zap.Error(err))
		}

		// 其他定时任务定在这里 参考上方使用方法
	}()
}

# 功能模块：商品基差点价交易

## 1. 功能模块简介 (Summary)

本模块为大宗商品交易提供线上化的基差点价与固定价点价（洗基差）平台。核心围绕**合同（Contract）**、**交易请求（TradeRequest）** 与 **执行明细（ExecutionDetail）** 三大主线展开，实现了灵活的“多对多”交易匹配模式：

- **被点价方（Setter）** 创建和管理具有独立生命周期的合同，合同定义了可交易的总量、价格类型（基差或固定价）等。
- **点价方（Pricer）** 发起交易请求，表达交易意图（如“买入100吨”），而不直接绑定单个合同。
- **系统** 自动匹配一个或多个符合条件的合同来满足该交易请求，并在执行明细中记录匹配关系。
- 交易流程实现了合同与交易的解耦，提高了系统的灵活性和资源利用率。
- 详细合同结构与管理见《02_Contract_Management.md》，交易请求与执行流程见《03_Trade_Management.md》。

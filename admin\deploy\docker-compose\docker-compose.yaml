version: "3"

# 声明一个名为network的networks,subnet为network的子网地址,默认网关是*********
networks:
  network:
    ipam:
      driver: default
      config:
        - subnet: '*********/16'
        
# 设置mysql，redis持久化保存
volumes:
  mysql:
  redis:
  
services:
  web:
    build:
      context: ../../web
      dockerfile: ./Dockerfile
    container_name: gva-web
    restart: always
    ports:
      - '8080:8080'
    depends_on:
      - server
    command: [ 'nginx-debug', '-g', 'daemon off;' ]
    networks:
      network:
        ipv4_address: *********1

  server:
    build:
      context: ../../server
      dockerfile: ./Dockerfile
    container_name: gva-server
    restart: always
    ports:
      - '8888:8888'
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    links:
      - mysql
      - redis
    networks:
      network:
        ipv4_address: *********2

  mysql:
    image: mysql:8.0.21       # 如果您是 arm64 架构：如 MacOS 的 M1，请修改镜像为 image: mysql/mysql-server:8.0.21
    container_name: gva-mysql
    command: mysqld --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci #设置utf8字符集
    restart: always
    ports:
      - "13306:3306"  # host物理直接映射端口为13306
    environment:
      #MYSQL_ROOT_PASSWORD: 'Aa@6447985' # root管理员用户密码
      MYSQL_DATABASE: 'qmPlus' # 初始化启动时要创建的数据库的名称
      MYSQL_USER: 'gva'
      MYSQL_PASSWORD: 'Aa@6447985'
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "gva", "-pAa@6447985"]
      interval: 10s
      timeout: 5s
      retries: 3
    volumes:
      - mysql:/var/lib/mysql
    networks:
      network:
        ipv4_address: *********3

  redis:
    image: redis:6.0.6
    container_name: gva-redis # 容器名
    restart: always
    ports:
      - '16379:6379'
    healthcheck:
      test: ["CMD-SHELL", "redis-cli ping | grep PONG || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 3
    volumes:
      - redis:/data
    networks:
      network:
        ipv4_address: **********

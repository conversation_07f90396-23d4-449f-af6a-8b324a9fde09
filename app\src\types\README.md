# 类型定义说明

本目录统一管理项目中的 TypeScript 类型定义，避免重复定义和提高代码维护性。

## 文件结构

```
src/types/
├── index.ts          # 统一导出所有类型
├── user.ts           # 用户相关类型
├── auth.ts           # 认证登录相关类型
└── README.md         # 本说明文件
```

## 类型组织原则

1. **按功能模块分类**：不同业务模块的类型放在对应的文件中
2. **避免重复定义**：统一的类型只定义一次，其他地方通过导入使用
3. **向后兼容**：保留旧版本的类型定义，标记为 `@deprecated`
4. **统一导出**：通过 `index.ts` 提供统一的导出入口

## 使用方式

### 推荐用法（从统一入口导入）
```typescript
import type { IUser, ILoginResponse, IPhoneLoginRequest } from '@/types'
```

### 也可以从具体文件导入
```typescript
import type { IUser } from '@/types/user'
import type { IPhoneLoginRequest } from '@/types/auth'
```

### 通过 API 模块导入（保持现有兼容性）
```typescript
import type { IPhoneLoginRequest, IUsernameLoginRequest } from '@/api/auth'
```

## 迁移说明

原有的类型定义已从以下位置迁移至统一的类型文件：

- `@/api/auth.ts` 中的接口类型 → `@/types/auth.ts` 和 `@/types/user.ts`
- `@/api/types/login.ts` 中的部分类型 → `@/types/auth.ts` 和 `@/types/user.ts`

旧的导入方式仍然可用，但推荐使用新的统一导入方式。

## 添加新类型

1. 根据功能确定应该放在哪个文件中
2. 在对应文件中添加类型定义
3. 在 `index.ts` 中添加导出
4. 更新相关的 API 文件导出（如果需要）
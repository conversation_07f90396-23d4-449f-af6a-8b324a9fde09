<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "交易执行"
  }
}
</route>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { useRouter } from 'vue-router'
import type { TradeRequestType, ITradeRequest, ExecutionMode, TradeRequestStatus } from '@/types/trade-request'
import type { IContractResponse, ContractStatus } from '@/types/contract'
import type { IInstrument, IInstrumentSelectItem } from '@/types/instrument'
import { pointPrice, basisWash, getMyTradeRequestsAsPricer } from '@/api/traderequest'
import { getInstrumentById } from '@/api/instrument'
import { getContractsAsPricer } from '@/api/contract'
import { toast } from '@/utils/toast'
import QuoteCard from '@/components/QuoteCard.vue'

defineOptions({
  name: 'TradeExecutePage',
})

const router = useRouter()

// 响应式状态
const requestType = ref<TradeRequestType>('PointPrice')
const instrumentId = ref<number | null>(null)
const setterID = ref<number | null>(null)      // 被点价方ID - 新增
const setterName = ref<string>('')            // 被点价方名称 - 新增
const instrument = ref<IInstrumentSelectItem | null>(null)
const tradeVolume = ref<number | null>(null)
const requestedPrice = ref<number | null>(null)
const todayTradeRequests = ref<ITradeRequest[]>([])
const availableContracts = ref<IContractResponse[]>([])
const loading = ref(false)

let priceInterval: ReturnType<typeof setInterval> | null = null

// 计算属性
const isPointPrice = computed(() => requestType.value === 'PointPrice')
const isBasisWash = computed(() => requestType.value === 'BasisWash')

// 计算总可用数量
const totalAvailableQuantity = computed(() => {
  return availableContracts.value.reduce((total, contract) => total + (contract.remainingQuantity - contract.frozenQuantity), 0)
})

// 根据当前操作类型过滤合同
const currentAvailableContracts = computed(() => {
  if (isPointPrice.value) {
    // 点价操作：只显示基差合同
    return availableContracts.value.filter(contract => contract.priceType === 'basis')
  } else {
    // 洗基差操作：只显示固定价合同
    return availableContracts.value.filter(contract => contract.priceType === 'fixed')
  }
})

// 当前操作类型的可用数量
const currentAvailableQuantity = computed(() => {
  return currentAvailableContracts.value.reduce((total, contract) => total + (contract.remainingQuantity - contract.frozenQuantity), 0)
})

// 当前操作类型的加权平均价格
const currentWeightedPrice = computed(() => {
  if (currentAvailableContracts.value.length === 0) return 0

  let totalWeightedPrice = 0
  let totalQuantity = 0

  currentAvailableContracts.value.forEach(contract => {
    const availableQuantity = contract.remainingQuantity - contract.frozenQuantity
    if (availableQuantity > 0) {
      totalWeightedPrice += contract.priceValue * availableQuantity
      totalQuantity += availableQuantity
    }
  })

  return totalQuantity > 0 ? totalWeightedPrice / totalQuantity : 0
})

// 当前操作类型的今日交易请求
const currentTodayTradeRequests = computed(() => {
  return todayTradeRequests.value.filter(request => true)
})

// 按钮禁用状态
const isButtonDisabled = computed(() => {
  // 基本条件：没有输入数量
  if (!tradeVolume.value) return true

  // 没有可用合同
  if (currentAvailableQuantity.value === 0) return true

  // 数量超过可用数量
  if (tradeVolume.value > currentAvailableQuantity.value) return true

  // 点价操作需要输入价格
  if (isPointPrice.value && !requestedPrice.value) return true

  // 正在加载中
  if (loading.value) return true

  return false
})

// 按钮文本
const getButtonText = computed(() => {
  if (loading.value) return '提交中...'

  if (currentAvailableQuantity.value === 0) {
    return `暂无可用${isPointPrice.value ? '基差' : '固定价'}合同`
  }

  return isPointPrice.value ? '确认点价' : '确认洗基差'
})



// 预估总价
const estimatedTotalPrice = computed(() => {
  if (!tradeVolume.value) return '0.00'

  if (isPointPrice.value) {
    // 点价：使用用户输入的价格
    if (!requestedPrice.value) return '0.00'
    return (requestedPrice.value * tradeVolume.value).toFixed(2)
  } else {
    // 洗基差：使用当前操作类型的加权平均价格
    return (currentWeightedPrice.value * tradeVolume.value).toFixed(2)
  }
})

// 处理行情组件的价格点击事件
interface PriceClickEvent {
  type: 'last' | 'bid' | 'ask' | 'limit_up' | 'limit_down'
  price: number
  numericPrice: number
}

const handlePriceClick = (event: PriceClickEvent) => {
  // 如果是点价操作，自动填入点击的价格
  if (isPointPrice.value) {
    requestedPrice.value = event.numericPrice
    toast.success(`已选择${getPriceTypeName(event.type)}价格: ${event.price}`)
  }
}

const getPriceTypeName = (type: PriceClickEvent['type']) => {
  const typeNames = {
    last: '最新',
    bid: '买一',
    ask: '卖一',
    limit_up: '涨停',
    limit_down: '跌停'
  }
  return typeNames[type]
}

// 加载期货合约信息
async function loadInstrument(id: number) {
  try {
    console.log('开始加载期货合约信息, ID:', id)
    const response = await getInstrumentById(id)
    console.log('期货合约API响应:', response)
    if (response.code === 0) {
      instrument.value = response.data
      console.log('期货合约信息加载成功:', response.data)
    } else {
      console.warn('期货合约API返回错误:', response.msg)
    }
  } catch (error) {
    console.error('加载期货合约信息失败:', error)
  }
}

// 加载可用合同
async function loadAvailableContracts() {
  if (!instrumentId.value || !setterID.value) {
    console.log('跳过加载合同：缺少必要参数', { instrumentId: instrumentId.value, setterID: setterID.value })
    return
  }

  try {
    console.log('开始加载可用合同', { instrumentId: instrumentId.value, setterID: setterID.value })
    const response = await getContractsAsPricer({ status: 'Executing' })
    console.log('合同API响应:', response)
    if (response.code === 0) {
      // 客户端过滤：只显示匹配的期货合约和被点价方的合同
      const filteredContracts = response.data.list.filter(contract =>
        contract.instrumentRefID === instrumentId.value &&
        contract.setterID === setterID.value &&
        (contract.remainingQuantity - contract.frozenQuantity) > 0
      )
      availableContracts.value = filteredContracts
      console.log('可用合同加载成功:', filteredContracts)
    } else {
      console.warn('合同API返回错误:', response.msg)
    }
  } catch (error) {
    console.error('加载可用合同失败:', error)
  }
}

// 加载今日交易请求
async function loadTodayTradeRequests() {
  try {
    const today = new Date()

    // 格式化日期为 YYYY-MM-DD HH:mm:ss 格式，与后端期望的格式一致
    const formatDate = (date: Date) => {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    }

    // 今天的开始时间：00:00:00
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0)
    // 今天的结束时间：23:59:59
    const todayEnd = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59)

    const params = {
      instrumentRefID: instrumentId.value || undefined,
      startDate: formatDate(todayStart), // 今天 00:00:00
      endDate: formatDate(todayEnd)       // 今天 23:59:59
    }
    console.log('开始加载今日交易请求', params)
    console.log('日期范围:', {
      startDate: params.startDate,
      endDate: params.endDate,
      todayStart: todayStart.toISOString(),
      todayEnd: todayEnd.toISOString()
    })
    const response = await getMyTradeRequestsAsPricer(params)
    console.log('今日交易请求API响应:', response)
    if (response.code === 0) {
      // 客户端过滤：只显示当前被点价方的交易请求
      const filteredRequests = response.data.list.filter(request =>
        !setterID.value || request.setterID === setterID.value
      )
      todayTradeRequests.value = filteredRequests
      console.log('今日交易请求加载成功:', filteredRequests)
    } else {
      console.warn('今日交易请求API返回错误:', response.msg)
    }
  } catch (error) {
    console.error('加载今日交易请求失败:', error)
  }
}



// 处理交易请求提交
async function handleTradeRequest() {
  if (!tradeVolume.value || tradeVolume.value <= 0) {
    toast.error('请输入有效的数量')
    return
  }

  if (tradeVolume.value > totalAvailableQuantity.value) {
    toast.error(`操作数量不能超过可用总量 ${totalAvailableQuantity.value} 手`)
    return
  }

  if (totalAvailableQuantity.value === 0) {
    toast.error('暂无可用合同，无法进行操作')
    return
  }

  if (!instrumentId.value) {
    toast.error('期货合约信息缺失')
    return
  }

  if (!setterID.value) {
    toast.error('被点价方信息缺失')
    return
  }

  if (isPointPrice.value && (!requestedPrice.value || requestedPrice.value <= 0)) {
    toast.error('点价操作需要输入有效的价格')
    return
  }

  loading.value = true
  
  try {
    let response: any

    // 确保数值类型正确
    const quantity = typeof tradeVolume.value === 'string' ? parseInt(tradeVolume.value, 10) : tradeVolume.value!
    const price = typeof requestedPrice.value === 'string' ? parseFloat(requestedPrice.value) : requestedPrice.value!

    // 验证数值有效性
    if (isNaN(quantity) || quantity <= 0) {
      toast.error('请输入有效的数量')
      return
    }

    // 验证数量不超过当前操作类型的可用数量
    if (quantity > currentAvailableQuantity.value) {
      toast.error(`数量不能超过可用的${isPointPrice.value ? '基差' : '固定价'}合同数量 ${currentAvailableQuantity.value} 手`)
      return
    }

    if (isPointPrice.value && (isNaN(price) || price <= 0)) {
      toast.error('请输入有效的价格')
      return
    }

    console.log('发送交易请求参数:', {
      setterID: setterID.value,
      instrumentId: instrumentId.value,
      quantity,
      price: isPointPrice.value ? price : undefined,
      requestType: requestType.value
    })

    if (isPointPrice.value) {
      // 点价操作
      response = await pointPrice(
        setterID.value!,
        instrumentId.value!,
        quantity,
        price,
        'MANUAL', // V4: 增加执行模式
        new Date(Date.now() + 3600 * 1000).toISOString() // V4: 增加1小时的过期时间
      )
    } else {
      // 洗基差操作
      response = await basisWash(
        setterID.value!,
        instrumentId.value!,
        quantity,
        'MANUAL', // V4: 增加执行模式
        new Date(Date.now() + 3600 * 1000).toISOString() // V4: 增加1小时的过期时间
      )
    }

    if (response.code === 0) {
      const typeText = isPointPrice.value ? '点价' : '洗基差'
      toast.success(`${typeText}请求已提交`)
      
      // 重置表单
      tradeVolume.value = null
      requestedPrice.value = null
      
      // 刷新今日交易请求列表
      loadTodayTradeRequests()
    } else {
      toast.error(response.msg || '提交失败')
    }
  } catch (error) {
    console.error('提交交易请求失败:', error)
    toast.error('网络错误，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 获取交易请求状态文本
function getTradeRequestStatusText(status: TradeRequestStatus): string {
  const statusMap: Record<TradeRequestStatus, string> = {
    Executing: '执行中',
    Completed: '已完成',
    Rejected: '已拒绝',
    Cancelled: '已取消',
    Expired: '已过期'
  }
  return statusMap[status] || status
}

// 获取交易请求状态样式
function getTradeRequestStatusClass(status: TradeRequestStatus): string {
  const statusClassMap: Record<TradeRequestStatus, string> = {
    Executing: 'text-blue-500',
    Completed: 'text-green-500',
    Rejected: 'text-red-600',
    Cancelled: 'text-gray-500',
    Expired: 'text-orange-500'
  }
  return statusClassMap[status] || 'text-gray-500'
}

// Tab 切换函数
function switchTab(newRequestType: TradeRequestType) {
  console.log('切换 Tab:', newRequestType)
  requestType.value = newRequestType

  // 重置表单状态
  tradeVolume.value = null
  requestedPrice.value = null

  // 重新加载今日交易请求
  loadTodayTradeRequests()
}

// 处理页面参数的函数
function handlePageParams(options: any) {
  console.log('处理页面参数:', options)

  // 重置状态
  tradeVolume.value = null
  requestedPrice.value = null

  // 重置数据
  availableContracts.value = []
  todayTradeRequests.value = []
  instrument.value = null

  if (options?.requestType) {
    requestType.value = options.requestType as TradeRequestType
    console.log('设置请求类型:', requestType.value)
  } else {
    // 如果没有传递操作类型，默认设置为点价
    requestType.value = 'PointPrice'
  }

  if (options?.instrumentId) {
    const newInstrumentId = parseInt(options.instrumentId, 10)
    instrumentId.value = newInstrumentId
    loadInstrument(instrumentId.value)
    loadTodayTradeRequests()
  }

  if (options?.setterID) {
    const newSetterID = parseInt(options.setterID, 10)
    setterID.value = newSetterID
  }

  if (options?.setter) {
    setterName.value = options.setter
  }

  // 当instrumentId和setterID都有值时，加载可用合同
  if (instrumentId.value && setterID.value) {
    loadAvailableContracts()
  }
}

// 生命周期钩子
onLoad((options) => {
  handlePageParams(options)
})

// 每次页面显示时重新处理参数
onShow(() => {
  console.log('execute page onShow')
  // 获取当前页面的参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  if (currentPage && currentPage.options) {
    console.log('onShow 重新处理参数:', currentPage.options)

    // 检查参数是否发生变化
    const newInstrumentId = currentPage.options.instrumentId ? parseInt(currentPage.options.instrumentId, 10) : null
    const newSetterID = currentPage.options.setterID ? parseInt(currentPage.options.setterID, 10) : null
    const newTimestamp = currentPage.options._t

    const paramsChanged =
      newInstrumentId !== instrumentId.value ||
      newSetterID !== setterID.value ||
      currentPage.options.setter !== setterName.value ||
      newTimestamp // 如果有时间戳参数，说明是新的跳转

    if (paramsChanged) {
      console.log('参数发生变化或新的跳转，重新加载数据')
      handlePageParams(currentPage.options)
    }
  }
})

onUnmounted(() => {
  if (priceInterval) clearInterval(priceInterval)
})

</script>

<template>
  <view class="trade-execute-page min-h-screen bg-gray-100">
    <wd-navbar title="交易执行" :fixed="false" />

    <view class="p-3">
      <!-- 1. Tab 切换区域 -->
      <view class="card bg-white rounded-lg shadow-sm mb-3">
        <view class="tab-container">
          <view
            class="tab-item"
            :class="{ 'active': requestType === 'PointPrice' }"
            @click="switchTab('PointPrice')"
          >
            <text class="tab-text">点价</text>
          </view>
          <view
            class="tab-item"
            :class="{ 'active': requestType === 'BasisWash' }"
            @click="switchTab('BasisWash')"
          >
            <text class="tab-text">洗基差</text>
          </view>
        </view>
      </view>

      <!-- 2. 基本信息显示 -->
      <view class="card bg-white rounded-lg shadow-sm p-4 mb-3">
        <view class="text-center text-sm text-gray-600 space-y-1">
          <view v-if="instrument">
            期货合约: {{ instrument.instrument_name }} ({{ instrument.instrument_id }})
          </view>
          <view v-if="setterName">
            被点价方: {{ setterName }}
          </view>
          <view v-if="currentAvailableQuantity > 0" class="mt-2 pt-2 border-t border-gray-100">
            <view class="flex justify-between items-center">
              <text class="text-blue-600 font-medium">可用总量: {{ currentAvailableQuantity }} 手</text>
              <text class="text-green-600 font-medium">加权价格: {{ currentWeightedPrice.toFixed(2) }}</text>
            </view>
          </view>
          <view v-else class="mt-2 pt-2 border-t border-gray-100">
            <text class="text-orange-500">暂无可用{{ isPointPrice ? '基差' : '固定价' }}合同</text>
          </view>
        </view>
      </view>

      <!-- 2. 实时行情 -->
      <QuoteCard
        :instrument-id="instrument?.instrument_id || ''"
        :symbol="instrument?.instrument_id || 'i2510'"
        :exchange="'DCE'"
        @price-click="handlePriceClick"
      />

      <!-- 3. 核心操作区 -->
      <view class="card bg-white rounded-lg shadow-sm p-4 mb-3">
        <wd-input
          label="操作数量(手)"
          type="number"
          placeholder="请输入数量"
          v-model.number="tradeVolume"
          :max="currentAvailableQuantity"
          clearable
        />
        <view v-if="currentAvailableQuantity > 0" class="text-xs text-gray-400 mt-1">
          最大可操作数量: {{ currentAvailableQuantity }} 手
        </view>

        <!-- 点价操作：显示价格输入框 -->
        <wd-input
          v-if="isPointPrice"
          label="点价价格"
          type="number"
          placeholder="请输入点价价格"
          v-model.number="requestedPrice"
          clearable
          class="mt-3"
        />

        <!-- 洗基差操作：显示当前加权价格（只读） -->
        <wd-input
          v-else
          label="基准价格"
          :model-value="currentWeightedPrice.toFixed(2)"
          readonly
          disabled
          class="mt-3"
        />

        <view v-if="tradeVolume && (isPointPrice ? requestedPrice : true)" class="text-sm text-gray-500 mt-3">
          预估总价: <text class="text-base font-semibold text-red-500">¥ {{ estimatedTotalPrice }}</text>
        </view>

        <wd-button
          :type="isPointPrice ? 'success' : 'primary'"
          block
          :loading="loading"
          :disabled="isButtonDisabled"
          @click="handleTradeRequest"
          class="mt-4"
        >
          {{ getButtonText }}
        </wd-button>
      </view>

      <!-- 4. 当日交易请求概览 -->
      <view class="card bg-white rounded-lg shadow-sm">
        <view class="p-3 border-b border-gray-100">
          <h2 class="text-base font-semibold text-gray-800">当日{{ isPointPrice ? '点价' : '洗基差' }}请求</h2>
        </view>
        <view class="p-3">
          <view v-if="currentTodayTradeRequests.length === 0" class="text-center text-sm text-gray-400 py-4">暂无{{ isPointPrice ? '点价' : '洗基差' }}记录</view>
          <view v-else>
            <view v-for="request in currentTodayTradeRequests" :key="request.ID" class="grid grid-cols-4 gap-2 text-xs py-2 border-b border-gray-100 last:border-none">
              <text class="font-semibold">{{ request.requestType === 'PointPrice' ? '点价' : '洗基差' }}</text>
              <text>{{ request.requestedQuantity }} 手</text>
              <text v-if="request.requestedPrice">@ {{ request.requestedPrice.toFixed(2) }}</text>
              <text v-else>-</text>
              <text class="text-right font-medium" :class="getTradeRequestStatusClass(request.status)">
                {{ getTradeRequestStatusText(request.status) }}
              </text>
            </view>
          </view>
        </view>
      </view>

    </view>
  </view>
</template>

<style lang="scss" scoped>
.trade-execute-page {
  background-color: #f7f8fa;
}

// Tab 切换样式
.tab-container {
  display: flex;
  background-color: #f5f7fa;
  border-radius: 8rpx;
  padding: 8rpx;
  margin: 16rpx;

  .tab-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx 16rpx;
    border-radius: 6rpx;
    transition: all 0.3s ease;
    cursor: pointer;

    .tab-text {
      font-size: 28rpx;
      font-weight: 500;
      color: #606266;
      transition: color 0.3s ease;
    }

    &.active {
      background-color: white;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

      .tab-text {
        color: #409eff;
        font-weight: 600;
      }
    }

    &:hover:not(.active) {
      background-color: rgba(255, 255, 255, 0.5);
    }
  }
}

.flex-2 {
  flex: 2;
}
</style>

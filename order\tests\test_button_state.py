#!/usr/bin/env python3
"""
登录按钮状态测试脚本

专门测试登录按钮的启用/禁用状态管理
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QVBoxLayout, QWidget, QPushButton, QLabel
from PySide6.QtCore import QTimer

# 设置日志
logging.basicConfig(level=logging.INFO)

def test_button_state():
    """测试登录按钮状态"""
    try:
        # 创建应用
        app = QApplication(sys.argv)
        
        # 导入必要的模块
        from vnpy.event import EventEngine
        from ui.login.login_window import LoginWindow
        
        # 创建事件引擎
        event_engine = EventEngine()
        
        # 创建登录窗口
        login_window = LoginWindow(event_engine)

        # 显示窗口（非模态）
        login_window.show()
        
        # 创建测试控制窗口
        test_widget = QWidget()
        test_widget.setWindowTitle("按钮状态测试")
        test_widget.resize(400, 400)
        
        layout = QVBoxLayout(test_widget)
        
        info_label = QLabel("登录按钮状态测试\n观察登录按钮的启用/禁用状态")
        layout.addWidget(info_label)
        
        # 状态显示标签
        status_label = QLabel("按钮状态: 未知")
        layout.addWidget(status_label)
        
        def update_status():
            enabled = login_window.login_button.isEnabled()
            text = login_window.login_button.text()
            status_label.setText(f"按钮状态: {'启用' if enabled else '禁用'} | 文本: {text}")
        
        # 定时更新状态显示
        status_timer = QTimer()
        status_timer.timeout.connect(update_status)
        status_timer.start(500)  # 每500ms更新一次
        
        # 测试按钮
        test_buttons = []
        
        # 1. Tab切换测试
        tab_switch_btn = QPushButton("切换到手机登录Tab")
        def switch_tab():
            current = login_window.login_widget.tab_widget.currentIndex()
            new_index = 1 if current == 0 else 0
            login_window.login_widget.tab_widget.setCurrentIndex(new_index)
            tab_name = "密码登录" if new_index == 0 else "手机登录"
            tab_switch_btn.setText(f"切换到{'手机登录' if new_index == 0 else '密码登录'}Tab")
            print(f"✅ 切换到{tab_name}Tab")
            update_status()
        
        tab_switch_btn.clicked.connect(switch_tab)
        layout.addWidget(tab_switch_btn)
        
        # 2. 输入测试
        input_test_btn = QPushButton("输入测试数据")
        def input_test():
            if login_window.login_widget.tab_widget.currentIndex() == 0:
                # 密码登录
                login_window.login_widget.username_edit.setText("test")
                login_window.login_widget.password_edit.setText("123456")
                login_window.login_widget.captcha_edit.setText("1234")
            else:
                # 手机登录
                login_window.login_widget.phone_edit.setText("18678863949")
                login_window.login_widget.code_edit.setText("123456")
            print("✅ 测试数据已输入")
            update_status()
        
        input_test_btn.clicked.connect(input_test)
        layout.addWidget(input_test_btn)
        
        # 3. 清空输入测试
        clear_btn = QPushButton("清空输入")
        def clear_input():
            login_window.login_widget.username_edit.clear()
            login_window.login_widget.password_edit.clear()
            login_window.login_widget.captcha_edit.clear()
            login_window.login_widget.phone_edit.clear()
            login_window.login_widget.code_edit.clear()
            print("✅ 输入已清空")
            update_status()
        
        clear_btn.clicked.connect(clear_input)
        layout.addWidget(clear_btn)
        
        # 4. 模拟登录失败
        login_fail_btn = QPushButton("模拟登录（应该失败）")
        def test_login_fail():
            try:
                # 输入无效数据
                login_window.login_widget.tab_widget.setCurrentIndex(0)
                login_window.login_widget.username_edit.setText("invalid_user")
                login_window.login_widget.password_edit.setText("invalid_pass")
                login_window.login_widget.captcha_edit.setText("1234")
                
                # 点击登录
                login_window.on_login_clicked()
                print("✅ 登录请求已发送（应该失败）")
                update_status()
            except Exception as e:
                print(f"❌ 登录测试异常: {str(e)}")
        
        login_fail_btn.clicked.connect(test_login_fail)
        layout.addWidget(login_fail_btn)
        
        # 5. 手动重置按钮状态
        reset_btn = QPushButton("手动重置按钮状态")
        def reset_button():
            login_window.set_login_state(False)
            print("✅ 按钮状态已重置")
            update_status()
        
        reset_btn.clicked.connect(reset_button)
        layout.addWidget(reset_btn)
        
        # 6. 检查按钮状态
        check_btn = QPushButton("检查当前状态")
        def check_status():
            enabled = login_window.login_button.isEnabled()
            text = login_window.login_button.text()
            widget_enabled = login_window.login_widget.isEnabled()
            print(f"📊 按钮启用: {enabled}")
            print(f"📊 按钮文本: {text}")
            print(f"📊 表单启用: {widget_enabled}")
            update_status()
        
        check_btn.clicked.connect(check_status)
        layout.addWidget(check_btn)
        
        # 显示测试窗口
        test_widget.show()
        
        # 初始状态更新
        update_status()
        
        # 设置窗口关闭时退出应用
        def on_window_closed():
            status_timer.stop()
            app.quit()
        
        login_window.finished.connect(on_window_closed)
        test_widget.closeEvent = lambda event: on_window_closed()
        
        print("=" * 50)
        print("登录按钮状态测试启动")
        print("=" * 50)
        print("1. 观察右侧状态显示")
        print("2. 点击各种测试按钮")
        print("3. 观察登录按钮的状态变化")
        print("4. 测试Tab切换是否影响按钮状态")
        print("5. 测试登录失败后按钮是否恢复")
        print("=" * 50)
        
        # 运行应用
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_button_state()

import { http } from '@/http/http'
import type {
  ICreateContractRequest,
  IUpdateContractRequest,
  IContractListRequest,
  IContractListResponse,
  IContractAsSetterRequest,
  IContractAsPricerRequest,
  IContractDetailParams,
  IContractResponse,
  ICreateTradeRequest,
  ITradeStatusRequest,
  ITradeListRequest,
  ITradeListResponse,
  ITrade,
  IPriceCalculationResponse,
  IContractCancelRecord,
  IContractStatusInfo,
  ICancelContractRequest
} from '@/types/contract'

// ============ 新版API - 符合文档要求的路径 ============

/**
 * 创建合同 - 新版API
 */
export function createContract(data: ICreateContractRequest): Promise<IResData<any>> {
  return http.post('/dianjia/contract', data)
}

/**
 * 更新合同 - 新版API
 */
export function updateContract(data: IUpdateContractRequest): Promise<IResData<any>> {
  return http.put(`/dianjia/contract/${data.id}`, data)
}

/**
 * 删除合同 - 新版API
 */
export function deleteContract(contractId: number): Promise<IResData<any>> {
  return http.delete(`/dianjia/contract/${contractId}`)
}

/**
 * 获取被点价方合同列表 - 新版API
 */
export function getContractsAsSetter(params?: IContractAsSetterRequest): Promise<IResData<IContractListResponse>> {
  return http.get<IContractListResponse>('/dianjia/contracts/as-setter', params)
}

/**
 * 获取点价方合同列表 - 新版API
 */
export function getContractsAsPricer(params?: IContractAsPricerRequest): Promise<IResData<IContractListResponse>> {
  return http.get<IContractListResponse>('/dianjia/contracts/as-pricer', params)
}

/**
 * 获取合同详情 - 新版API
 */
export function getContractDetail(contractId: number): Promise<IResData<IContractResponse>> {
  return http.get<IContractResponse>(`/dianjia/contract/${contractId}`)
}




/**
 * 获取合同取消记录 - 新版API
 */
export function getContractCancelRecords(contractId: number): Promise<IResData<IContractCancelRecord[]>> {
  return http.get<IContractCancelRecord[]>(`/dianjia/contract/${contractId}/cancel-records`)
}



// ============ 交易相关API ============

/**
 * 创建交易
 */
export function createTrade(data: ICreateTradeRequest): Promise<IResData<any>> {
  return http.post('/trade/create', data)
}

/**
 * 获取交易列表
 */
export function getTradeList(params: ITradeListRequest): Promise<IResData<ITradeListResponse>> {
  return http.get<ITradeListResponse>('/trade/list', params)
}

/**
 * 获取交易详情
 */
export function getTradeDetail(tradeId: number): Promise<IResData<ITrade>> {
  return http.get<ITrade>(`/trade/detail/${tradeId}`)
}

/**
 * 更新交易状态
 */
export function updateTradeStatus(data: ITradeStatusRequest): Promise<IResData<any>> {
  return http.put('/trade/status', data)
}

/**
 * 获取当前指数价格
 */
export function getCurrentIndexPrice(instrumentId: number): Promise<IResData<number>> {
  return http.get<number>('/trade/index-price', { instrumentId })
}

/**
 * 计算交易价格
 */
export function calculateTradePrice(contractId: number): Promise<IResData<IPriceCalculationResponse>> {
  return http.get<IPriceCalculationResponse>('/trade/calculate-price', { contractId })
}

// ============ 快捷操作 ============

/**
 * 审批交易（被点价方专用）
 */
export function approveTrade(tradeId: number): Promise<IResData<any>> {
  return updateTradeStatus({
    id: tradeId,
    status: 'Approved'
  })
}

/**
 * 拒绝交易（被点价方专用）
 */
export function rejectTrade(tradeId: number): Promise<IResData<any>> {
  return updateTradeStatus({
    id: tradeId,
    status: 'Rejected'
  })
}

/**
 * 取消交易
 */
export function cancelTrade(tradeId: number): Promise<IResData<any>> {
  return updateTradeStatus({
    id: tradeId,
    status: 'Cancelled'
  })
}


// ============ V3 新增：合同状态管理API ============

/**
 * 激活合同 (V3 新增)
 * 将合同状态从 Unexecuted 变更为 Executing
 */
export function activateContract(contractId: number): Promise<IResData<any>> {
  return http.post(`/dianjia/contract/${contractId}/activate`)
}

/**
 * 挂起合同 (V3 新增)
 * 将合同状态从 Executing 变更为 Unexecuted
 */
export function deactivateContract(contractId: number): Promise<IResData<any>> {
  return http.post(`/dianjia/contract/${contractId}/deactivate`)
}

/**
 * 取消合同 (V3 新增)
 * 部分或全部取消合同，只有 Unexecuted 状态的合同可以取消
 */
export function cancelContract(contractId: number, data: ICancelContractRequest): Promise<IResData<any>> {
  return http.post(`/dianjia/contract/${contractId}/cancel`, data)
}
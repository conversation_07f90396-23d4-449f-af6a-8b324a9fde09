<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "用户选择器测试",
    "navigationStyle": "default"
  }
}
</route>

<script lang="ts" setup>
import { ref } from 'vue'
import type { IUserSelectable } from '@/types'
import UserSelector from '@/components/UserSelector.vue'
import { toast } from '@/utils/toast'

// 状态管理
const selectedUserId = ref<number>(0)
const selectedUserInfo = ref<IUserSelectable | null>(null)

// 方法
function handleUserChange(user: IUserSelectable | null) {
  selectedUserInfo.value = user
  if (user) {
    toast.success(`选择了用户：${user.nickName}`)
  } else {
    toast.info('已清除用户选择')
  }
}

function resetSelection() {
  selectedUserId.value = 0
  selectedUserInfo.value = null
}

function simulateSetUser() {
  // 模拟设置一个用户ID（假设用户ID为1）
  selectedUserId.value = 1
}
</script>

<template>
  <view class="test-page p-4">
    <view class="page-header mb-6">
      <text class="page-title text-xl font-bold text-gray-800">
        用户选择器测试页面
      </text>
    </view>

    <view class="test-section mb-8">
      <view class="section-title mb-4">
        <text class="text-lg font-semibold text-gray-700">基础使用</text>
      </view>
      
      <UserSelector
        v-model="selectedUserId"
        label="选择用户"
        placeholder="请选择一个用户"
        @change="handleUserChange"
      />
    </view>

    <view class="info-section mb-6">
      <view class="section-title mb-4">
        <text class="text-lg font-semibold text-gray-700">选择结果</text>
      </view>
      
      <view class="info-card bg-white p-4 rounded-lg shadow-sm">
        <view class="info-item mb-2">
          <text class="label font-medium text-gray-600">用户ID: </text>
          <text class="value text-gray-800">{{ selectedUserId || '未选择' }}</text>
        </view>
        <view class="info-item mb-2">
          <text class="label font-medium text-gray-600">用户昵称: </text>
          <text class="value text-gray-800">{{ selectedUserInfo?.nickName || '未选择' }}</text>
        </view>
        <view class="info-item">
          <text class="label font-medium text-gray-600">用户手机: </text>
          <text class="value text-gray-800">{{ selectedUserInfo?.phone || '未选择' }}</text>
        </view>
      </view>
    </view>

    <view class="action-section">
      <view class="section-title mb-4">
        <text class="text-lg font-semibold text-gray-700">测试操作</text>
      </view>
      
      <view class="action-buttons flex gap-4">
        <wd-button type="info" size="small" @click="resetSelection">
          重置选择
        </wd-button>
        <wd-button type="primary" size="small" @click="simulateSetUser">
          模拟设置用户
        </wd-button>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.test-page {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.page-title {
  color: #303133;
  margin-bottom: 20rpx;
}

.section-title {
  color: #606266;
  border-left: 4rpx solid #667eea;
  padding-left: 16rpx;
}

.info-card {
  border-left: 4rpx solid #67c23a;
}

.info-item {
  display: flex;
  align-items: center;
  
  .label {
    min-width: 120rpx;
  }
  
  .value {
    flex: 1;
  }
}

.action-buttons {
  flex-wrap: wrap;
  gap: 20rpx;
}
</style>
<template>
  <view class="quote-card bg-white rounded-lg shadow-sm p-3">
    <view v-if="tickData" class="space-y-2">
      <!-- 标题行：合约名称、交易所、时间 -->
      <view class="flex justify-between items-center">
        <view class="flex items-center space-x-2">
          <text class="text-sm font-medium text-gray-800">{{ tickData.symbol }}</text>
          <text class="text-xs text-gray-500">{{ tickData.exchange }}</text>
        </view>
        <text class="text-xs text-gray-400">{{ formatTime(tickData.datetime) }}</text>
      </view>
      
      <!-- 主价格行：最新价、涨跌比率/数量、卖一买一 -->
      <view class="flex items-stretch justify-between">
        <view class="flex items-center space-x-3">
          <text
            class="text-4xl font-bold cursor-pointer leading-tight"
            :class="priceChangeClass"
            @click="onPriceClick('last', tickData.last_price)"
          >
            {{ formatPrice(tickData.last_price) }}
          </text>
          <view class="flex flex-col justify-center">
            <text class="text-xs" :class="priceChangeClass">
              {{ priceChangePercent >= 0 ? '+' : '' }}{{ priceChangePercent.toFixed(2) }}%
            </text>
            <text class="text-xs text-gray-500">
              {{ tickData.last_volume }}
            </text>
          </view>
        </view>
        <view class="text-right flex flex-col justify-center space-y-1">
          <view class="flex items-center space-x-1">
            <text class="text-xs text-gray-500">卖一</text>
            <text
              class="text-sm font-medium text-red-600 cursor-pointer"
              @click="onPriceClick('ask', tickData.ask_price_1)"
            >
              {{ formatPrice(tickData.ask_price_1) }}
            </text>
            <text class="text-xs text-gray-500">{{ tickData.ask_volume_1 }}</text>
          </view>
          <view class="flex items-center space-x-1">
            <text class="text-xs text-gray-500">买一</text>
            <text
              class="text-sm font-medium text-green-600 cursor-pointer"
              @click="onPriceClick('bid', tickData.bid_price_1)"
            >
              {{ formatPrice(tickData.bid_price_1) }}
            </text>
            <text class="text-xs text-gray-500">{{ tickData.bid_volume_1 }}</text>
          </view>
        </view>
      </view>
      

      
      <!-- 涨跌停行 -->
      <view class="flex justify-between items-center">
        <view class="flex items-center space-x-1">
          <text class="text-xs text-gray-500">涨停</text>
          <text 
            class="text-sm font-medium text-red-500 cursor-pointer" 
            @click="onPriceClick('limit_up', tickData.limit_up)"
          >
            {{ formatPrice(tickData.limit_up) }}
          </text>
        </view>
        <view class="flex items-center space-x-1">
          <text class="text-xs text-gray-500">跌停</text>
          <text 
            class="text-sm font-medium text-green-500 cursor-pointer" 
            @click="onPriceClick('limit_down', tickData.limit_down)"
          >
            {{ formatPrice(tickData.limit_down) }}
          </text>
        </view>
      </view>
    </view>
    
    <!-- 无数据状态 -->
    <view v-else class="flex items-center justify-center py-6">
      <view class="text-center">
        <wd-icon name="chart-line" size="24px" custom-class="text-gray-300 mb-1"></wd-icon>
        <text class="text-xs text-gray-400">正在获取行情数据...</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'

// Props 定义
interface Props {
  instrumentId?: string  // 合约ID，如 IF2508
  symbol?: string       // 合约代码，如 i2510
  exchange?: string     // 交易所，如 DCE
}

const props = withDefaults(defineProps<Props>(), {
  instrumentId: '',
  symbol: '',
  exchange: 'DCE'
})

// Emits 定义
interface PriceClickEvent {
  type: 'last' | 'bid' | 'ask' | 'limit_up' | 'limit_down'
  price: string
  numericPrice: number
}

const emit = defineEmits<{
  priceClick: [event: PriceClickEvent]
}>()

// 行情数据结构
interface ITickData {
  gateway_name: string
  symbol: string
  exchange: string
  datetime: string
  last_price: string
  last_volume: string
  limit_up: string
  limit_down: string
  bid_price_1: string
  ask_price_1: string
  bid_volume_1: string
  ask_volume_1: string
  vt_symbol: string
}

// 响应式数据
const tickData = ref<ITickData | null>(null)
const previousPrice = ref<number>(0)
let priceInterval: ReturnType<typeof setInterval> | null = null

// 计算属性
const currentPrice = computed(() => {
  return tickData.value ? parseFloat(tickData.value.last_price) : 0
})

const priceChange = computed(() => {
  if (!tickData.value || !previousPrice.value) return 0
  return currentPrice.value - previousPrice.value
})

const priceChangePercent = computed(() => {
  if (!tickData.value || !previousPrice.value || previousPrice.value === 0) return 0
  return ((currentPrice.value - previousPrice.value) / previousPrice.value * 100)
})

const priceChangeClass = computed(() => {
  if (priceChange.value > 0) return 'text-red-500'
  if (priceChange.value < 0) return 'text-green-500'
  return 'text-gray-700'
})

// 方法
const formatTime = (datetime: string) => {
  const date = new Date(datetime)
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit', 
    second: '2-digit' 
  })
}

const formatPrice = (price: string | number) => {
  return typeof price === 'string' ? parseFloat(price).toFixed(1) : price.toFixed(1)
}

const onPriceClick = (type: PriceClickEvent['type'], price: string) => {
  const numericPrice = parseFloat(price)
  emit('priceClick', { type, price, numericPrice })
}

// 模拟实时行情数据更新
const startPriceUpdates = () => {
  if (priceInterval) clearInterval(priceInterval)
  
  // 根据合约ID确定基础价格
  const getBasePrice = (instrumentId: string) => {
    if (instrumentId.startsWith('IF')) return 4200  // 股指期货
    if (instrumentId.startsWith('i')) return 792.5  // 铁矿石
    if (instrumentId.startsWith('rb')) return 3800  // 螺纹钢
    return 792.5  // 默认价格
  }
  
  const basePrice = getBasePrice(props.instrumentId || props.symbol)
  const currentTime = new Date()
  
  // 设置初始行情数据
  tickData.value = {
    gateway_name: "CTP",
    symbol: props.instrumentId || props.symbol || "i2510",
    exchange: props.exchange,
    datetime: currentTime.toISOString(),
    last_price: basePrice.toString(),
    last_volume: "0",
    limit_up: (basePrice * 1.1).toFixed(1),
    limit_down: (basePrice * 0.9).toFixed(1),
    bid_price_1: (basePrice - 0.5).toString(),
    ask_price_1: (basePrice + 0.5).toString(),
    bid_volume_1: "270",
    ask_volume_1: "316",
    vt_symbol: `${props.instrumentId || props.symbol || "i2510"}.${props.exchange}`
  }
  previousPrice.value = basePrice
  
  // 定时更新行情数据
  priceInterval = setInterval(() => {
    if (!tickData.value) return
    
    const currentPrice = parseFloat(tickData.value.last_price)
    previousPrice.value = currentPrice
    
    // 模拟价格变动
    const change = (Math.random() - 0.5) * 5
    const newPrice = Math.max(0.1, currentPrice + change)
    const volume = Math.floor(Math.random() * 100)
    
    // 更新行情数据
    tickData.value = {
      ...tickData.value,
      datetime: new Date().toISOString(),
      last_price: newPrice.toFixed(1),
      last_volume: volume.toString(),
      bid_price_1: (newPrice - 0.5).toFixed(1),
      ask_price_1: (newPrice + 0.5).toFixed(1),
      bid_volume_1: (Math.floor(Math.random() * 500) + 100).toString(),
      ask_volume_1: (Math.floor(Math.random() * 500) + 100).toString()
    }
  }, 2000)
}

// 监听 props 变化，重新初始化数据
watch(() => [props.instrumentId, props.symbol], () => {
  startPriceUpdates()
}, { immediate: false })

// 生命周期
onMounted(() => {
  startPriceUpdates()
})

onUnmounted(() => {
  if (priceInterval) clearInterval(priceInterval)
})
</script>

<style lang="scss" scoped>
.quote-card {
  .cursor-pointer {
    cursor: pointer;
    transition: opacity 0.2s;
    
    &:hover {
      opacity: 0.8;
    }
    
    &:active {
      opacity: 0.6;
    }
  }
}

// 响应式调整
@media (max-width: 375px) {
  .quote-card {
    .text-2xl {
      font-size: 1.75rem;
    }
  }
}
</style>

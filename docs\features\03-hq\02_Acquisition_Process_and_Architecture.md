# 子功能：数据采集流程与架构

## 1. 功能设计 (Functional Design)

### 1.1 订阅机制

- **自动化订阅**: 系统将在每个交易日开盘前，通过 `vn.py` 自动订阅全市场所有期货合约的行情数据。
- **数据接口**: 连接到期货公司的行情服务器（CTP 等）来接收实时数据流。

### 1.2 数据落库逻辑

1.  **Tick 数据处理**:
    - **实时双写**: 接收到的每一条 Tick 数据将同时写入 `TDengine` 和 `Redis`。
      - **TDengine**: 用于永久性存储，支持复杂的历史数据分析。
      - **Redis**: 仅保留最新的 Tick 信息，键名可设计为 `TICK:{合约代码}`，方便前端快速读取和展示。

2.  **K线合成与存储**:
    - **分钟线 (1-min Bar)**: `vn.py` 内置的 `BarGenerator` 模块将根据 Tick 数据实时合成1分钟K线。每分钟结束时，生成的K线数据将被存入 `TDengine`。
    - **日线 (Daily Bar)**: 在每日收盘后，系统将根据分钟线或交易所结算数据生成日线数据，并存入 `TDengine`。

### 1.3 高可用设计

- **备用行情源**: 为了确保数据的稳定性和及时性，可以设计备选方案。例如，同时连接到多家期货公司提供的行情服务器，当主服务器延迟或中断时，系统可以自动切换到备用服务器，确保数据流不中断。

## 2. 接口定义 (API Definition)

本功能模块主要为后台数据服务，不直接对外提供 HTTP API。其主要“接口”是向下游服务提供数据：

| 功能描述 | 接口形式 | 目标服务 |
|---|---|---|
| 提供历史行情数据 | 数据库查询 | TDengine |
| 提供最新实时行情 | 缓存查询 | Redis |

## 3. 相关页面 (Related Pages)

本功能为纯后端服务，不涉及前端页面。

## 4. 测试用例 (Test Cases)

| 用例ID | 场景描述 | 测试步骤 | 预期结果 |
|---|---|---|---|
| TC-ACQ-001 | 交易日开盘行情订阅 | 1. 到达开盘时间 | 系统自动订阅所有合约，日志中无报错 |
| TC-ACQ-002 | Tick数据双写验证 | 1. 接收一条Tick数据 | 该Tick数据被正确写入 TDengine 和 Redis |
| TC-ACQ-003 | 分钟线合成验证 | 1. 模拟一分钟内的Tick流 | 一分钟结束后，数据库中生成一条正确的1分钟K线 |
| TC-ACQ-004 | 日线生成验证 | 1. 到达收盘时间 | 数据库中为每个合约生成一条正确的日线数据 |
| TC-ACQ-005 | 行情源断线重连 | 1. 手动断开主行情源 | 系统能自动切换到备用行情源，或在主行情源恢复后自动重连 |

## 5. 注意事项 (Notes/Caveats)

- **数据一致性**: 需要有监控和校验机制，确保合成的K线数据与交易所发布的最终数据一致。
- **存储压力**: 随着时间推移，Tick数据量会非常庞大，需要定期评估 `TDengine` 的存储和性能，并制定归档或清理策略。

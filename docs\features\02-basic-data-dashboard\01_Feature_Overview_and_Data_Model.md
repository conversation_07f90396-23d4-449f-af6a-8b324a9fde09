# 功能模块：基础数据看板

## 1. 功能模块简介 (Summary)

本功能旨在为期现贸易用户提供一个直观、可定制的基础数据看板。看板将围绕不同期货品种，集中展示和分析关键的时序市场数据（如价格、持仓量等），通过丰富的可视化图表，帮助用户快速把握市场动态、发现交易机会。

## 2. 数据定义 (Data Definition)

### 2.1 数据来源

本看板所需数据由 **[功能03 - 行情数据采集](../03-data-acquisition/01_Feature_Overview_and_Data_Model.md)** 提供。

### 2.2 存储方案

- **时序数据库 (TDengine)**: 存储所有期货品种的 Tick、分钟线和日线历史数据，作为本功能分析和可视化的主要数据源。
- **实时缓存 (Redis)**: 存储最新的 Tick 数据，用于支持前端的实时数据更新。

### 2.3 核心数据模型

此功能主要依赖于行情数据，其核心数据结构（以数据库表为例）如下：

**表名: `market_data_daily` (日线数据)**

| 字段名 | 类型 | 约束 | 描述 |
|---|---|---|---|
| `ts` | `TIMESTAMP` | Primary Key | 时间戳 |
| `symbol` | `VARCHAR(32)` | Not Null | 合约代码，例如 `FUT.RB2410` |
| `trading_date` | `DATE` | Not Null | 交易日 |
| `open` | `DOUBLE` | | 开盘价 |
| `high` | `DOUBLE` | | 最高价 |
| `low` | `DOUBLE` | | 最低价 |
| `close` | `DOUBLE` | | 收盘价 |
| `volume` | `BIGINT` | | 成交量 |
| `open_interest` | `BIGINT` | | 持仓量 |

*注：分钟线和Tick数据表结构类似，仅时间戳精度和部分字段有差异。*

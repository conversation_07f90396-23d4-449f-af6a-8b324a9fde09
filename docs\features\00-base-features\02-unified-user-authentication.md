# 统一身份认证方案 (Unified User Authentication)

**版本:** 1.0
**日期:** 2025年7月21日
**作者:** Gemini

---

## 1. 概述

随着业务发展，用户需要通过多种渠道（如微信、手机号、用户名/密码）登录我们的应用。为了避免数据冗余和用户身份混淆，我们需要一个统一的身份认证方案。

本文档旨在提出一个基于现有 `sys_users` 表的统一用户身份管理方案，确保无论用户通过何种方式登录，都能被准确识别为同一个主体，并能无缝复用现有的后台管理能力。

## 2. 核心原则

1.  **单一用户主体 (Single Source of Truth)**: 每一个真实的用户，在 `sys_users` 表中都只对应 **唯一一条记录**。
2.  **以内部ID为核心**: 用户的核心标识是 `sys_users` 表的 `ID` (主键) 和 `UUID` (业务唯一标识)，所有业务数据都应与此关联。
3.  **多种登录凭证**: 用户名/密码、手机号、微信OpenID/UnionID等，都仅仅是用于验证用户身份的 **“凭证”** 或 **“钥匙”**，它们共同指向同一个用户主体。
4.  **渐进式信息完善**: 允许用户以最低门槛（如微信一键登录）进入应用，然后在需要时（如交易、安全设置）引导用户绑定手机号等更高级别的身份信息。

## 3. 数据库表结构变更

为了支持微信登录，我们需要在现有的 `sys_users` 表中增加微信相关的字段。

**目标文件**: `admin/server/model/system/sys_user.go`
**目标表名**: `sys_users`

### 3.1. 新增字段

在 `SysUser` 结构体中添加以下两个字段：

```go
type SysUser struct {
    global.GVA_MODEL
    UUID          uuid.UUID      `json:"uuid" gorm:"index;comment:用户UUID"`
    Username      string         `json:"userName" gorm:"index;comment:用户登录名"`
    Password      string         `json:"-"  gorm:"comment:用户登录密码"`
    NickName      string         `json:"nickName" gorm:"default:系统用户;comment:用户昵称"`
    HeaderImg     string         `json:"headerImg" gorm:"default:https://qmplusimg.henrongyi.top/gva_header.jpg;comment:用户头像"`
    AuthorityId   uint           `json:"authorityId" gorm:"default:888;comment:用户角色ID"`
    Phone         string         `json:"phone"  gorm:"index;comment:用户手机号"` // 建议为手机号也增加索引
    Email         string         `json:"email"  gorm:"comment:用户邮箱"`
    Enable        int            `json:"enable" gorm:"default:1;comment:用户是否被冻结 1正常 2冻结"`
    
    // --- 新增字段 ---
    WechatOpenID  string         `json:"wechatOpenId" gorm:"index;comment:微信OpenID"` // 微信在当前应用下的唯一标识
    WechatUnionID string         `json:"wechatUnionId" gorm:"index;comment:微信UnionID"` // 微信在同一开发者账号下的唯一标识

    // ... 其他字段保持不变
    Authority     SysAuthority   `json:"authority" gorm:"foreignKey:AuthorityId;references:AuthorityId;comment:用户角色"`
    Authorities   []SysAuthority `json:"authorities" gorm:"many2many:sys_user_authority;"`
    OriginSetting common.JSONMap `json:"originSetting" form:"originSetting" gorm:"type:text;default:null;column:origin_setting;comment:配置;"`
}
```

### 3.2. 字段说明

-   `WechatOpenID`: 微信用户在单个应用（例如我们的小程序或H5应用）中的唯一标识。
-   `WechatUnionID`: 如果我们的业务涉及多个应用（如公众号、小程序、网站应用），`UnionID` 是识别同一用户的关键。**强烈建议使用 `UnionID` 作为微信用户的主要识别依据。**
-   **索引**: 为 `WechatOpenID` 和 `WechatUnionID` 添加数据库索引，以加快查询速度。同时建议为 `Phone` 字段也添加索引。

## 4. 核心业务流程设计

### 4.1. 首次微信登录

1.  **前端操作**: 用户在 `app` 端点击“微信登录”，调用 `uni.login` 获取 `code`，并可能通过 `uni.getUserProfile` 获取用户信息（昵称、头像）。
2.  **后端接口**: 前端将 `code` 发送到后端新的登录接口，例如 `/user/loginByWechat`。
3.  **后端处理**:
    a. 后端通过 `code` 调用微信服务器接口，换取用户的 `SessionKey`, `OpenID`, 和 `UnionID`。
    b. **查询用户**: 使用 `UnionID` 在 `sys_users` 表中查询：`WHERE wechat_union_id = '用户的UnionID'`。
    c. **用户不存在 (首次登录)**:
        i. 在 `sys_users` 表中创建一条新记录。
        ii. 填充 `UUID`, `WechatOpenID`, `WechatUnionID`。
        iii. 将从微信获取的昵称和头像填充到 `NickName` 和 `HeaderImg` 字段。
        iv. `Username` 可以自动生成一个唯一的字符串（如 `wx_` + `UUID` 的一部分），或者暂时留空（取决于业务逻辑）。`Password` 留空。
        v. 分配一个默认的 `AuthorityId` (角色ID)，例如“普通用户”。
        vi. 生成JWT令牌并返回给前端，完成登录。
    d. **用户已存在**:
        i. 直接基于该用户记录生成JWT令牌并返回，完成登录。
        ii. 可选：更新用户的 `NickName` 和 `HeaderImg` 为微信最新的信息。

### 4.2. 手机号登录/注册

此流程基本保持不变，但需要注意：
-   用户通过手机号+验证码登录时，查询 `sys_users` 表中 `Phone` 字段匹配的记录。
-   如果记录不存在，则创建新用户。

### 4.3. 账户绑定与合并

这是统一身份的关键，解决了用户使用不同方式登录后如何关联的问题。

#### 场景A: 已登录用户绑定手机号

-   **流程**: 用户通过微信登录后，在“账户与安全”页面点击“绑定手机”。
-   **操作**: 用户输入手机号和验证码。后端验证通过后，查询该手机号是否已被其他账号占用。
    -   **未被占用**: 将手机号更新到当前用户的 `Phone` 字段。绑定成功。
    -   **已被占用**: 进入下面的“账户合并”流程。

#### 场景B: 账户合并

-   **触发条件**:
    1.  一个用**微信A**登录的用户，尝试绑定一个已经被**账号B**（例如用手机号注册的）使用的手机号。
    2.  一个用**手机号A**登录的用户，尝试绑定一个已经被**账号C**（例如用微信登录的）使用的微信号。
-   **处理逻辑**:
    1.  向用户发出明确提示：“该手机号/微信号已被另一账号注册。是否要将两个账号合并？合并后，您将可以使用任一方式登录同一账户。”
    2.  用户确认合并后，以后端为主，将一个账号的信息合并到另一个，然后禁用或删除被合并的账号。
    3.  **推荐策略**: 保留创建时间更早的账号记录，将被合并账号的凭证（如 `WechatUnionID`）更新到主账号记录中。业务数据（如订单）需要根据业务规则进行迁移。

## 5. 对现有功能的影响

-   **后台用户管理**: `admin/web` 的用户管理界面需要能展示和搜索新增的 `WechatOpenID` 和 `WechatUnionID` 字段。
-   **用户创建**: 后台管理员手动创建用户时，这些新字段为非必填。
-   **权限系统**: 无直接影响。用户的权限由 `AuthorityId` 决定，与登录方式无关。

## 6. 实施步骤建议

1.  **数据库迁移**:
    -   在 `sys_user.go` 中添加新字段。
    -   启动服务，GORM的 `AutoMigrate` 功能会自动将新字段添加到 `sys_users` 表中。
2.  **后端接口开发**:
    -   创建 `loginByWechat` 接口。
    -   创建 `bindPhoneNumber` 接口。
    -   修改现有的 `loginByPhone` 或 `loginByUsername` 接口，确保其逻辑兼容。
3.  **前端功能开发**:
    -   在 `app` 端添加入口，调用微信登录。
    -   在用户中心添加入口，用于绑定手机号或微信号。
4.  **测试**:
    -   充分测试所有场景：首次微信登录、已存在用户微信登录、手机号注册、绑定、账户合并等。
5.  **文档更新**:
    -   更新API文档（Swagger）。

通过此方案，我们可以构建一个健壮、可扩展的用户系统，为未来的业务发展打下坚实的基础。
